{"timestamp": "2025-07-21T06:36:56.894Z", "summary": {"totalChecks": 6, "successCount": 5, "criticalFailures": 1}, "results": {"compilation": {"success": false, "message": "编译失败，发现 707 个错误", "details": {"errorCount": 707, "sample": ["src/api/routes/trading-execution/routes/order-position-routes.ts(323,9): error TS2698: Spread types may only be created from object types.", "src/application/RefactoredCrossSystemStateSyncManager.ts(39,11): error TS2739: Type '{ nodeId: string; maxVersionHistory: number; conflictResolutionStrategy: \"merge\"; cleanupInterval: number; maxClockSize: number; }' is missing the following properties from type 'VersionManagerConfig': enableCompression, enableChecksumValidation, retentionPeriod", "src/application/websocket-event-server.ts(258,9): error TS2554: Expected 2-3 arguments, but got 5.", "src/config/index.ts(6,1): error TS2308: Module './environment' has already exported a member named 'getDatabaseUrl'. Consider explicitly re-exporting to resolve the ambiguity.", "src/contexts/ai-reasoning/infrastructure/reasoning/continuous-learning-engine.ts(295,11): error TS2561: Object literal may only specify known properties, but 'symbols' does not exist in type '(Without<DecisionOutcomesCreateInput, DecisionOutcomesUncheckedCreateInput> & DecisionOutcomesUncheckedCreateInput) | (Without<...> & DecisionOutcomesCreateInput)'. Did you mean to write 'Symbols'?", "src/contexts/ai-reasoning/infrastructure/reasoning/unified-decision-engine.ts(807,13): error TS2740: Type '{}' is missing the following properties from type 'BinanceTickerResponse': symbol, priceChange, priceChangePercent, weightedAvgPrice, and 15 more.", "src/contexts/ai-reasoning/infrastructure/services/learning-knowledge-base.ts(412,11): error TS2561: Object literal may only specify known properties, but 'symbols' does not exist in type 'ShortCyclePredictionsWhereInput'. Did you mean to write 'Symbols'?", "src/contexts/ai-reasoning/infrastructure/services/learning-knowledge-base.ts(416,20): error TS2561: Object literal may only specify known properties, but 'symbols' does not exist in type 'ShortCyclePredictionsInclude<DefaultArgs>'. Did you mean to write 'Symbols'?", "src/contexts/ai-reasoning/infrastructure/services/timeframe-learning-coordinator.ts(718,21): error TS2345: Argument of type '{ type: string; description: string; confidence: number; applicability: number; recommendation: string; timeframe: TimeframeType; marketCondition: string; }' is not assignable to parameter of type 'CrossTimeframeInsight'.", "  Type '{ type: string; description: string; confidence: number; applicability: number; recommendation: string; timeframe: TimeframeType; marketCondition: string; }' is missing the following properties from type 'CrossTimeframeInsight': sourceTimeframe, targetTimeframe, insight, applicabilityScore, and 3 more."]}, "critical": true}, "dependencies": {"success": true, "message": "依赖包完整"}, "database-schema": {"success": true, "message": "Prisma schema有效"}, "env-config": {"success": true, "message": "环境配置完整"}, "core-services": {"success": true, "message": "核心服务文件存在"}, "api-routes": {"success": true, "message": "API路由文件存在"}}}