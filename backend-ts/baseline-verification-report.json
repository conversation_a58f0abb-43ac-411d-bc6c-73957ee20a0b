{"timestamp": "2025-07-21T06:10:16.446Z", "summary": {"totalChecks": 6, "successCount": 4, "criticalFailures": 2}, "results": {"compilation": {"success": false, "message": "编译失败，发现 728 个错误", "details": {"errorCount": 728, "sample": ["src/api/routes/trading-execution/routes/order-position-routes.ts(323,9): error TS2698: Spread types may only be created from object types.", "src/application/RefactoredCrossSystemStateSyncManager.ts(39,11): error TS2739: Type '{ nodeId: string; maxVersionHistory: number; conflictResolutionStrategy: \"merge\"; cleanupInterval: number; maxClockSize: number; }' is missing the following properties from type 'VersionManagerConfig': enableCompression, enableChecksumValidation, retentionPeriod", "src/application/websocket-event-server.ts(258,9): error TS2554: Expected 2-3 arguments, but got 5.", "src/application/websocket-event-server.ts(291,29): error TS2339: Property 'removeSubscriber' does not exist on type 'DataChangeEventBroadcaster'.", "src/config/index.ts(6,1): error TS2308: Module './environment' has already exported a member named 'getDatabaseUrl'. Consider explicitly re-exporting to resolve the ambiguity.", "src/contexts/ai-reasoning/infrastructure/knowledge/financial-knowledge-graph.ts(259,11): error TS2561: Object literal may only specify known properties, but 'knowledgeEntities' does not exist in type 'KnowledgeSearchIndicesInclude<DefaultArgs>'. Did you mean to write 'KnowledgeEntities'?", "src/contexts/ai-reasoning/infrastructure/knowledge/financial-knowledge-graph.ts(268,20): error TS2339: Property 'knowledgeEntities' does not exist on type '{ id: string; weight: Decimal; domain: string; content: string; entityId: string; keywords: string; }'.", "src/contexts/ai-reasoning/infrastructure/knowledge/financial-knowledge-graph.ts(269,23): error TS2339: Property 'knowledgeEntities' does not exist on type '{ id: string; weight: Decimal; domain: string; content: string; entityId: string; keywords: string; }'.", "src/contexts/ai-reasoning/infrastructure/knowledge/financial-knowledge-graph.ts(273,35): error TS2339: Property 'knowledgeEntities' does not exist on type '{ id: string; weight: Decimal; domain: string; content: string; entityId: string; keywords: string; }'.", "src/contexts/ai-reasoning/infrastructure/knowledge/financial-knowledge-graph.ts(274,24): error TS2339: Property 'knowledgeEntities' does not exist on type '{ id: string; weight: Decimal; domain: string; content: string; entityId: string; keywords: string; }'."]}, "critical": true}, "dependencies": {"success": true, "message": "依赖包完整"}, "database-schema": {"success": true, "message": "Prisma schema有效"}, "env-config": {"success": false, "message": "缺少必需的环境变量: DATABASE_URL, JWT_SECRET, NODE_ENV", "critical": true}, "core-services": {"success": true, "message": "核心服务文件存在"}, "api-routes": {"success": true, "message": "API路由文件存在"}}}