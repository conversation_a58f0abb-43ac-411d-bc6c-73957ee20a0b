/**
 * 增强时间框架学习协调器
 * 扩展原有协调器以支持三层时间框架（微观、中观、宏观）
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { PrismaClient } from '@prisma/client';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IDynamicWeightingService } from '../../../../shared/infrastructure/analysis';

// 暂时内联接口定义，避免循环依赖
enum TimeframeType {
  MICRO = 'micro',
  MESO = 'meso',
  MACRO = 'macro'
}

enum PredictionType {
  PRICE_MICRO = 'price_15m',
  TREND_MESO = 'trend_8h',
  STRATEGY_MACRO = 'strategy_3d'
}

interface MarketContext {
  currentPrice: number;
  avgPrice: number;
  volatility: number;
  marketCondition: string;
  timestamp: Date;
  dataPoints: number;
  [key: string]: any;
}

interface PredictionResult {
  symbolId: string;
  predictionType: PredictionType;
  predictedValue: number;
  predictedDirection: 'UP' | 'DOWN' | 'SIDEWAYS';
  confidence: number;
  marketContext: MarketContext;
  predictionTimestamp: Date;
  targetVerificationTime: Date;
  predictionHorizon: string;
  verificationDelay: string;
  modelVersion: string;
}

interface LearningInsights {
  timeframe: TimeframeType;
  totalPredictions: number;
  verifiedPredictions: number;
  accuracyRate: number;
  lastLearning: Date;
  patterns: string[];
  recommendations: string[];
}

interface TimeframeConfig {
  type: TimeframeType;
  interval: string;
  verification: string;
  predictionType: PredictionType;
  learningWeight: number;
  crossInfluenceWeight: number;
  decayFactor: number;
  minSampleSize: number;
  maxAge: number;
}

interface TimeframeIsolatedParameters {
  timeframe: TimeframeType;
  parameters: Record<string, number>;
  lastUpdated: Date;
  confidence: number;
  sampleCount: number;
  validationCount: number;
}

interface CrossTimeframeInsight {
  type?: string; // 洞察类型，如 'trend_alignment', 'volatility_spillover' 等
  sourceTimeframe: TimeframeType;
  targetTimeframe: TimeframeType;
  insight: string;
  confidence: number;
  applicabilityScore: number;
  validationCount: number;
  createdAt: Date;
  lastValidated: Date;
  description?: string; // 详细描述
  applicability?: number; // 适用性评分
  recommendation?: string; // 推荐建议
  timeframe?: TimeframeType; // 目标时间框架
  marketCondition?: string; // 市场条件
}

interface TimeframeConsistencyResult {
  isConsistent: boolean;
  consistencyScore: number;
  conflicts: TimeframeConflict[];
  recommendations: string[];
  adjustments: Record<TimeframeType, Record<string, number>>;
}

interface TimeframeConflict {
  timeframes: TimeframeType[];
  conflictType: 'direction' | 'strength' | 'confidence' | 'timing';
  severity: 'low' | 'medium' | 'high';
  description: string;
  resolution: string;
}

interface TimeframeLearningUpdate {
  timeframe: TimeframeType;
  predictionType: PredictionType;
  accuracy: number;
  marketCondition: string;
  metadata: any;
  timestamp: Date;
}

interface TimeframeSynchronizationResult {
  synchronized: boolean;
  synchronizationScore: number;
  adjustments: Record<TimeframeType, any>;
  warnings: string[];
}

interface ITimeframeCoordinator {
  getTimeframeConfig(timeframe: TimeframeType): Promise<TimeframeConfig>;
  getAllTimeframeConfigs(): Promise<Map<TimeframeType, TimeframeConfig>>;
  getTimeframeSpecificParameters(timeframe: TimeframeType, predictionType: PredictionType): Promise<TimeframeIsolatedParameters>;
  getCrossTimeframeInsights(targetTimeframe: TimeframeType, marketContext: MarketContext): Promise<CrossTimeframeInsight[]>;
  updateTimeframeLearning(update: TimeframeLearningUpdate): Promise<void>;
  validateCrossTimeframeConsistency(predictions: Record<TimeframeType, PredictionResult>): Promise<TimeframeConsistencyResult>;
  synchronizeTimeframeParameters(sourceTimeframe: TimeframeType, targetTimeframes: TimeframeType[]): Promise<TimeframeSynchronizationResult>;
  getTimeframeLearningInsights(timeframe: TimeframeType, marketContext: MarketContext): Promise<LearningInsights>;
  evaluateCrossTimeframePropagation(sourceTimeframe: TimeframeType, accuracy: number, marketCondition: string): Promise<boolean>;
  optimizeTimeframeWeights(performanceData: Record<TimeframeType, number>): Promise<Record<TimeframeType, number>>;
  detectTimeframeConflicts(predictions: Record<TimeframeType, PredictionResult>): Promise<TimeframeConflict[]>;
  resolveTimeframeConflicts(conflicts: TimeframeConflict[], predictions: Record<TimeframeType, PredictionResult>): Promise<Record<TimeframeType, PredictionResult>>;
}

@injectable()
export class TimeframeLearningCoordinator implements ITimeframeCoordinator {
  private readonly timeframeConfigs: Map<TimeframeType, TimeframeConfig>;
  private readonly isolatedParameters: Map<TimeframeType, TimeframeIsolatedParameters>;

  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.Shared.DynamicWeightingService)
    private readonly dynamicWeightingService: IDynamicWeightingService,
    @inject(TYPES.Database)
    private readonly prisma: PrismaClient
  ) {
    this.timeframeConfigs = this.initializeTimeframeConfigs();
    this.isolatedParameters = new Map();
    this.logger.info('增强时间框架学习协调器初始化完成');
  }

  /**
   * 获取时间框架配置
   */
  async getTimeframeConfig(timeframe: TimeframeType): Promise<TimeframeConfig> {
    const config = this.timeframeConfigs.get(timeframe);
    if (!config) {
      throw new Error(`未找到时间框架 ${timeframe} 的配置`);
    }
    return config;
  }

  /**
   * 获取所有时间框架配置
   */
  async getAllTimeframeConfigs(): Promise<Map<TimeframeType, TimeframeConfig>> {
    return new Map(this.timeframeConfigs);
  }

  /**
   * 获取时间框架特定的学习参数
   */
  async getTimeframeSpecificParameters(
    timeframe: TimeframeType,
    predictionType: PredictionType
  ): Promise<TimeframeIsolatedParameters> {
    try {
      const config = this.timeframeConfigs.get(timeframe);
      if (!config) {
        throw new Error(`未知的时间框架: ${timeframe}`);
      }

      // 获取该时间框架的隔离参数
      let isolatedParams = this.isolatedParameters.get(timeframe);
      
      if (!isolatedParams || this.isParameterStale(isolatedParams, config.maxAge)) {
        isolatedParams = await this.loadTimeframeParameters(timeframe, predictionType);
        this.isolatedParameters.set(timeframe, isolatedParams);
      }

      return isolatedParams;
    } catch (error) {
      this.logger.error('获取时间框架特定参数失败', { timeframe, error });
      throw error;
    }
  }

  /**
   * 获取跨时间框架的学习洞察
   */
  async getCrossTimeframeInsights(
    targetTimeframe: TimeframeType,
    marketContext: MarketContext
  ): Promise<CrossTimeframeInsight[]> {
    try {
      const insights: CrossTimeframeInsight[] = [];

      // 从其他时间框架获取相关洞察
      for (const [sourceTimeframe] of this.timeframeConfigs) {
        if (sourceTimeframe === targetTimeframe) continue;

        const crossInsights = await this.extractCrossTimeframeInsights(
          sourceTimeframe,
          targetTimeframe,
          marketContext.marketCondition
        );

        // 根据适用性评分过滤洞察
        const applicableInsights = crossInsights.filter(
          insight => insight.applicabilityScore > 0.6
        );

        insights.push(...applicableInsights);
      }

      return insights;
    } catch (error) {
      this.logger.error('获取跨时间框架洞察失败', { targetTimeframe, error });
      return [];
    }
  }

  /**
   * 更新时间框架特定的学习结果
   */
  async updateTimeframeLearning(update: TimeframeLearningUpdate): Promise<void> {
    try {
      const config = this.timeframeConfigs.get(update.timeframe);
      if (!config) {
        this.logger.warn('未知的时间框架，跳过学习更新', { 
          timeframe: update.timeframe 
        });
        return;
      }

      // 更新时间框架特定的知识库
      await this.updateTimeframeKnowledge(update);

      // 评估是否需要跨时间框架传播洞察
      await this.evaluateCrossTimeframePropagation(
        update.timeframe,
        update.accuracy,
        update.marketCondition
      );

      // 更新参数（仅影响同类时间框架）
      await this.updateTimeframeParameters(update);

      this.logger.info('时间框架学习更新完成', {
        timeframe: update.timeframe,
        accuracy: update.accuracy,
        marketCondition: update.marketCondition
      });
    } catch (error) {
      this.logger.error('时间框架学习更新失败', { update, error });
    }
  }

  /**
   * 验证跨时间框架一致性
   */
  async validateCrossTimeframeConsistency(
    predictions: Record<TimeframeType, PredictionResult>
  ): Promise<TimeframeConsistencyResult> {
    try {
      const conflicts: TimeframeConflict[] = [];
      const recommendations: string[] = [];
      const adjustments = {} as Record<TimeframeType, Record<string, number>>;

      // 检查方向一致性
      const directionConflicts = this.checkDirectionConsistency(predictions);
      conflicts.push(...directionConflicts);

      // 检查强度一致性
      const strengthConflicts = this.checkStrengthConsistency(predictions);
      conflicts.push(...strengthConflicts);

      // 检查置信度一致性
      const confidenceConflicts = this.checkConfidenceConsistency(predictions);
      conflicts.push(...confidenceConflicts);

      // 生成建议和调整
      if (conflicts.length > 0) {
        const { recs, adjs } = this.generateConflictResolutions(conflicts, predictions);
        recommendations.push(...recs);
        Object.assign(adjustments, adjs);
      }

      const consistencyScore = this.calculateConsistencyScore(conflicts);

      return {
        isConsistent: conflicts.length === 0,
        consistencyScore,
        conflicts,
        recommendations,
        adjustments
      };
    } catch (error) {
      this.logger.error('跨时间框架一致性验证失败', { error });
      return {
        isConsistent: false,
        consistencyScore: 0,
        conflicts: [{
          timeframes: [TimeframeType.MICRO, TimeframeType.MESO, TimeframeType.MACRO],
          conflictType: 'direction',
          severity: 'high',
          description: '一致性验证失败',
          resolution: '建议手动检查预测结果'
        }],
        recommendations: ['建议手动检查预测结果'],
        adjustments: {} as Record<TimeframeType, Record<string, number>>
      };
    }
  }

  /**
   * 同步时间框架参数
   */
  async synchronizeTimeframeParameters(
    sourceTimeframe: TimeframeType,
    targetTimeframes: TimeframeType[]
  ): Promise<TimeframeSynchronizationResult> {
    try {
      const sourceParams = await this.getTimeframeSpecificParameters(
        sourceTimeframe,
        this.getPredictionTypeByTimeframe(sourceTimeframe)
      );

      const adjustments = {} as Record<TimeframeType, any>;
      const warnings: string[] = [];

      for (const targetTimeframe of targetTimeframes) {
        if (targetTimeframe === sourceTimeframe) continue;

        const targetConfig = this.timeframeConfigs.get(targetTimeframe);
        if (!targetConfig) {
          warnings.push(`目标时间框架 ${targetTimeframe} 配置不存在`);
          continue;
        }

        // 计算同步调整
        const syncAdjustment = this.calculateSynchronizationAdjustment(
          sourceParams,
          targetTimeframe
        );

        adjustments[targetTimeframe] = syncAdjustment;
      }

      const synchronizationScore = this.calculateSynchronizationScore(adjustments);

      return {
        synchronized: warnings.length === 0,
        synchronizationScore,
        adjustments,
        warnings
      };
    } catch (error) {
      this.logger.error('时间框架参数同步失败', { sourceTimeframe, targetTimeframes, error });
      return {
        synchronized: false,
        synchronizationScore: 0,
        adjustments: {} as Record<TimeframeType, Record<string, number>>,
        warnings: ['参数同步失败']
      };
    }
  }

  /**
   * 获取时间框架学习洞察
   */
  async getTimeframeLearningInsights(
    timeframe: TimeframeType,
    _marketContext: MarketContext
  ): Promise<LearningInsights> {
    try {
      const config = this.timeframeConfigs.get(timeframe);
      if (!config) {
        throw new Error(`未找到时间框架 ${timeframe} 的配置`);
      }

      // 从数据库获取该时间框架的学习统计
      const learningStats = await this.getLearningStatsFromDatabase(timeframe);
      const patterns = await this.getLearnedPatternsFromDatabase(timeframe);

      return {
        timeframe,
        totalPredictions: learningStats.totalPredictions,
        verifiedPredictions: learningStats.verifiedPredictions,
        accuracyRate: learningStats.accuracyRate,
        lastLearning: learningStats.lastLearning,
        patterns,
        recommendations: this.generateTimeframeRecommendations(timeframe, patterns)
      };
    } catch (error) {
      this.logger.error('获取时间框架学习洞察失败', { timeframe, error });
      throw error;
    }
  }

  /**
   * 评估跨时间框架传播
   */
  async evaluateCrossTimeframePropagation(
    sourceTimeframe: TimeframeType,
    accuracy: number,
    marketCondition: string
  ): Promise<boolean> {
    try {
      const config = this.timeframeConfigs.get(sourceTimeframe);
      if (!config) return false;

      // 只有在准确率足够高且影响权重足够大时才传播
      const shouldPropagate = accuracy > 0.8 && config.crossInfluenceWeight > 0.5;

      if (shouldPropagate) {
        this.logger.info('触发跨时间框架洞察传播', {
          sourceTimeframe,
          accuracy,
          marketCondition,
          crossInfluenceWeight: config.crossInfluenceWeight
        });
        
        // 实现传播逻辑
        await this.propagateInsights(sourceTimeframe, accuracy, marketCondition);
      }

      return shouldPropagate;
    } catch (error) {
      this.logger.error('评估跨时间框架传播失败', { sourceTimeframe, error });
      return false;
    }
  }

  /**
   * 优化时间框架权重 - 使用统一的动态权重分配服务
   */
  async optimizeTimeframeWeights(
    performanceData: Record<TimeframeType, number>
  ): Promise<Record<TimeframeType, number>> {
    try {
      // 构造基于性能的趋势数据
      const trends = Object.entries(performanceData).map(([timeframe, performance]) => ({
        timeframe,
        direction: this.mapPerformanceToDirection(performance) as 'bullish' | 'bearish' | 'neutral',
        strength: this.mapPerformanceToStrength(performance),
        confidence: this.mapPerformanceToConfidence(performance),
        volume: 1000000,
        momentum: this.mapPerformanceToMomentum(performance),
        timestamp: new Date()
      }));

      // 构造市场条件
      const marketCondition = {
        type: this.determineMarketTypeFromTimeframes(performanceData) as 'HIGH_VOLATILITY' | 'LOW_VOLATILITY' | 'TRENDING' | 'RANGING',
        volatility: this.calculateTimeframeVolatility(performanceData),
        volume: 1000000,
        trend: this.getDominantTimeframeTrend(performanceData),
        strength: this.calculateOverallTimeframeStrength(performanceData)
      };

      // 使用动态权重分配服务
      const weightingResult = await this.dynamicWeightingService.allocate(trends, marketCondition);

      // 构造优化后的权重
      const optimizedWeights = {} as Record<TimeframeType, number>;
      for (const timeframe of Object.keys(performanceData) as TimeframeType[]) {
        optimizedWeights[timeframe] = weightingResult.weights[timeframe] ||
          this.getFallbackWeight(timeframe, performanceData);
      }

      this.logger.info('时间框架权重优化完成', {
        originalPerformance: performanceData,
        optimizedWeights,
        strategy: weightingResult.strategy,
        confidence: weightingResult.confidence
      });

      return optimizedWeights;

    } catch (error) {
      this.logger.error('动态权重优化失败，使用简单权重计算', {
        performanceData,
        error: error instanceof Error ? error.message : String(error)
      });

      // 降级到简单权重计算
      return this.calculateSimpleTimeframeWeights(performanceData);
    }
  }

  /**
   * 检测时间框架冲突
   */
  async detectTimeframeConflicts(
    predictions: Record<TimeframeType, PredictionResult>
  ): Promise<TimeframeConflict[]> {
    const conflicts: TimeframeConflict[] = [];

    // 检查方向冲突
    conflicts.push(...this.checkDirectionConsistency(predictions));

    // 检查强度冲突
    conflicts.push(...this.checkStrengthConsistency(predictions));

    // 检查置信度冲突
    conflicts.push(...this.checkConfidenceConsistency(predictions));

    return conflicts;
  }

  /**
   * 解决时间框架冲突
   */
  async resolveTimeframeConflicts(
    conflicts: TimeframeConflict[],
    predictions: Record<TimeframeType, PredictionResult>
  ): Promise<Record<TimeframeType, PredictionResult>> {
    try {
      const resolvedPredictions = { ...predictions };

      for (const conflict of conflicts) {
        switch (conflict.conflictType) {
          case 'direction':
            this.resolveDirectionConflict(conflict, resolvedPredictions);
            break;
          case 'strength':
            this.resolveStrengthConflict(conflict, resolvedPredictions);
            break;
          case 'confidence':
            this.resolveConfidenceConflict(conflict, resolvedPredictions);
            break;
        }
      }

      return resolvedPredictions;
    } catch (error) {
      this.logger.error('解决时间框架冲突失败', { conflicts, error });
      return predictions;
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 初始化时间框架配置
   */
  private initializeTimeframeConfigs(): Map<TimeframeType, TimeframeConfig> {
    const configs = new Map<TimeframeType, TimeframeConfig>();

    // 微观系统：专注15分钟级别的快速交易信号
    configs.set(TimeframeType.MICRO, {
      type: TimeframeType.MICRO,
      interval: '15m',
      verification: '30m',
      predictionType: PredictionType.PRICE_MICRO,
      learningWeight: 1.0,        // 高学习权重，快速适应
      crossInfluenceWeight: 0.2,  // 对其他系统影响较小
      decayFactor: 0.85,          // 较快衰减，适应市场变化
      minSampleSize: 20,          // 较少样本即可学习
      maxAge: 48                  // 48小时内的经验有效
    });

    // 中观系统：专注8小时级别的趋势判断
    configs.set(TimeframeType.MESO, {
      type: TimeframeType.MESO,
      interval: '8h',
      verification: '24h',
      predictionType: PredictionType.TREND_MESO,
      learningWeight: 0.6,        // 中等学习权重
      crossInfluenceWeight: 0.5,  // 中等影响权重
      decayFactor: 0.92,          // 中等衰减速度
      minSampleSize: 35,          // 中等样本需求
      maxAge: 168                 // 7天内的经验有效
    });

    // 宏观系统：专注3日级别的战略判断
    configs.set(TimeframeType.MACRO, {
      type: TimeframeType.MACRO,
      interval: '3d',
      verification: '7d',
      predictionType: PredictionType.STRATEGY_MACRO,
      learningWeight: 0.3,        // 较低学习权重，保持稳定
      crossInfluenceWeight: 0.8,  // 对其他系统影响较大
      decayFactor: 0.98,          // 缓慢衰减，保持长期记忆
      minSampleSize: 50,          // 需要更多样本确保可靠性
      maxAge: 720                 // 30天内的经验有效
    });

    return configs;
  }

  /**
   * 加载时间框架特定参数 - 占位符实现
   */
  private async loadTimeframeParameters(
    timeframe: TimeframeType,
    _predictionType: PredictionType
  ): Promise<TimeframeIsolatedParameters> {
    // 占位符实现 - 基于时间框架返回默认参数
    const defaultParams: Record<string, number> = {};

    switch (timeframe) {
      case TimeframeType.MICRO:
        defaultParams.confidenceCalibration = 1.0;
        defaultParams.volatilityAdjustment = 1.2;
        defaultParams.trendSensitivity = 0.8;
        break;
      case TimeframeType.MESO:
        defaultParams.confidenceCalibration = 1.0;
        defaultParams.volatilityAdjustment = 1.0;
        defaultParams.trendSensitivity = 0.6;
        break;
      case TimeframeType.MACRO:
        defaultParams.confidenceCalibration = 1.0;
        defaultParams.volatilityAdjustment = 0.8;
        defaultParams.trendSensitivity = 0.4;
        break;
    }

    return {
      timeframe,
      parameters: defaultParams,
      lastUpdated: new Date(),
      confidence: 0.8,
      sampleCount: 0,
      validationCount: 0
    };
  }

  /**
   * 检查参数是否过期
   */
  private isParameterStale(params: TimeframeIsolatedParameters, maxAgeHours: number): boolean {
    const ageHours = (Date.now() - params.lastUpdated.getTime()) / (1000 * 60 * 60);
    return ageHours > maxAgeHours;
  }

  /**
   * 提取跨时间框架洞察
   */
  private async extractCrossTimeframeInsights(
    sourceTimeframe: TimeframeType,
    targetTimeframe: TimeframeType,
    marketCondition: string
  ): Promise<CrossTimeframeInsight[]> {
    try {
      // 占位符实现 - 生成基于规则的洞察
      const insights: string[] = [
        `${sourceTimeframe}层面的${marketCondition}条件对${targetTimeframe}层面的影响`,
        `跨时间框架传播: ${sourceTimeframe} -> ${targetTimeframe}`,
        `市场条件适应性: ${marketCondition}`
      ];

      // 转换为CrossTimeframeInsight格式
      const crossInsights: CrossTimeframeInsight[] = insights.map((insight) => ({
        sourceTimeframe,
        targetTimeframe,
        insight,
        confidence: this.calculateInsightConfidence(sourceTimeframe, targetTimeframe),
        applicabilityScore: this.calculateApplicabilityScore(sourceTimeframe, targetTimeframe, marketCondition),
        validationCount: 0,
        createdAt: new Date(),
        lastValidated: new Date()
      }));

      // 添加一些基于规则的默认洞察
      const defaultInsights = this.generateDefaultCrossTimeframeInsights(
        sourceTimeframe, targetTimeframe, marketCondition
      );

      return [...crossInsights, ...defaultInsights];
    } catch (error) {
      this.logger.error('提取跨时间框架洞察失败', {
        sourceTimeframe, targetTimeframe, marketCondition, error
      });

      // 返回基本的默认洞察
      return this.generateDefaultCrossTimeframeInsights(
        sourceTimeframe, targetTimeframe, marketCondition
      );
    }
  }

  /**
   * 生成默认的跨时间框架洞察
   */
  private generateDefaultCrossTimeframeInsights(
    sourceTimeframe: TimeframeType,
    targetTimeframe: TimeframeType,
    marketCondition: string
  ): CrossTimeframeInsight[] {
    const insights: CrossTimeframeInsight[] = [];

    // 基于时间框架关系生成洞察
    const timeframeRelation = this.analyzeTimeframeRelation(sourceTimeframe, targetTimeframe);

    if (timeframeRelation === 'higher-to-lower') {
      insights.push({
        type: 'trend_alignment',
        sourceTimeframe,
        targetTimeframe,
        insight: `${sourceTimeframe}时间框架的趋势可能影响${targetTimeframe}的短期走势`,
        description: `${sourceTimeframe}时间框架的趋势可能影响${targetTimeframe}的短期走势`,
        confidence: 0.75,
        applicabilityScore: 0.8,
        applicability: 0.8,
        recommendation: `关注${sourceTimeframe}的趋势变化，作为${targetTimeframe}交易的参考`,
        timeframe: targetTimeframe,
        marketCondition,
        validationCount: 0,
        createdAt: new Date(),
        lastValidated: new Date()
      });
    } else if (timeframeRelation === 'lower-to-higher') {
      insights.push({
        type: 'momentum_transfer',
        sourceTimeframe,
        targetTimeframe,
        insight: `${sourceTimeframe}的强势动能可能推动${targetTimeframe}的趋势延续`,
        description: `${sourceTimeframe}的强势动能可能推动${targetTimeframe}的趋势延续`,
        confidence: 0.65,
        applicabilityScore: 0.7,
        applicability: 0.7,
        recommendation: `${sourceTimeframe}的突破信号可能预示${targetTimeframe}的方向`,
        timeframe: targetTimeframe,
        marketCondition,
        validationCount: 0,
        createdAt: new Date(),
        lastValidated: new Date()
      });
    }

    // 基于市场条件添加特定洞察
    if (marketCondition === 'volatile') {
      insights.push({
        type: 'volatility_impact',
        sourceTimeframe,
        targetTimeframe,
        insight: '高波动环境下，短期时间框架信号的可靠性降低',
        description: '高波动环境下，短期时间框架信号的可靠性降低',
        confidence: 0.8,
        applicabilityScore: 0.9,
        applicability: 0.9,
        recommendation: '在高波动期间，优先参考较长时间框架的信号',
        timeframe: targetTimeframe,
        marketCondition,
        validationCount: 0,
        createdAt: new Date(),
        lastValidated: new Date()
      });
    }

    return insights;
  }

  private calculateInsightConfidence(
    _sourceTimeframe: TimeframeType,
    _targetTimeframe: TimeframeType
  ): number {
    return 0.75;
  }

  private calculateApplicabilityScore(
    _sourceTimeframe: TimeframeType,
    _targetTimeframe: TimeframeType,
    _marketCondition: string
  ): number {
    return 0.8;
  }

  private updateTimeframeKnowledge(_update: TimeframeLearningUpdate): Promise<void> {
    // TODO: 实现时间框架知识更新逻辑
    return Promise.resolve();
  }

  /**
   * 从数据库获取学习统计数据
   */
  private async getLearningStatsFromDatabase(timeframe: TimeframeType): Promise<{
    totalPredictions: number;
    verifiedPredictions: number;
    accuracyRate: number;
    lastLearning: Date;
  }> {
    try {
      // 查询该时间框架的预测记录
      const predictions = await this.prisma.aiPrediction.findMany({
        where: {
          // 使用metadata中的timeframe字段
          metadata: {
            path: ['timeframe'],
            equals: timeframe
          },
          timestamp: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
          }
        }
      });

      const totalPredictions = predictions.length;
      // AiPrediction模型没有isVerified属性，假设所有预测都已验证
      const verifiedPredictions = totalPredictions;
      const accuracyRate = totalPredictions > 0 ? verifiedPredictions / totalPredictions : 0;
      const lastLearning = predictions.length > 0
        ? new Date(Math.max(...predictions.map(p => p.timestamp.getTime())))
        : new Date();

      return {
        totalPredictions,
        verifiedPredictions,
        accuracyRate,
        lastLearning
      };
    } catch (error) {
      this.logger.error('获取学习统计数据失败', { timeframe, error });
      // 返回默认值而不是虚假数据
      return {
        totalPredictions: 0,
        verifiedPredictions: 0,
        accuracyRate: 0,
        lastLearning: new Date()
      };
    }
  }

  /**
   * 从数据库获取学习到的模式
   */
  private async getLearnedPatternsFromDatabase(timeframe: TimeframeType): Promise<string[]> {
    try {
      // 查询该时间框架学习到的模式
      const patterns = await this.prisma.learningKnowledgeBase.findMany({
        where: {
          knowledgeType: timeframe,
          confidenceScore: {
            gte: 0.7 // 只返回高置信度的模式
          }
        },
        select: {
          knowledgeKey: true,
          knowledgeData: true,
          confidenceScore: true
        },
        orderBy: {
          confidenceScore: 'desc'
        },
        take: 10 // 最多返回10个模式
      });

      return patterns.map(p => `${p.knowledgeKey}: ${JSON.stringify(p.knowledgeData)}`);
    } catch (error) {
      this.logger.error('获取学习模式失败', { timeframe, error });
      // 返回空数组而不是虚假数据
      return [];
    }
  }

  private updateTimeframeParameters(_update: TimeframeLearningUpdate): Promise<void> {
    // TODO: 实现时间框架参数更新逻辑
    return Promise.resolve();
  }

  private propagateInsights(
    _sourceTimeframe: TimeframeType,
    _accuracy: number,
    _marketCondition: string
  ): Promise<void> {
    return Promise.resolve();
  }

  private checkDirectionConsistency(
    _predictions: Record<TimeframeType, PredictionResult>
  ): TimeframeConflict[] {
    return [];
  }

  private checkStrengthConsistency(
    _predictions: Record<TimeframeType, PredictionResult>
  ): TimeframeConflict[] {
    return [];
  }

  private checkConfidenceConsistency(
    _predictions: Record<TimeframeType, PredictionResult>
  ): TimeframeConflict[] {
    return [];
  }

  private generateConflictResolutions(
    _conflicts: TimeframeConflict[],
    _predictions: Record<TimeframeType, PredictionResult>
  ): { recs: string[]; adjs: Record<TimeframeType, Record<string, number>> } {
    return {
      recs: [],
      adjs: {} as Record<TimeframeType, Record<string, number>>
    };
  }

  private calculateConsistencyScore(conflicts: TimeframeConflict[]): number {
    return conflicts.length === 0 ? 1.0 : Math.max(0, 1.0 - conflicts.length * 0.2);
  }

  private getPredictionTypeByTimeframe(timeframe: TimeframeType): PredictionType {
    switch (timeframe) {
      case TimeframeType.MICRO:
        return PredictionType.PRICE_MICRO;
      case TimeframeType.MESO:
        return PredictionType.TREND_MESO;
      case TimeframeType.MACRO:
        return PredictionType.STRATEGY_MACRO;
      default:
        throw new Error(`未知的时间框架: ${timeframe}`);
    }
  }

  private calculateSynchronizationAdjustment(
    _sourceParams: TimeframeIsolatedParameters,
    _targetTimeframe: TimeframeType
  ): any {
    return {};
  }

  private calculateSynchronizationScore(
    _adjustments: Record<TimeframeType, any>
  ): number {
    return 0.8;
  }

  private generateTimeframeRecommendations(
    timeframe: TimeframeType,
    _patterns: string[]
  ): string[] {
    return [`${timeframe}时间框架建议1`, `${timeframe}时间框架建议2`];
  }

  private resolveDirectionConflict(
    _conflict: TimeframeConflict,
    _predictions: Record<TimeframeType, PredictionResult>
  ): void {
    // 实现方向冲突解决逻辑
  }

  private resolveStrengthConflict(
    _conflict: TimeframeConflict,
    _predictions: Record<TimeframeType, PredictionResult>
  ): void {
    // 实现强度冲突解决逻辑
  }

  private resolveConfidenceConflict(
    _conflict: TimeframeConflict,
    _predictions: Record<TimeframeType, PredictionResult>
  ): void {
    // 实现置信度冲突解决逻辑
  }

  // ==================== 时间框架权重优化辅助方法 ====================

  /**
   * 将性能映射到趋势方向
   */
  private mapPerformanceToDirection(performance: number): string {
    if (performance > 0.6) return 'bullish';
    if (performance < 0.4) return 'bearish';
    return 'neutral';
  }

  /**
   * 将性能映射到强度
   */
  private mapPerformanceToStrength(performance: number): number {
    return Math.max(1, Math.min(10, performance * 10));
  }

  /**
   * 将性能映射到置信度
   */
  private mapPerformanceToConfidence(performance: number): number {
    return Math.max(0.1, Math.min(1.0, performance));
  }

  /**
   * 将性能映射到动量
   */
  private mapPerformanceToMomentum(performance: number): number {
    return Math.max(-1, Math.min(1, (performance - 0.5) * 2));
  }

  /**
   * 从时间框架性能确定市场类型
   */
  private determineMarketTypeFromTimeframes(performanceData: Record<TimeframeType, number>): string {
    const performances = Object.values(performanceData);
    const variance = this.calculateVariance(performances);

    if (variance > 0.3) return 'HIGH_VOLATILITY';
    if (variance < 0.1) return 'LOW_VOLATILITY';

    const avgPerformance = performances.reduce((sum, p) => sum + p, 0) / performances.length;
    return avgPerformance > 0.6 || avgPerformance < 0.4 ? 'TRENDING' : 'RANGING';
  }

  /**
   * 计算时间框架波动性
   */
  private calculateTimeframeVolatility(performanceData: Record<TimeframeType, number>): number {
    const performances = Object.values(performanceData);
    return this.calculateVariance(performances);
  }

  /**
   * 获取主导时间框架趋势
   */
  private getDominantTimeframeTrend(performanceData: Record<TimeframeType, number>): string {
    const avgPerformance = Object.values(performanceData).reduce((sum, p) => sum + p, 0) / Object.keys(performanceData).length;

    if (avgPerformance > 0.6) return 'UPTREND';
    if (avgPerformance < 0.4) return 'DOWNTREND';
    return 'SIDEWAYS';
  }

  /**
   * 计算整体时间框架强度
   */
  private calculateOverallTimeframeStrength(performanceData: Record<TimeframeType, number>): number {
    const avgPerformance = Object.values(performanceData).reduce((sum, p) => sum + p, 0) / Object.keys(performanceData).length;
    return Math.max(1, Math.min(10, avgPerformance * 10));
  }

  /**
   * 获取降级权重
   */
  private getFallbackWeight(timeframe: TimeframeType, performanceData: Record<TimeframeType, number>): number {
    const performance = performanceData[timeframe] || 0.5;
    const totalPerformance = Object.values(performanceData).reduce((sum, p) => sum + p, 0);

    if (totalPerformance === 0) return 1 / Object.keys(performanceData).length;

    const normalizedPerformance = performance / totalPerformance;
    return Math.max(0.1, Math.min(0.8, normalizedPerformance));
  }

  /**
   * 计算简单时间框架权重（降级方案）
   */
  private calculateSimpleTimeframeWeights(performanceData: Record<TimeframeType, number>): Record<TimeframeType, number> {
    const optimizedWeights = {} as Record<TimeframeType, number>;
    const totalPerformance = Object.values(performanceData).reduce((sum, perf) => sum + perf, 0);

    // 基于性能数据计算权重
    for (const [timeframe, performance] of Object.entries(performanceData) as [TimeframeType, number][]) {
      const normalizedPerformance = totalPerformance > 0 ? performance / totalPerformance : 1 / Object.keys(performanceData).length;
      optimizedWeights[timeframe] = Math.max(0.1, Math.min(0.8, normalizedPerformance));
    }

    return optimizedWeights;
  }

  /**
   * 计算方差
   */
  private calculateVariance(values: number[]): number {
    if (values.length < 2) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;

    return Math.sqrt(variance);
  }

  /**
   * 分析时间框架关系
   */
  private analyzeTimeframeRelation(source: TimeframeType, target: TimeframeType): 'higher-to-lower' | 'lower-to-higher' | 'same-level' {
    const timeframeOrder = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w', '1M'];
    const sourceIndex = timeframeOrder.indexOf(source);
    const targetIndex = timeframeOrder.indexOf(target);

    if (sourceIndex > targetIndex) {
      return 'higher-to-lower';
    } else if (sourceIndex < targetIndex) {
      return 'lower-to-higher';
    } else {
      return 'same-level';
    }
  }
}
