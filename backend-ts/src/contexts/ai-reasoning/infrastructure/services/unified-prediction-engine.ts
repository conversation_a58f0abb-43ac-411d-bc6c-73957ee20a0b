/**
 * 统一预测引擎实现
 * 基于EnhancedShortCyclePredictionEngine扩展，支持三层时间框架预测
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { Logger } from 'winston';

import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { CachedAICall } from '../../../../shared/infrastructure/ai/cached-ai-call-decorator';
import {
  IUnifiedTechnicalIndicatorCalculator
} from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { UNIFIED_TECHNICAL_INDICATOR_TYPES } from '../../../../shared/infrastructure/technical-indicators/types';


// 暂时内联接口定义，避免循环依赖
interface IUnifiedPredictionEngine {
  generateMicroPrediction(context: any, config?: any): Promise<any>;
  generateMesoPrediction(context: any, config?: any): Promise<any>;
  generateMacroPrediction(context: any, config?: any): Promise<any>;
  generateComprehensivePrediction(context: any): Promise<any>;
  validatePrediction(prediction: any, context: any): Promise<any>;
  getPredictionMetadata(timeframe: any, context: any): Promise<any>;
  getPredictionConfig(timeframe: any): Promise<any>;
  updatePredictionConfig(timeframe: any, config: any): Promise<void>;
}

interface PredictionConfig {
  timeframe: any;
  predictionType: any;
  confidenceThreshold: number;
  volatilityAdjustment: number;
  trendSensitivity: number;
  marketConditionWeight: number;
}

interface PredictionMetadata {
  dataQuality: number;
  marketVolatility: number;
  trendStrength: number;
  seasonalFactors: string[];
  externalFactors: string[];
  modelConfidence: number;
}

interface PredictionValidationResult {
  isValid: boolean;
  validationScore: number;
  warnings: string[];
  recommendations: string[];
}

import {
  TimeframeType,
  PredictionType,
  MarketContext,
  MicroPredictionResult,
  MesoPredictionResult,
  MacroPredictionResult,
  ComprehensivePredictionResult
} from '../../domain/services/unified-learning-engine.interface';

// 恢复真实接口导入 - 所有接口都已验证存在
import { IParameterConfigCenter } from '../../domain/services/parameter-config-center.interface';
import { ILearningKnowledgeBase } from '../../domain/services/learning-knowledge-base.interface';
import { ITimeframeCoordinator } from '../../domain/services/timeframe-coordinator.interface';

@injectable()
export class UnifiedPredictionEngine implements IUnifiedPredictionEngine {
  private readonly defaultConfigs: Map<TimeframeType, PredictionConfig>;

  constructor(
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator)
    private readonly technicalCalculator: IUnifiedTechnicalIndicatorCalculator,
    @inject(TYPES.AIReasoning.ParameterConfigCenter)
    private readonly parameterCenter: IParameterConfigCenter,
    @inject(TYPES.AIReasoning.LearningKnowledgeBase)
    private readonly knowledgeBase: ILearningKnowledgeBase,
    @inject(TYPES.AIReasoning.TimeframeCoordinator)
    private readonly timeframeCoordinator: ITimeframeCoordinator
  ) {
    this.defaultConfigs = this.initializeDefaultConfigs();
    this.logger.info('统一预测引擎初始化完成');
  }

  /**
   * 微观预测 (15分钟)
   */
  @CachedAICall('unifiedMicroPrediction', {
    extractMarketContext: (args) => ({
      symbol: args[0]?.currentPrice ? 'BTC' : 'UNKNOWN',
      timeframe: '15m',
      timestamp: new Date(),
      predictionType: 'micro'
    }),
    extractConfidenceScore: (result) => result?.confidence,
    calculateCost: () => 0.008,
    cacheOptions: {
      similarityThreshold: 0.85,
      maxAge: 180000, // 3分钟
      includeMarketContext: true
    }
  })
  async generateMicroPrediction(
    context: MarketContext,
    _config?: Partial<PredictionConfig>
  ): Promise<MicroPredictionResult> {
    try {
      this.logger.debug('生成微观预测', { timeframe: '15m' });
      // 占位符实现 - 获取时间框架参数
      const timeframeParams = {
        parameters: {
          confidenceCalibration: 1.0,
          volatilityAdjustment: 1.2,
          trendSensitivity: 0.8
        }
      };

      // 占位符实现 - 获取知识洞察
      const knowledgeInsights = [
        `微观市场条件: ${context.marketCondition}`,
        '短期波动性分析',
        '快速交易信号识别'
      ];

      // 使用UnifiedLearningServiceManager的短期学习核心预测逻辑
      const adjustedPrediction = await this.generateMicroPredictionCore(
        context,
        timeframeParams.parameters,
        knowledgeInsights
      );

      const result: MicroPredictionResult = {
        symbolId: await this.getSymbolId(context),
        predictionType: PredictionType.PRICE_MICRO,
        predictedValue: adjustedPrediction.value,
        predictedDirection: adjustedPrediction.direction,
        confidence: adjustedPrediction.confidence,
        marketContext: context,
        predictionTimestamp: new Date(),
        targetVerificationTime: new Date(Date.now() + 30 * 60 * 1000), // 30分钟后
        predictionHorizon: '15m',
        verificationDelay: '30m',
        modelVersion: 'unified_v3.0'
      };

      this.logger.info('微观预测生成完成', {
        confidence: result.confidence.toFixed(3),
        direction: result.predictedDirection,
        value: result.predictedValue
      });

      return result;
    } catch (error) {
      this.logger.error('微观预测生成失败', { error });
      throw error;
    }
  }

  /**
   * 中观预测 (8小时)
   */
  @CachedAICall('unifiedMesoPrediction', {
    extractMarketContext: (args) => ({
      symbol: args[0]?.currentPrice ? 'BTC' : 'UNKNOWN',
      timeframe: '8h',
      timestamp: new Date(),
      predictionType: 'meso'
    }),
    extractConfidenceScore: (result) => result?.confidence,
    calculateCost: () => 0.012,
    cacheOptions: {
      similarityThreshold: 0.80,
      maxAge: 300000, // 5分钟
      includeMarketContext: true
    }
  })
  async generateMesoPrediction(
    context: MarketContext,
    config?: Partial<PredictionConfig>
  ): Promise<MesoPredictionResult> {
    try {
      this.logger.debug('生成中观预测', { timeframe: '8h' });
      // 占位符实现 - 获取时间框架参数
      const timeframeParams = {
        parameters: {
          confidenceCalibration: 1.0,
          volatilityAdjustment: 1.0,
          trendSensitivity: 0.6
        }
      };

      // 占位符实现 - 获取跨时间框架洞察
      const crossTimeframeInsights = [
        '微观层面的短期信号影响',
        '宏观层面的长期趋势指导',
        `中观市场条件: ${context.marketCondition}`
      ];

      // 使用中观预测核心逻辑
      const adjustedPrediction = await this.generateMesoPredictionCore(
        context,
        timeframeParams.parameters,
        crossTimeframeInsights
      );

      const result: MesoPredictionResult = {
        symbolId: await this.getSymbolId(context),
        predictionType: PredictionType.TREND_MESO,
        predictedValue: adjustedPrediction.value,
        predictedDirection: adjustedPrediction.direction,
        confidence: adjustedPrediction.confidence,
        marketContext: context,
        predictionTimestamp: new Date(),
        targetVerificationTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后
        predictionHorizon: '8h',
        verificationDelay: '24h',
        modelVersion: 'unified_v3.0'
      };

      this.logger.info('中观预测生成完成', {
        confidence: result.confidence.toFixed(3),
        direction: result.predictedDirection,
        value: result.predictedValue
      });

      return result;
    } catch (error) {
      this.logger.error('中观预测生成失败', { error });
      throw error;
    }
  }

  /**
   * 宏观预测 (3日)
   */
  @CachedAICall('unifiedMacroPrediction', {
    extractMarketContext: (args) => ({
      symbol: args[0]?.currentPrice ? 'BTC' : 'UNKNOWN',
      timeframe: '3d',
      timestamp: new Date(),
      predictionType: 'macro'
    }),
    extractConfidenceScore: (result) => result?.confidence,
    calculateCost: () => 0.020,
    cacheOptions: {
      similarityThreshold: 0.75,
      maxAge: 600000, // 10分钟
      includeMarketContext: true
    }
  })
  async generateMacroPrediction(
    context: MarketContext,
    config?: Partial<PredictionConfig>
  ): Promise<MacroPredictionResult> {
    try {
      this.logger.debug('生成宏观预测', { timeframe: '3d' });
      // 占位符实现 - 获取时间框架参数
      const timeframeParams = {
        parameters: {
          confidenceCalibration: 1.0,
          volatilityAdjustment: 0.8,
          trendSensitivity: 0.4
        }
      };

      // 获取宏观特有的市场洞察
      const macroInsights = await this.getMacroMarketInsights(context);

      // 使用宏观预测核心逻辑
      const adjustedPrediction = await this.generateMacroPredictionCore(
        context,
        timeframeParams.parameters,
        macroInsights
      );

      const result: MacroPredictionResult = {
        symbolId: await this.getSymbolId(context),
        predictionType: PredictionType.STRATEGY_MACRO,
        predictedValue: adjustedPrediction.value,
        predictedDirection: adjustedPrediction.direction,
        confidence: adjustedPrediction.confidence,
        marketContext: context,
        predictionTimestamp: new Date(),
        targetVerificationTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后
        predictionHorizon: '3d',
        verificationDelay: '7d',
        modelVersion: 'unified_v3.0'
      };

      this.logger.info('宏观预测生成完成', {
        confidence: result.confidence.toFixed(3),
        direction: result.predictedDirection,
        value: result.predictedValue
      });

      return result;
    } catch (error) {
      this.logger.error('宏观预测生成失败', { error });
      throw error;
    }
  }

  /**
   * 综合预测 (三层结合)
   */
  async generateComprehensivePrediction(
    context: MarketContext
  ): Promise<ComprehensivePredictionResult> {
    try {
      this.logger.info('生成综合预测', { symbol: context.currentPrice });

      // 并行生成三层预测
      const [microPrediction, mesoPrediction, macroPrediction] = await Promise.all([
        this.generateMicroPrediction(context),
        this.generateMesoPrediction(context),
        this.generateMacroPrediction(context)
      ]);

      // 占位符实现 - 验证跨时间框架一致性
      const consistencyResult = {
        isConsistent: true,
        consistencyScore: 0.85,
        conflicts: [],
        recommendations: []
      };

      // 生成综合建议
      const comprehensiveAdvice = this.generateComprehensiveAdvice(
        microPrediction,
        mesoPrediction,
        macroPrediction,
        consistencyResult
      );

      const result: ComprehensivePredictionResult = {
        micro: microPrediction,
        meso: mesoPrediction,
        macro: macroPrediction,
        综合建议: comprehensiveAdvice
      };

      this.logger.info('综合预测生成完成', {
        microConfidence: microPrediction.confidence.toFixed(3),
        mesoConfidence: mesoPrediction.confidence.toFixed(3),
        macroConfidence: macroPrediction.confidence.toFixed(3),
        overallConfidence: comprehensiveAdvice.confidenceScore
      });

      return result;
    } catch (error) {
      this.logger.error('综合预测生成失败', { error });
      throw error;
    }
  }

  /**
   * 验证预测结果
   */
  async validatePrediction(
    prediction: MicroPredictionResult | MesoPredictionResult | MacroPredictionResult,
    context: MarketContext
  ): Promise<PredictionValidationResult> {
    try {
      const warnings: string[] = [];
      const recommendations: string[] = [];
      let validationScore = 1.0;

      // 置信度验证
      if (prediction.confidence < 0.5) {
        warnings.push('预测置信度较低');
        validationScore -= 0.2;
      }

      // 市场条件验证
      if (context.volatility > 0.05) {
        warnings.push('市场波动性较高，预测风险增加');
        recommendations.push('建议降低仓位或等待市场稳定');
        validationScore -= 0.1;
      }

      // 数据质量验证
      if (context.dataPoints < 100) {
        warnings.push('历史数据点不足');
        validationScore -= 0.15;
      }

      return {
        isValid: validationScore > 0.6,
        validationScore,
        warnings,
        recommendations
      };
    } catch (error) {
      this.logger.error('预测验证失败', { error });
      throw error;
    }
  }

  /**
   * 获取预测元数据
   */
  async getPredictionMetadata(
    timeframe: TimeframeType,
    context: MarketContext
  ): Promise<PredictionMetadata> {
    try {
      // 占位符实现 - 获取时间框架参数
      let modelConfidence = 0.85; // 默认宏观置信度
      if (timeframe === TimeframeType.MICRO) {
        modelConfidence = 0.75;
      } else if (timeframe === TimeframeType.MESO) {
        modelConfidence = 0.80;
      }

      return {
        dataQuality: Math.min(1.0, context.dataPoints / 1000),
        marketVolatility: context.volatility,
        trendStrength: Math.abs(context.currentPrice - context.avgPrice) / context.avgPrice,
        seasonalFactors: this.identifySeasonalFactors(context),
        externalFactors: this.identifyExternalFactors(context),
        modelConfidence
      };
    } catch (error) {
      this.logger.error('获取预测元数据失败', { error });
      throw error;
    }
  }

  /**
   * 获取预测配置
   */
  async getPredictionConfig(timeframe: TimeframeType): Promise<PredictionConfig> {
    const defaultConfig = this.defaultConfigs.get(timeframe);
    if (!defaultConfig) {
      throw new Error(`未找到时间框架 ${timeframe} 的默认配置`);
    }
    return defaultConfig;
  }

  /**
   * 更新预测配置
   */
  async updatePredictionConfig(
    timeframe: TimeframeType,
    config: Partial<PredictionConfig>
  ): Promise<void> {
    try {
      const currentConfig = this.defaultConfigs.get(timeframe);
      if (!currentConfig) {
        throw new Error(`未找到时间框架 ${timeframe} 的配置`);
      }

      const updatedConfig = { ...currentConfig, ...config };
      this.defaultConfigs.set(timeframe, updatedConfig);

      this.logger.info('预测配置已更新', { timeframe, config });
    } catch (error) {
      this.logger.error('更新预测配置失败', { timeframe, config, error });
      throw error;
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 从Binance API获取历史数据
   */
  private async getHistoricalDataFromBinance(symbol: string, limit: number): Promise<any[]> {
    try {
      const response = await fetch(`https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=1h&limit=${limit}`);
      if (!response.ok) {
        throw new Error(`Binance API错误: ${response.status}`);
      }

      const klineData = await response.json();
      return (klineData as any[]).map((k: any) => ({
        timestamp: new Date(k[0]),
        open: parseFloat(k[1]),
        high: parseFloat(k[2]),
        low: parseFloat(k[3]),
        close: parseFloat(k[4]),
        volume: parseFloat(k[5])
      }));
    } catch (error) {
      this.logger.error('获取Binance历史数据失败', { error, symbol, limit });
      return [];
    }
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigs(): Map<TimeframeType, PredictionConfig> {
    const configs = new Map<TimeframeType, PredictionConfig>();

    configs.set(TimeframeType.MICRO, {
      timeframe: TimeframeType.MICRO,
      predictionType: PredictionType.PRICE_MICRO,
      confidenceThreshold: 0.6,
      volatilityAdjustment: 1.2,
      trendSensitivity: 0.8,
      marketConditionWeight: 0.7
    });

    configs.set(TimeframeType.MESO, {
      timeframe: TimeframeType.MESO,
      predictionType: PredictionType.TREND_MESO,
      confidenceThreshold: 0.65,
      volatilityAdjustment: 1.0,
      trendSensitivity: 0.6,
      marketConditionWeight: 0.8
    });

    configs.set(TimeframeType.MACRO, {
      timeframe: TimeframeType.MACRO,
      predictionType: PredictionType.STRATEGY_MACRO,
      confidenceThreshold: 0.7,
      volatilityAdjustment: 0.8,
      trendSensitivity: 0.4,
      marketConditionWeight: 0.9
    });

    return configs;
  }

  /**
   * 合并配置
   */
  private mergeConfig(
    timeframe: TimeframeType,
    customConfig?: Partial<PredictionConfig>
  ): PredictionConfig {
    const defaultConfig = this.defaultConfigs.get(timeframe);
    if (!defaultConfig) {
      throw new Error(`未找到时间框架 ${timeframe} 的默认配置`);
    }

    return customConfig ? { ...defaultConfig, ...customConfig } : defaultConfig;
  }

  /**
   * 获取AI响应 - 禁用虚假数据实现
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async _getAIResponse(prompt: string, domain: string): Promise<any> {
    try {
      this.logger.error('拒绝生成虚假AI响应', { domain, promptLength: prompt.length });
      throw new Error(`拒绝为域 ${domain} 生成虚假AI响应。必须使用真实的LLM服务。`);
    } catch (error) {
      this.logger.error('获取AI响应失败', { domain, error });
      throw error;
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(response: any): any {
    try {
      // 简化的解析逻辑，实际应该更复杂
      return {
        value: response.predictedValue ?? 0,
        direction: response.predictedDirection ?? 'SIDEWAYS',
        confidence: response.confidence ?? 0.5
      };
    } catch (error) {
      this.logger.error('解析AI响应失败', { response, error });
      throw error;
    }
  }

  /**
   * 获取符号ID
   */
  private async getSymbolId(context: MarketContext): Promise<string> {
    // 查找BTC/USDT符号记录
    const symbol = await this.prisma.symbols.findFirst({
      where: { symbol: 'BTC/USDT' }
    });

    if (!symbol) {
      throw new Error('未找到BTC/USDT符号记录');
    }

    return symbol.id;
  }

  /**
   * 根据时间框架获取预测类型
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private _getPredictionTypeByTimeframe(timeframe: TimeframeType): PredictionType {
    switch (timeframe) {
      case TimeframeType.MICRO:
        return PredictionType.PRICE_MICRO;
      case TimeframeType.MESO:
        return PredictionType.TREND_MESO;
      case TimeframeType.MACRO:
        return PredictionType.STRATEGY_MACRO;
      default:
        throw new Error(`未知的时间框架: ${timeframe}`);
    }
  }

  /**
   * 识别季节性因子
   */
  private identifySeasonalFactors(context: MarketContext): string[] {
    const factors: string[] = [];
    const now = new Date();
    const month = now.getMonth() + 1;
    const hour = now.getHours();

    // 月度季节性
    if (month === 12 || month === 1) {
      factors.push('年末效应');
    }
    if (month >= 3 && month <= 5) {
      factors.push('春季反弹');
    }

    // 日内季节性
    if (hour >= 9 && hour <= 11) {
      factors.push('亚洲开盘');
    }
    if (hour >= 14 && hour <= 16) {
      factors.push('欧洲开盘');
    }
    if (hour >= 21 && hour <= 23) {
      factors.push('美国开盘');
    }

    return factors;
  }

  /**
   * 识别外部因子
   */
  private identifyExternalFactors(context: MarketContext): string[] {
    const factors: string[] = [];

    // 基于市场条件识别外部因子
    if (context.marketCondition.includes('highVolatility')) {
      factors.push('市场不确定性增加');
    }
    if (context.marketCondition.includes('trending')) {
      factors.push('趋势性行情');
    }
    if (context.marketCondition.includes('sideways')) {
      factors.push('横盘整理');
    }

    return factors;
  }

  /**
   * 生成综合建议
   */
  private generateComprehensiveAdvice(
    micro: MicroPredictionResult,
    meso: MesoPredictionResult,
    macro: MacroPredictionResult,
    consistencyResult: any
  ): { strategy: string; riskLevel: string; confidenceScore: number } {
    // 计算综合置信度
    const avgConfidence = (micro.confidence + meso.confidence + macro.confidence) / 3;

    // 分析方向一致性
    const directions = [micro.predictedDirection, meso.predictedDirection, macro.predictedDirection];
    const upCount = directions.filter(d => d === 'UP').length;
    const downCount = directions.filter(d => d === 'DOWN').length;

    let strategy = '';
    let riskLevel = 'medium';

    if (upCount >= 2) {
      strategy = '多头策略为主';
      if (upCount === 3) {
        strategy = '强烈看多，建议积极做多';
        riskLevel = avgConfidence > 0.8 ? 'low' : 'medium';
      }
    } else if (downCount >= 2) {
      strategy = '空头策略为主';
      if (downCount === 3) {
        strategy = '强烈看空，建议谨慎做空';
        riskLevel = avgConfidence > 0.8 ? 'low' : 'medium';
      }
    } else {
      strategy = '观望为主，等待明确信号';
      riskLevel = 'high';
    }

    // 考虑一致性结果
    if (!consistencyResult.isConsistent) {
      strategy += '，但存在时间框架冲突，建议谨慎';
      riskLevel = 'high';
    }

    return {
      strategy,
      riskLevel: riskLevel,
      confidenceScore: Math.round(avgConfidence * 100)
    };
  }

  /**
   * 微观预测核心逻辑 - 基于UnifiedLearningServiceManager短期学习
   */
  private async generateMicroPredictionCore(
    context: MarketContext,
    learnedParams: Record<string, number>,
    insights: string[]
  ): Promise<any> {
    const currentPrice = context.currentPrice;
    const avgPrice = context.avgPrice;
    const volatility = context.volatility;

    // 使用学习到的参数进行预测（来自UnifiedLearningServiceManager短期学习逻辑）
    const priceChange = currentPrice - avgPrice;
    const trendFactor = learnedParams.confidenceCalibration || 0.1;
    const volatilityAdjustment = learnedParams.volatilityAdjustment || 1.0;

    // 应用学习到的趋势延续因子和波动率调整
    const adjustedTrendFactor = trendFactor * volatilityAdjustment * (1 - volatility);
    const predictedValue = currentPrice + (priceChange * adjustedTrendFactor);
    const predictedDirection = predictedValue > currentPrice ? 'UP' : 'DOWN';

    // 使用学习到的置信度计算方法
    const baseConfidence = Math.max(0.3, Math.min(0.9, 1 - volatility));
    const confidenceThreshold = learnedParams.trendSensitivity || 0.5;
    const confidence = Math.max(confidenceThreshold, baseConfidence);

    return {
      value: predictedValue,
      direction: predictedDirection,
      confidence
    };
  }

  /**
   * 中观预测核心逻辑 - 基于UnifiedLearningServiceManager长期学习
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private async generateMesoPredictionCore(
    context: MarketContext,
    learnedParams: Record<string, number>,
    insights: any[]
  ): Promise<any> {
    // 获取真实历史价格数据用于趋势分析
    const recentPrices = await this.generateRecentPricesFromContext(context, 30);
    const currentPrice = context.currentPrice;

    // 计算移动平均线 - 使用统一技术指标计算器
    const shortMA = this.technicalCalculator.calculateSMA(recentPrices, 10);
    const longMA = this.technicalCalculator.calculateSMA(recentPrices, 20);

    // 计算趋势强度
    const trendStrength = this.calculateTrendStrength(recentPrices);

    // 预测8小时后的价格（基于趋势分析）
    const trendDirection = shortMA > longMA ? 1 : -1;
    const priceChangePercent = trendStrength * trendDirection * 0.02; // 最大2%变化
    const predictedValue = currentPrice * (1 + priceChangePercent);
    const predictedDirection = predictedValue > currentPrice ? 'UP' : 'DOWN';

    // 计算置信度（基于趋势一致性）
    const trendConsistency = this.calculateTrendConsistency(recentPrices);
    const confidence = Math.max(0.4, Math.min(0.85, trendConsistency));

    return {
      value: predictedValue,
      direction: predictedDirection,
      confidence
    };
  }

  /**
   * 宏观预测核心逻辑 - 新实现
   */
  private async generateMacroPredictionCore(
    context: MarketContext,
    learnedParams: Record<string, number>,
    insights: any[]
  ): Promise<any> {
    const currentPrice = context.currentPrice;
    const avgPrice = context.avgPrice;

    // 宏观层面关注长期趋势和基本面
    const longTermTrend = (currentPrice - avgPrice) / avgPrice;
    const marketMomentum = learnedParams.confidenceCalibration || 0.4;
    const stabilityFactor = learnedParams.volatilityAdjustment || 0.8;

    // 3日预测考虑更多的市场结构因素
    const structuralAdjustment = longTermTrend * marketMomentum * stabilityFactor;
    const predictedValue = currentPrice * (1 + structuralAdjustment);
    const predictedDirection = structuralAdjustment > 0 ? 'UP' : 'DOWN';

    // 宏观置信度通常更保守
    const confidence = Math.max(0.5, Math.min(0.8, marketMomentum * stabilityFactor));

    return {
      value: predictedValue,
      direction: predictedDirection,
      confidence
    };
  }

  /**
   * 计算波动率 - 来自UnifiedLearningServiceManager短期学习
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  private _calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i-1]) / prices[i-1]);
    }

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  // calculateMA方法已移除 - 现在使用统一技术指标计算器

  /**
   * 计算趋势强度 - 来自UnifiedLearningServiceManager长期学习
   */
  private calculateTrendStrength(prices: number[]): number {
    if (prices.length < 3) return 0;

    let upCount = 0;
    let downCount = 0;

    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > prices[i - 1]) {
        upCount++;
      } else if (prices[i] < prices[i - 1]) {
        downCount++;
      }
    }

    const total = upCount + downCount;
    if (total === 0) return 0;

    return Math.abs(upCount - downCount) / total;
  }

  /**
   * 计算趋势一致性 - 来自UnifiedLearningServiceManager长期学习
   */
  private calculateTrendConsistency(prices: number[]): number {
    if (prices.length < 5) return 0.5;

    const segments = 5;
    const segmentSize = Math.floor(prices.length / segments);
    const segmentTrends: number[] = [];

    for (let i = 0; i < segments; i++) {
      const start = i * segmentSize;
      const end = Math.min(start + segmentSize, prices.length);
      const segmentPrices = prices.slice(start, end);

      if (segmentPrices.length >= 2) {
        const trend = segmentPrices[0] > segmentPrices[segmentPrices.length - 1] ? -1 : 1;
        segmentTrends.push(trend);
      }
    }

    if (segmentTrends.length === 0) return 0.5;

    const positiveCount = segmentTrends.filter(t => t > 0).length;
    const negativeCount = segmentTrends.filter(t => t < 0).length;

    return Math.max(positiveCount, negativeCount) / segmentTrends.length;
  }

  /**
   * 从真实历史数据获取最近价格序列
   */
  private async generateRecentPricesFromContext(context: MarketContext, count: number): Promise<number[]> {
    try {
      this.logger.info('获取真实历史价格数据', {
        symbol: context.symbol,
        count
      });

      // 使用Binance API获取真实历史数据
      const historicalData = await this.getHistoricalDataFromBinance(context.symbol, count);

      if (!historicalData || historicalData.length === 0) {
        throw new Error(`无法获取符号 ${context.symbol} 的历史价格数据`);
      }

      // 提取收盘价格序列
      const prices = historicalData.map(data => data.close).reverse(); // 按时间顺序排列

      this.logger.info('成功获取真实历史价格数据', {
        symbol: context.symbol,
        dataPoints: prices.length,
        priceRange: {
          min: Math.min(...prices),
          max: Math.max(...prices),
          latest: prices[prices.length - 1]
        }
      });

      return prices;
    } catch (error) {
      this.logger.error('获取真实历史价格数据失败', {
        symbol: context.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`无法获取符号 ${context.symbol} 的真实历史价格数据，拒绝使用虚假数据进行预测: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取宏观市场洞察
   */
  private async getMacroMarketInsights(context: MarketContext): Promise<any[]> {
    return [
      '长期趋势分析',
      '基本面因素评估',
      '宏观经济环境影响'
    ];
  }

  // 占位符方法
  private async getProviderByModel(modelName: string): Promise<any> {
    return {
      reason: async (params: any) => ({
        predictedValue: 50000,
        predictedDirection: 'UP',
        confidence: 0.75
      })
    };
  }
}
