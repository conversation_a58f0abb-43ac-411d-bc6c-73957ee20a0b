/**
 * 金融知识图谱实现
 * 管理BTC投资相关的专业知识和推理规则
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { PrismaClient } from '@prisma/client';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  FinancialKnowledgeGraph,
  MarketContext,
  TechnicalKnowledge,
  RiskRule,
  CausalRelationship,
  KnowledgeUpdate,
  KnowledgeSearchResult,
  RiskType,
  MarketPhase,
  MarketFactor,
  FactorType,
  HistoricalPattern,
  AssetCorrelation,
  MarketSentiment,
  VolatilityRegime,
  IndicatorKnowledge,
  PatternKnowledge,
  StrategyKnowledge,
  InterpretationRule
} from '../../domain/services/knowledge-graph.interface';

@injectable()
export class BTCFinancialKnowledgeGraph implements FinancialKnowledgeGraph {
  constructor(
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    @inject(TYPES.Logger) private readonly logger: IBasicLogger
  ) {}

  async getMarketContext(symbol?: string): Promise<MarketContext> {
    try {
      this.logger.info('获取市场上下文', { symbol });

      // 获取当前市场阶段
      const currentPhase = await this.determineMarketPhase(symbol);
      
      // 获取主导因素
      const dominantFactors = await this.getDominantFactors(symbol);
      
      // 获取历史模式
      const historicalPatterns = await this.getHistoricalPatterns(symbol);
      
      // 获取资产相关性
      const correlations = await this.getAssetCorrelations(symbol);
      
      // 获取市场情绪
      const sentiment = await this.getMarketSentiment(symbol);
      
      // 获取波动率状态
      const volatilityRegime = await this.getVolatilityRegime(symbol);

      return {
        currentPhase,
        dominantFactors,
        historicalPatterns,
        correlations,
        sentiment,
        volatilityRegime
      };
    } catch (error) {
      this.logger.error('获取市场上下文失败', { error: error instanceof Error ? error.message : String(error), symbol });
      throw error;
    }
  }

  async getTechnicalAnalysisKnowledge(indicators: string[]): Promise<TechnicalKnowledge> {
    try {
      this.logger.info('获取技术分析知识', { indicators });

      // 查询指标知识
      const indicatorEntities = await this.prisma.knowledgeEntities.findMany({
        where: {
          type: 'indicator',
          domain: 'technicalAnalysis',
          name: { in: indicators },
          isActive: true
        }
      });

      const indicatorKnowledge = indicatorEntities.map(entity =>
        JSON.parse(entity.properties as string) as IndicatorKnowledge
      );

      // 查询模式知识
      const patternEntities = await this.prisma.knowledgeEntities.findMany({
        where: {
          type: 'pattern',
          domain: 'technicalAnalysis',
          isActive: true
        }
      });

      const patterns = patternEntities.map(entity =>
        JSON.parse(entity.properties as string) as PatternKnowledge
      );

      // 查询策略知识
      const strategyEntities = await this.prisma.knowledgeEntities.findMany({
        where: {
          type: 'strategy',
          domain: 'technicalAnalysis',
          isActive: true
        }
      });

      const strategies = strategyEntities.map(entity =>
        JSON.parse(entity.properties as string) as StrategyKnowledge
      );

      // 查询解释规则
      const ruleEntities = await this.prisma.knowledgeEntities.findMany({
        where: {
          type: 'interpretationRule',
          domain: 'technicalAnalysis',
          isActive: true
        }
      });

      const interpretations = ruleEntities.map(entity =>
        JSON.parse(entity.properties as string) as InterpretationRule
      );

      return {
        indicators: indicatorKnowledge,
        patterns,
        strategies,
        interpretations
      };
    } catch (error) {
      this.logger.error('获取技术分析知识失败', { error: error instanceof Error ? error.message : String(error), indicators });
      throw error;
    }
  }

  async getRiskManagementRules(riskType: RiskType): Promise<RiskRule[]> {
    try {
      this.logger.info('获取风险管理规则', { riskType });

      const ruleEntities = await this.prisma.knowledgeEntities.findMany({
        where: {
          type: 'riskRule',
          domain: 'riskManagement',
          isActive: true,
          // properties: {
          //   contains: `"type":"${riskType}"`
          // }
          name: { contains: riskType }
        }
      });

      return ruleEntities.map(entity => JSON.parse(entity.properties as string) as RiskRule);
    } catch (error) {
      this.logger.error('获取风险管理规则失败', { error: error instanceof Error ? error.message : String(error), riskType });
      throw error;
    }
  }

  async queryCausalRelationships(entity: string): Promise<CausalRelationship[]> {
    try {
      this.logger.info('查询因果关系', { entity });

      // 查找实体
      const knowledgeEntity = await this.prisma.knowledgeEntities.findFirst({
        where: {
          name: entity,
          isActive: true
        }
      });

      if (!knowledgeEntity) {
        return [];
      }

      // 查询因果关系
      const relations = await this.prisma.knowledgeRelations.findMany({
        where: {
          OR: [
            { sourceId: knowledgeEntity.id },
            { targetId: knowledgeEntity.id }
          ],
          relationType: { in: ['causes', 'implies'] }
        },
        include: {
          knowledgeEntitiesKnowledgeRelationsSourceIdToKnowledgeEntities: true,
          knowledgeEntitiesKnowledgeRelationsTargetIdToKnowledgeEntities: true
        }
      });

      return relations.map(relation => ({
        cause: relation.knowledgeEntitiesKnowledgeRelationsSourceIdToKnowledgeEntities.name,
        effect: relation.knowledgeEntitiesKnowledgeRelationsTargetIdToKnowledgeEntities.name,
        strength: Number(relation.strength),
        direction: relation.direction as 'positive' | 'negative',
        timeDelay: relation.timeDelay || 'immediate',
        conditions: relation.conditions ? JSON.parse(relation.conditions as string) : [],
        confidence: Number(relation.confidence),
        evidence: relation.evidence ? JSON.parse(relation.evidence as string) : []
      }));
    } catch (error) {
      this.logger.error('查询因果关系失败', { error: error instanceof Error ? error.message : String(error), entity });
      throw error;
    }
  }

  async updateKnowledge(knowledge: KnowledgeUpdate): Promise<void> {
    try {
      this.logger.info('更新知识', { type: knowledge.type, domain: knowledge.domain, entity: knowledge.entity });

      switch (knowledge.type) {
        case 'add':
          await this.addKnowledge(knowledge);
          break;
        case 'update':
          await this.updateExistingKnowledge(knowledge);
          break;
        case 'delete':
          await this.deleteKnowledge(knowledge);
          break;
      }

      // 更新搜索索引
      await this.updateSearchIndex(knowledge);
    } catch (error) {
      this.logger.error('更新知识失败', { error: error instanceof Error ? error.message : String(error), knowledge });
      throw error;
    }
  }

  async searchKnowledge(query: string, domain?: string): Promise<KnowledgeSearchResult[]> {
    try {
      this.logger.info('搜索知识', { query, domain });

      const keywords = query.toLowerCase().split(' ').filter(word => word.length > 2);
      
      const searchResults = await this.prisma.knowledgeSearchIndices.findMany({
        where: {
          AND: [
            domain ? { domain } : {},
            {
              OR: keywords.map(keyword => ({
                OR: [
                  { content: { contains: keyword } },
                  { keywords: { contains: keyword } }
                ]
              }))
            }
          ]
        },
        include: {
          KnowledgeEntities: true
        },
        orderBy: {
          weight: 'desc'
        },
        take: 20
      });

      return searchResults.map(result => ({
        id: result.KnowledgeEntities.id,
        title: result.KnowledgeEntities.name,
        content: result.content,
        domain: result.domain,
        relevance: this.calculateRelevance(query, result.content, result.keywords),
        confidence: Number(result.KnowledgeEntities.confidence),
        source: result.KnowledgeEntities.source,
        lastUpdated: result.KnowledgeEntities.updatedAt
      }));
    } catch (error) {
      this.logger.error('搜索知识失败', { error: error instanceof Error ? error.message : String(error), query, domain });
      throw error;
    }
  }

  // 私有辅助方法
  private async determineMarketPhase(_symbol?: string): Promise<MarketPhase> {
    // 基于技术指标和历史数据判断市场阶段
    // 这里返回一个示例值，实际实现需要分析市场数据
    return MarketPhase.BULL_MARKET;
  }

  private async getDominantFactors(_symbol?: string): Promise<MarketFactor[]> {
    // 获取当前主导市场的因素
    return [
      {
        name: 'Bitcoin Halving Cycle',
        type: FactorType.FUNDAMENTAL,
        influence: 0.8,
        confidence: 0.9,
        timeframe: 'longTerm',
        description: 'Bitcoin halving event impact on supply and price'
      },
      {
        name: 'Institutional Adoption',
        type: FactorType.FUNDAMENTAL,
        influence: 0.7,
        confidence: 0.8,
        timeframe: 'mediumTerm',
        description: 'Growing institutional investment in Bitcoin'
      }
    ];
  }

  private async getHistoricalPatterns(_symbol?: string): Promise<HistoricalPattern[]> {
    // 获取历史模式
    return [];
  }

  private async getAssetCorrelations(_symbol?: string): Promise<AssetCorrelation[]> {
    // 获取资产相关性
    return [];
  }

  private async getMarketSentiment(_symbol?: string): Promise<MarketSentiment> {
    // 获取市场情绪
    return {
      overall: 0.6,
      components: {
        fearGreed: 0.7,
        volatilitySentiment: 0.5,
        newsSentiment: 0.6,
        socialSentiment: 0.8
      },
      confidence: 0.8,
      trend: 'improving'
    };
  }

  private async getVolatilityRegime(_symbol?: string): Promise<VolatilityRegime> {
    // 获取波动率状态
    return VolatilityRegime.NORMAL;
  }

  private async addKnowledge(knowledge: KnowledgeUpdate): Promise<void> {
    await this.prisma.knowledgeEntities.create({
      data: {
        name: knowledge.entity,
        type: this.getEntityType(knowledge.data),
        domain: knowledge.domain,
        description: knowledge.data.description || '',
        properties: JSON.stringify(knowledge.data),
        confidence: knowledge.confidence,
        source: knowledge.source
      }
    });
  }

  private async updateExistingKnowledge(knowledge: KnowledgeUpdate): Promise<void> {
    await this.prisma.knowledgeEntities.updateMany({
      where: {
        name: knowledge.entity,
        domain: knowledge.domain
      },
      data: {
        properties: JSON.stringify(knowledge.data),
        confidence: knowledge.confidence,
        updatedAt: new Date()
      }
    });
  }

  private async deleteKnowledge(knowledge: KnowledgeUpdate): Promise<void> {
    await this.prisma.knowledgeEntities.updateMany({
      where: {
        name: knowledge.entity,
        domain: knowledge.domain
      },
      data: {
        isActive: false
      }
    });
  }

  private async updateSearchIndex(_knowledge: KnowledgeUpdate): Promise<void> {
    // 更新搜索索引的实现
    // 这里简化处理，实际实现需要更复杂的索引逻辑
  }

  private getEntityType(data: any): string {
    // 根据数据结构推断实体类型
    if (data.calculation) return 'indicator';
    if (data.formation) return 'pattern';
    if (data.conditions && data.execution) return 'strategy';
    if (data.condition && data.action) return 'riskRule';
    return 'concept';
  }

  private calculateRelevance(query: string, content: string, keywords: string): number {
    // 计算搜索相关性
    const queryWords = query.toLowerCase().split(' ');
    const contentWords = content.toLowerCase().split(' ');
    const keywordList = keywords.toLowerCase().split(' ');
    
    let matches = 0;
    queryWords.forEach(word => {
      if (contentWords.includes(word) || keywordList.includes(word)) {
        matches++;
      }
    });
    
    return matches / queryWords.length;
  }
}
