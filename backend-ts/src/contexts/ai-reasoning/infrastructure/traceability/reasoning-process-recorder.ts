/**
 * 推理过程记录器实现
 * 负责记录AI推理的每个步骤和决策过程
 */

import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from '@prisma/client';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/logger.interface';
import { UnifiedErrorHandler } from '../../../../shared/infrastructure/error-handling/unified-error-handler';
import { IMultiTierCacheService } from '../../../../shared/infrastructure/cache/multi-tier-cache.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import {
  ReasoningProcessRecorder as IReasoningProcessRecorder,
  ReasoningSession,
  ReasoningStepRecord,
  DecisionPointRecord,
  ModelInvocationRecord
} from '../../domain/services/reasoning-traceability.interface';

@injectable()
export class ReasoningProcessRecorder implements IReasoningProcessRecorder {
  private readonly sessions: Map<string, ReasoningSession> = new Map();
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject(TYPES.Shared.MultiTierCacheService) private readonly cache: IMultiTierCacheService,
    @inject(TYPES.Database) private readonly prisma: PrismaClient
  ) {}

  /**
   * 开始记录推理会话
   */
  async startReasoningSession(sessionId: string, request: any): Promise<ReasoningSession> {
    try {
      this.logger.info('开始推理会话记录', { sessionId });

      const session: ReasoningSession = {
        sessionId,
        startTime: new Date(),
        status: 'active',
        request,
        steps: [],
        decisions: [],
        modelInvocations: [],
        metadata: {
          userAgent: request.userAgent,
          ipAddress: request.ipAddress,
          userId: request.userId
        }
      };

      // 存储到内存缓存
      this.sessions.set(sessionId, session);

      // 存储到分布式缓存
      await this.cache.set(
        `reasoning_session:${sessionId}`,
        session,
        this.CACHE_TTL
      );

      // 持久化到数据库
      await this.persistSessionToDatabase(session);

      this.logger.info('推理会话记录已开始', { sessionId });
      return session;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningProcessRecorder.startReasoningSession',
        sessionId
      });
      throw error;
    }
  }

  /**
   * 记录推理步骤
   */
  async recordReasoningStep(sessionId: string, step: ReasoningStepRecord): Promise<void> {
    try {
      this.logger.debug('记录推理步骤', { sessionId, stepId: step.stepId });

      const session = await this.getOrLoadSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 添加步骤到会话
      session.steps.push(step);

      // 更新缓存
      await this.updateSessionInCache(session);

      // 持久化步骤到数据库
      await this.persistStepToDatabase(sessionId, step);

      this.logger.debug('推理步骤记录完成', { sessionId, stepId: step.stepId });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningProcessRecorder.recordReasoningStep',
        sessionId,
        stepId: step.stepId
      });
      throw error;
    }
  }

  /**
   * 记录决策点
   */
  async recordDecisionPoint(sessionId: string, decision: DecisionPointRecord): Promise<void> {
    try {
      this.logger.debug('记录决策点', { sessionId, decisionId: decision.decisionId });

      const session = await this.getOrLoadSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 添加决策到会话
      session.decisions.push(decision);

      // 更新缓存
      await this.updateSessionInCache(session);

      // 持久化决策到数据库
      await this.persistDecisionToDatabase(sessionId, decision);

      this.logger.debug('决策点记录完成', { sessionId, decisionId: decision.decisionId });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningProcessRecorder.recordDecisionPoint',
        sessionId,
        decisionId: decision.decisionId
      });
      throw error;
    }
  }

  /**
   * 记录AI模型调用
   */
  async recordModelInvocation(sessionId: string, invocation: ModelInvocationRecord): Promise<void> {
    try {
      this.logger.debug('记录模型调用', { sessionId, invocationId: invocation.invocationId });

      const session = await this.getOrLoadSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 添加模型调用到会话
      session.modelInvocations.push(invocation);

      // 更新缓存
      await this.updateSessionInCache(session);

      // 持久化模型调用到数据库
      await this.persistModelInvocationToDatabase(sessionId, invocation);

      this.logger.debug('模型调用记录完成', { sessionId, invocationId: invocation.invocationId });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningProcessRecorder.recordModelInvocation',
        sessionId,
        invocationId: invocation.invocationId
      });
      throw error;
    }
  }

  /**
   * 结束推理会话
   */
  async endReasoningSession(sessionId: string, result: any): Promise<void> {
    try {
      this.logger.info('结束推理会话记录', { sessionId });

      const session = await this.getOrLoadSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 更新会话状态
      session.endTime = new Date();
      session.status = 'completed';
      session.result = result;

      // 更新缓存
      await this.updateSessionInCache(session);

      // 更新数据库
      await this.updateSessionInDatabase(session);

      // 从内存中移除（保留在缓存中）
      this.sessions.delete(sessionId);

      this.logger.info('推理会话记录已结束', { 
        sessionId, 
        duration: session.endTime.getTime() - session.startTime.getTime(),
        stepsCount: session.steps.length,
        decisionsCount: session.decisions.length,
        modelInvocationsCount: session.modelInvocations.length
      });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningProcessRecorder.endReasoningSession',
        sessionId
      });
      throw error;
    }
  }

  /**
   * 获取推理会话记录
   */
  async getReasoningSession(sessionId: string): Promise<ReasoningSession | null> {
    try {
      this.logger.debug('获取推理会话记录', { sessionId });

      // 首先从内存缓存获取
      let session = this.sessions.get(sessionId);
      if (session) {
        return session;
      }

      // 从分布式缓存获取
      session = await this.cache.get(`reasoning_session:${sessionId}`);
      if (session) {
        return session;
      }

      // 从数据库获取
      session = await this.loadSessionFromDatabase(sessionId);
      if (session) {
        // 重新缓存
        await this.cache.set(
          `reasoning_session:${sessionId}`,
          session,
          this.CACHE_TTL
        );
      }

      return session;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningProcessRecorder.getReasoningSession',
        sessionId
      });
      return null;
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取或加载会话
   */
  private async getOrLoadSession(sessionId: string): Promise<ReasoningSession | null> {
    let session = this.sessions.get(sessionId);
    if (!session) {
      session = await this.getReasoningSession(sessionId);
      if (session) {
        this.sessions.set(sessionId, session);
      }
    }
    return session;
  }

  /**
   * 更新会话缓存
   */
  private async updateSessionInCache(session: ReasoningSession): Promise<void> {
    this.sessions.set(session.sessionId, session);
    await this.cache.set(
      `reasoning_session:${session.sessionId}`,
      session,
      this.CACHE_TTL
    );
  }

  /**
   * 持久化会话到数据库
   */
  private async persistSessionToDatabase(session: ReasoningSession): Promise<void> {
    try {
      await this.prisma.reasoningSessions.create({
        data: {
          sessionId: session.sessionId,
          userId: session.metadata?.userId || null,
          context: session.request || {},
          status: session.status,
          startTime: session.startTime,
          endTime: session.endTime,
          metadata: session.metadata || {}
        }
      });
    } catch (error) {
      this.logger.error('持久化会话到数据库失败', { 
        sessionId: session.sessionId, 
        error: error instanceof Error ? error.message : String(error) 
      });
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 持久化步骤到数据库
   */
  private async persistStepToDatabase(sessionId: string, step: ReasoningStepRecord): Promise<void> {
    try {
      await this.prisma.reasoningSteps.create({
        data: {
          stepId: step.stepId,
          sessionId,
          stepType: step.stepType,
          input: step.input || {},
          output: step.output || {},
          confidence: step.confidence || 0.5,
          timestamp: step.timestamp,
          metadata: {
            description: step.description,
            reasoning: step.reasoning,
            duration: step.duration,
            dependencies: step.dependencies,
            ...step.metadata
          }
        }
      });
    } catch (error) {
      this.logger.error('持久化步骤到数据库失败', { 
        sessionId, 
        stepId: step.stepId,
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  /**
   * 持久化决策到数据库
   */
  private async persistDecisionToDatabase(sessionId: string, decision: DecisionPointRecord): Promise<void> {
    try {
      await this.prisma.reasoningDecisions.create({
        data: {
          id: decision.decisionId,
          sessionId,
          decisionType: decision.decisionType,
          question: decision.question,
          options: JSON.stringify(decision.options),
          selectedOption: decision.selectedOption,
          reasoning: decision.reasoning,
          confidence: decision.confidence,
          impact: decision.impact,
          timestamp: decision.timestamp,
          context: JSON.stringify(decision.context)
        }
      });
    } catch (error) {
      this.logger.error('持久化决策到数据库失败', { 
        sessionId, 
        decisionId: decision.decisionId,
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  /**
   * 持久化模型调用到数据库
   */
  private async persistModelInvocationToDatabase(sessionId: string, invocation: ModelInvocationRecord): Promise<void> {
    try {
      await this.prisma.aiCallLogs.create({
        data: {
          id: invocation.invocationId,
          sessionId,
          modelName: invocation.modelName,
          provider: invocation.provider,
          prompt: invocation.prompt,
          response: invocation.response,
          parameters: JSON.stringify(invocation.parameters),
          duration: invocation.duration,
          tokenUsage: JSON.stringify(invocation.tokenUsage),
          cost: invocation.cost,
          timestamp: invocation.timestamp,
          success: invocation.success,
          error: invocation.error
        }
      });
    } catch (error) {
      this.logger.error('持久化模型调用到数据库失败', { 
        sessionId, 
        invocationId: invocation.invocationId,
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  /**
   * 从数据库加载会话
   */
  private async loadSessionFromDatabase(sessionId: string): Promise<ReasoningSession | null> {
    try {
      const sessionData = await this.prisma.reasoningSessions.findUnique({
        where: { id: sessionId },
        include: {
          steps: true,
          decisions: true,
          modelInvocations: true
        }
      });

      if (!sessionData) {
        return null;
      }

      return {
        sessionId: sessionData.id,
        startTime: sessionData.startTime,
        endTime: sessionData.endTime || undefined,
        status: sessionData.status as any,
        request: JSON.parse(sessionData.request),
        result: sessionData.result ? JSON.parse(sessionData.result) : undefined,
        steps: sessionData.steps.map(step => ({
          stepId: step.id,
          stepNumber: step.stepNumber,
          stepType: step.stepType as any,
          description: step.description,
          input: JSON.parse(step.input),
          output: JSON.parse(step.output),
          reasoning: step.reasoning,
          confidence: step.confidence,
          duration: step.duration,
          timestamp: step.timestamp,
          dependencies: JSON.parse(step.dependencies),
          metadata: JSON.parse(step.metadata)
        })),
        decisions: sessionData.decisions.map(decision => ({
          decisionId: decision.id,
          decisionType: decision.decisionType as any,
          question: decision.question,
          options: JSON.parse(decision.options),
          selectedOption: decision.selectedOption,
          reasoning: decision.reasoning,
          confidence: decision.confidence,
          impact: decision.impact as any,
          timestamp: decision.timestamp,
          context: JSON.parse(decision.context)
        })),
        modelInvocations: sessionData.modelInvocations.map(invocation => ({
          invocationId: invocation.id,
          modelName: invocation.modelName,
          provider: invocation.provider,
          prompt: invocation.prompt,
          response: invocation.response,
          parameters: JSON.parse(invocation.parameters),
          duration: invocation.duration,
          tokenUsage: JSON.parse(invocation.tokenUsage),
          cost: invocation.cost || undefined,
          timestamp: invocation.timestamp,
          success: invocation.success,
          error: invocation.error || undefined
        })),
        metadata: JSON.parse(sessionData.metadata)
      };
    } catch (error) {
      this.logger.error('从数据库加载会话失败', { 
        sessionId,
        error: error instanceof Error ? error.message : String(error) 
      });
      return null;
    }
  }

  /**
   * 更新数据库中的会话
   */
  private async updateSessionInDatabase(session: ReasoningSession): Promise<void> {
    try {
      await this.prisma.reasoningSessions.update({
        where: { sessionId: session.sessionId },
        data: {
          endTime: session.endTime,
          status: session.status,
          metadata: session.metadata as Prisma.InputJsonValue
        }
      });
    } catch (error) {
      this.logger.error('更新数据库会话失败', { 
        sessionId: session.sessionId,
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }
}
