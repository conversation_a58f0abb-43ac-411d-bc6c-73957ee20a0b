/**
 * 决策路径追踪器实现
 * 提供决策路径的完整追踪和回溯功能
 */

import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { PrismaClient } from '@prisma/client';
import { ILogger } from '../../../../shared/infrastructure/logging/logger.interface';
import { UnifiedErrorHandler } from '../../../../shared/infrastructure/error/unified-error-handler';
import { IMultiTierCacheService } from '../../../../shared/infrastructure/cache/multi-tier-cache.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import {
  DecisionPathTracker as IDecisionPathTracker,
  DecisionPath,
  DecisionNode,
  DecisionBranch,
  DecisionPathTrace,
  DecisionImpactAnalysis,
  TraceStep,
  AlternativePath,
  ImpactMetrics,
  CascadingEffect,
  RiskAssessment,
  RiskFactor
} from '../../domain/services/reasoning-traceability.interface';

@injectable()
export class DecisionPathTracker implements IDecisionPathTracker {
  private readonly paths: Map<string, DecisionPath> = new Map();
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24小时

  constructor(
    @inject(TYPES.Logger) private readonly logger: ILogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject(TYPES.Shared.MultiTierCacheService) private readonly cache: IMultiTierCacheService,
    @inject(TYPES.Database) private readonly prisma: PrismaClient
  ) {}

  /**
   * 创建决策路径
   */
  async createDecisionPath(pathId: string, initialContext: any): Promise<DecisionPath> {
    try {
      this.logger.info('创建决策路径', { pathId });

      const path: DecisionPath = {
        pathId,
        startTime: new Date(),
        status: 'active',
        initialContext,
        nodes: [],
        branches: [],
        metadata: {
          createdBy: initialContext.userId || 'system',
        }
      };

      // 存储到内存缓存
      this.paths.set(pathId, path);

      // 存储到分布式缓存
      await this.cache.set(
        `decision_path:${pathId}`,
        path,
        this.CACHE_TTL
      );

      // 持久化到数据库
      await this.persistPathToDatabase(path);

      this.logger.info('决策路径创建完成', { pathId });
      return path;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DecisionPathTracker.createDecisionPath',
        pathId
      });
      throw error;
    }
  }

  /**
   * 添加决策节点
   */
  async addDecisionNode(pathId: string, node: DecisionNode): Promise<void> {
    try {
      this.logger.debug('添加决策节点', { pathId, nodeId: node.nodeId });

      const path = await this.getOrLoadPath(pathId);
      if (!path) {
        throw new Error(`决策路径不存在: ${pathId}`);
      }

      // 添加节点到路径
      path.nodes.push(node);

      // 更新缓存
      await this.updatePathInCache(path);

      // 持久化节点到数据库
      await this.persistNodeToDatabase(pathId, node);

      this.logger.debug('决策节点添加完成', { pathId, nodeId: node.nodeId });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DecisionPathTracker.addDecisionNode',
        pathId,
        nodeId: node.nodeId
      });
      throw error;
    }
  }

  /**
   * 记录分支决策
   */
  async recordBranchDecision(pathId: string, branch: DecisionBranch): Promise<void> {
    try {
      this.logger.debug('记录分支决策', { pathId, branchId: branch.branchId });

      const path = await this.getOrLoadPath(pathId);
      if (!path) {
        throw new Error(`决策路径不存在: ${pathId}`);
      }

      // 添加分支到路径
      path.branches.push(branch);

      // 更新缓存
      await this.updatePathInCache(path);

      // 持久化分支到数据库
      await this.persistBranchToDatabase(pathId, branch);

      this.logger.debug('分支决策记录完成', { pathId, branchId: branch.branchId });
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DecisionPathTracker.recordBranchDecision',
        pathId,
        branchId: branch.branchId
      });
      throw error;
    }
  }

  /**
   * 获取决策路径
   */
  async getDecisionPath(pathId: string): Promise<DecisionPath | null> {
    try {
      this.logger.debug('获取决策路径', { pathId });

      // 首先从内存缓存获取
      let path = this.paths.get(pathId);
      if (path) {
        return path;
      }

      // 从分布式缓存获取
      path = await this.cache.get(`decision_path:${pathId}`);
      if (path) {
        return path;
      }

      // 从数据库获取
      path = await this.loadPathFromDatabase(pathId);
      if (path) {
        // 重新缓存
        await this.cache.set(
          `decision_path:${pathId}`,
          path,
          this.CACHE_TTL
        );
      }

      return path;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DecisionPathTracker.getDecisionPath',
        pathId
      });
      return null;
    }
  }

  /**
   * 回溯决策路径
   */
  async traceDecisionPath(pathId: string, targetNodeId?: string): Promise<DecisionPathTrace> {
    try {
      this.logger.info('回溯决策路径', { pathId, targetNodeId });

      const path = await this.getDecisionPath(pathId);
      if (!path) {
        throw new Error(`决策路径不存在: ${pathId}`);
      }

      // 构建追踪步骤
      const traceSteps = await this.buildTraceSteps(path, targetNodeId);
      
      // 识别关键决策
      const keyDecisions = this.identifyKeyDecisions(path);
      
      // 识别关键分支
      const criticalBranches = this.identifyCriticalBranches(path);
      
      // 生成替代路径
      const alternativePaths = await this.generateAlternativePaths(path, targetNodeId);
      
      // 生成摘要
      const summary = this.generateTraceSummary(path, traceSteps, keyDecisions);

      const trace: DecisionPathTrace = {
        pathId,
        traceSteps,
        keyDecisions,
        criticalBranches,
        alternativePaths,
        summary
      };

      this.logger.info('决策路径回溯完成', { 
        pathId, 
        stepsCount: traceSteps.length,
        keyDecisionsCount: keyDecisions.length 
      });

      return trace;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DecisionPathTracker.traceDecisionPath',
        pathId,
        targetNodeId
      });
      throw error;
    }
  }

  /**
   * 分析决策影响
   */
  async analyzeDecisionImpact(pathId: string, nodeId: string): Promise<DecisionImpactAnalysis> {
    try {
      this.logger.info('分析决策影响', { pathId, nodeId });

      const path = await this.getDecisionPath(pathId);
      if (!path) {
        throw new Error(`决策路径不存在: ${pathId}`);
      }

      const node = path.nodes.find(n => n.nodeId === nodeId);
      if (!node) {
        throw new Error(`决策节点不存在: ${nodeId}`);
      }

      // 分析直接影响
      const directImpact = this.analyzeDirectImpact(path, node);
      
      // 分析间接影响
      const indirectImpact = await this.analyzeIndirectImpact(path, node);
      
      // 分析级联效应
      const cascadingEffects = this.analyzeCascadingEffects(path, node);
      
      // 评估风险
      const riskAssessment = this.assessRisks(path, node, cascadingEffects);
      
      // 生成建议
      const recommendations = this.generateRecommendations(directImpact, indirectImpact, riskAssessment);

      const analysis: DecisionImpactAnalysis = {
        nodeId,
        directImpact,
        indirectImpact,
        cascadingEffects,
        riskAssessment,
        recommendations
      };

      this.logger.info('决策影响分析完成', { pathId, nodeId });
      return analysis;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'DecisionPathTracker.analyzeDecisionImpact',
        pathId,
        nodeId
      });
      throw error;
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 获取或加载路径
   */
  private async getOrLoadPath(pathId: string): Promise<DecisionPath | null> {
    let path = this.paths.get(pathId);
    if (!path) {
      path = await this.getDecisionPath(pathId);
      if (path) {
        this.paths.set(pathId, path);
      }
    }
    return path;
  }

  /**
   * 更新路径缓存
   */
  private async updatePathInCache(path: DecisionPath): Promise<void> {
    this.paths.set(path.pathId, path);
    await this.cache.set(
      `decision_path:${path.pathId}`,
      path,
      this.CACHE_TTL
    );
  }

  /**
   * 构建追踪步骤
   */
  private async buildTraceSteps(path: DecisionPath, targetNodeId?: string): Promise<TraceStep[]> {
    const steps: TraceStep[] = [];
    let currentNodes = path.nodes;

    // 如果指定了目标节点，只追踪到该节点
    if (targetNodeId) {
      const targetIndex = path.nodes.findIndex(n => n.nodeId === targetNodeId);
      if (targetIndex >= 0) {
        // TODO: Implement logic to get real price or relevant data
        // currentNodes = await this.getRealPrice(); 
      }
    }

    currentNodes.forEach((node, index) => {
      steps.push({
        nodeId: node.nodeId,
        action: this.getNodeAction(node),
        reasoning: node.reasoning,
        impact: this.assessNodeImpact(node),
        timestamp: node.timestamp
      });
    });

    return steps;
  }

  /**
   * 识别关键决策
   */
  private identifyKeyDecisions(path: DecisionPath): DecisionNode[] {
    return path.nodes.filter(node => 
      node.nodeType === 'decision' && 
      (node.confidence < 0.7 || this.hasHighImpact(node))
    );
  }

  /**
   * 识别关键分支
   */
  private identifyCriticalBranches(path: DecisionPath): DecisionBranch[] {
    return path.branches.filter(branch =>
      branch.probability < 0.8 || branch.weight > 0.7
    );
  }

  /**
   * 生成替代路径
   */
  private async generateAlternativePaths(path: DecisionPath, targetNodeId?: string): Promise<AlternativePath[]> {
    const alternatives: AlternativePath[] = [];
    
    // 基于关键决策点生成替代路径
    const keyDecisions = this.identifyKeyDecisions(path);
    
    for (const decision of keyDecisions) {
      const alternative: AlternativePath = {
        pathId: `${path.pathId}_alt_${decision.nodeId}`,
        description: `如果在${decision.label}处选择不同选项`,
        probability: 1 - decision.confidence,
        expectedOutcome: this.predictAlternativeOutcome(decision),
        reasoning: `基于${decision.reasoning}的替代分析`
      };
      alternatives.push(alternative);
    }

    return alternatives;
  }

  /**
   * 生成追踪摘要
   */
  private generateTraceSummary(path: DecisionPath, steps: TraceStep[], keyDecisions: DecisionNode[]): string {
    const totalSteps = steps.length;
    const keyDecisionsCount = keyDecisions.length;
    const avgConfidence = path.nodes.reduce((sum, node) => sum + node.confidence, 0) / path.nodes.length;

    return `决策路径包含${totalSteps}个步骤，其中${keyDecisionsCount}个关键决策点。` +
           `路径从${path.startTime.toISOString()}开始，` +
           `${path.status === 'completed' ? '已完成' : '进行中'}。`;
  }

  /**
   * 分析直接影响
   */
  private analyzeDirectImpact(path: DecisionPath, node: DecisionNode): ImpactMetrics {
    return {
      magnitude: node.confidence,
      scope: 'local',
      confidence: node.confidence,
      timeframe: this.estimateTimeframe(node),
      affectedAreas: this.identifyAffectedAreas(node)
    };
  }

  /**
   * 分析间接影响
   */
  private async analyzeIndirectImpact(path: DecisionPath, node: DecisionNode): Promise<ImpactMetrics> {
    const childNodes = path.nodes.filter(n => n.parentNodes.includes(node.nodeId));
// TODO: Implement logic to calculate indirect magnitude based on real data
    const indirectMagnitude = 0; // Placeholder for now, as getRealPrice is not defined

    return {
      scope: childNodes.length > 2 ? 'regional' : 'local',
      magnitude: indirectMagnitude * 0.7, // 间接影响通常较小
      confidence: node.confidence * 0.8,
      timeframe: '中长期',
      affectedAreas: childNodes.flatMap(child => this.identifyAffectedAreas(child))
    };
  }

  /**
   * 分析级联效应
   */
  private analyzeCascadingEffects(path: DecisionPath, node: DecisionNode): CascadingEffect[] {
    const effects: CascadingEffect[] = [];
    
    // 基于节点的子节点分析级联效应
    const childNodes = path.nodes.filter(n => n.parentNodes.includes(node.nodeId));
    
    childNodes.forEach((child, index) => {
      effects.push({
        effectId: `cascade_${node.nodeId}_${child.nodeId}`,
        description: `${node.label}影响${child.label}`,
        probability: node.confidence * child.confidence,
        severity: this.determineSeverity(child),
        mitigation: [`监控${child.label}`, `调整${child.description}`]
      });
    });

    return effects;
  }

  /**
   * 评估风险
   */
  private assessRisks(path: DecisionPath, node: DecisionNode, cascadingEffects: CascadingEffect[]): RiskAssessment {
    const riskFactors: RiskFactor[] = [];
    
    // 基于置信度评估风险
    if (node.confidence < 0.6) {
      riskFactors.push({
        factorId: `low_confidence_${node.nodeId}`,
        description: '决策置信度较低',
        probability: 1 - node.confidence,
        impact: 0.7,
        severity: 'medium',
        category: 'decision_quality'
      });
    }

    // 基于级联效应评估风险
    cascadingEffects.forEach(effect => {
      if (effect.severity === 'high') {
        riskFactors.push({
          factorId: `cascade_risk_${effect.effectId}`,
          description: effect.description,
          probability: effect.probability,
          impact: 0.8,
          severity: effect.severity,
          category: 'cascading_effect'
        });
      }
    });

    const overallRisk = this.calculateOverallRisk(riskFactors);

    return {
      overallRisk,
      riskFactors,
      mitigationStrategies: this.generateMitigationStrategies(riskFactors),
      contingencyPlans: this.generateContingencyPlans(riskFactors)
    };
  }

  /**
   * 生成建议
   */
  private generateRecommendations(
    directImpact: ImpactMetrics,
    indirectImpact: ImpactMetrics,
    riskAssessment: RiskAssessment
  ): string[] {
    const recommendations: string[] = [];

    if (directImpact.magnitude > 0.8) {
      recommendations.push('密切监控直接影响，确保预期结果');
    }

    if (indirectImpact.magnitude > 0.6) {
      recommendations.push('关注间接影响，制定应对措施');
    }

    if (riskAssessment.overallRisk === 'high') {
      recommendations.push('立即实施风险缓解策略');
    }

    return recommendations;
  }

  // ==================== 数据库操作方法 ====================

  /**
   * 持久化路径到数据库
   */
  private async persistPathToDatabase(path: DecisionPath): Promise<void> {
    try {
      await this.prisma.decisionPaths.create({
        data: {
          pathId: path.pathId,
          startNode: path.nodes[0]?.nodeId || 'unknown',
          endNode: path.nodes[path.nodes.length - 1]?.nodeId || null,
          totalWeight: 1.0,
          isComplete: path.status === 'completed',
          metadata: {
            startTime: path.startTime,
            status: path.status,
            initialContext: path.initialContext,
            ...path.metadata
          }
        }
      });
    } catch (error) {
      this.logger.error('持久化路径到数据库失败', { 
        pathId: path.pathId, 
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  /**
   * 持久化节点到数据库
   */
  private async persistNodeToDatabase(pathId: string, node: DecisionNode): Promise<void> {
    try {
      await this.prisma.decisionNodes.create({
        data: {
          nodeId: node.nodeId,
          pathId,
          nodeType: node.nodeType,
          confidence: node.confidence,
          timestamp: node.timestamp,
          data: {
            label: node.label,
            description: node.description,
            input: node.input,
            output: node.output,
            reasoning: node.reasoning,
            parentNodes: node.parentNodes,
            childNodes: node.childNodes,
            metadata: node.metadata
          }
        }
      });
    } catch (error) {
      this.logger.error('持久化节点到数据库失败', { 
        pathId, 
        nodeId: node.nodeId,
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  /**
   * 持久化分支到数据库
   */
  private async persistBranchToDatabase(pathId: string, branch: DecisionBranch): Promise<void> {
    try {
      await this.prisma.decisionBranches.create({
        data: {
          branchId: branch.branchId,
          pathId,
          fromNode: branch.fromNodeId,
          toNode: branch.toNodeId,
          condition: typeof branch.condition === 'string' ? JSON.parse(branch.condition) : branch.condition,
          probability: branch.probability,
          weight: branch.weight,
          metadata: {
            reasoning: branch.reasoning,
            ...branch.metadata
          }
        }
      });
    } catch (error) {
      this.logger.error('持久化分支到数据库失败', { 
        pathId, 
        branchId: branch.branchId,
        error: error instanceof Error ? error.message : String(error) 
      });
    }
  }

  /**
   * 从数据库加载路径
   */
  private async loadPathFromDatabase(pathId: string): Promise<DecisionPath | null> {
    try {
      const pathData = await this.prisma.decisionPaths.findUnique({
        where: { id: pathId },
        include: {
          nodes: true,
          branches: true
        }
      });

      if (!pathData) {
        return null;
      }

      return {
        pathId: pathData.id,
        startTime: pathData.createdAt,
        endTime: undefined, // DecisionPaths模型没有endTime
        status: pathData.isComplete ? 'completed' : 'active',
        initialContext: pathData.metadata || {},
        finalDecision: undefined, // DecisionPaths模型没有finalDecision
        nodes: pathData.nodes.map(node => {
          // 从data字段中提取所需信息
          const nodeData = node.data as any;
          return {
            nodeId: node.nodeId,
            nodeType: node.nodeType as any,
            label: nodeData.label || node.nodeId,
            description: nodeData.description || '',
            input: nodeData.input || {},
            output: nodeData.output || {},
            reasoning: nodeData.reasoning || '',
            confidence: Number(node.confidence),
            timestamp: node.timestamp,
            parentNodes: nodeData.parentNodes || [],
            childNodes: nodeData.childNodes || [],
            level: 0, // 需要计算
            metadata: nodeData.metadata || {}
          };
        }),
        branches: pathData.branches.map(branch => {
          const branchMetadata = branch.metadata as any;
          return {
            branchId: branch.branchId,
            fromNodeId: branch.fromNode,
            toNodeId: branch.toNode,
            condition: typeof branch.condition === 'string' ? branch.condition : JSON.stringify(branch.condition),
            probability: Number(branch.probability),
            reasoning: branchMetadata?.reasoning || '',
            weight: Number(branch.weight),
            metadata: branchMetadata || {}
          };
        }),
        metadata: JSON.parse(pathData.metadata)
      };
    } catch (error) {
      this.logger.error('从数据库加载路径失败', { 
        pathId,
        error: error instanceof Error ? error.message : String(error) 
      });
      return null;
    }
  }

  // ==================== 辅助计算方法 ====================

  private getNodeAction(node: DecisionNode): string {
    switch (node.nodeType) {
      case 'input': return '输入数据';
      case 'process': return '处理分析';
      case 'decision': return '做出决策';
      case 'output': return '输出结果';
      default: return '执行操作';
    }
  }

  private assessNodeImpact(node: DecisionNode): string {
    if (node.confidence > 0.8) return '高影响';
    if (node.confidence > 0.6) return '中等影响';
    return '低影响';
  }

  private hasHighImpact(node: DecisionNode): boolean {
    return node.childNodes.length > 2 || node.confidence < 0.6;
  }

  private predictAlternativeOutcome(decision: DecisionNode): any {
    return {
      expectedResult: '替代结果',
      confidence: 1 - decision.confidence,
      riskLevel: decision.confidence < 0.5 ? 'high' : 'medium'
    };
  }

  private determineImpactScope(node: DecisionNode): 'local' | 'regional' | 'global' {
    if (node.childNodes.length > 5) return 'global';
    if (node.childNodes.length > 2) return 'regional';
    return 'local';
  }

  private estimateTimeframe(node: DecisionNode): string {
    if (node.nodeType === 'decision') return '短期';
    if (node.childNodes.length > 0) return '中期';
    return '长期';
  }

  private identifyAffectedAreas(node: DecisionNode): string[] {
    const areas = ['决策质量'];
    if (node.nodeType === 'decision') areas.push('风险控制');
    if (node.confidence < 0.7) areas.push('不确定性管理');
    return areas;
  }

  private determineSeverity(node: DecisionNode): 'low' | 'medium' | 'high' {
    if (node.confidence < 0.5) return 'high';
    if (node.confidence < 0.7) return 'medium';
    return 'low';
  }

  private calculateOverallRisk(riskFactors: RiskFactor[]): 'low' | 'medium' | 'high' {
    if (riskFactors.length === 0) return 'low';
    
    const avgRisk = riskFactors.reduce((sum, factor) => sum + factor.impact * factor.probability, 0) / riskFactors.length;
    
    if (avgRisk > 0.7) return 'high';
    if (avgRisk > 0.4) return 'medium';
    return 'low';
  }

  private generateMitigationStrategies(riskFactors: RiskFactor[]): string[] {
    const strategies: string[] = [];
    
    riskFactors.forEach(factor => {
      switch (factor.category) {
        case 'decision_quality':
          strategies.push('提高决策数据质量');
          break;
        case 'cascading_effect':
          strategies.push('建立级联效应监控');
          break;
        default:
          strategies.push('加强风险监控');
      }
    });

    return [...new Set(strategies)]; // 去重
  }

  private generateContingencyPlans(riskFactors: RiskFactor[]): string[] {
    const plans: string[] = [];
    
    if (riskFactors.some(f => f.severity === 'high')) {
      plans.push('启动紧急响应程序');
    }
    
    if (riskFactors.some(f => f.category === 'decision_quality')) {
      plans.push('准备决策回滚方案');
    }

    return plans;
  }
}
