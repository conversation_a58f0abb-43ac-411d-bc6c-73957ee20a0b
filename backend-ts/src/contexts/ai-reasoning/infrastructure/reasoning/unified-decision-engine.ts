/**
 * 统一决策引擎实现
 * 整合三大AI模块的分析结果，生成最终投资决策
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
// import { v4 as uuidv4 } from 'uuid';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import {
  IUnifiedTechnicalIndicatorCalculator
} from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { UNIFIED_TECHNICAL_INDICATOR_TYPES } from '../../../../shared/infrastructure/technical-indicators/types';
import { TradingSymbol } from '../../../../contexts/market-data/domain/value-objects/trading-symbol';
import { Timeframe } from '../../../../contexts/market-data/domain/value-objects/timeframe';
import {
  UnifiedDecisionEngine,
  InvestmentRequest,
  InvestmentDecision,
  DecisionQualityAssessment,
  DecisionFeedback,
  // ExecutionPlan,
  // RiskControls,
  // ExpectedReturn,
  // MonitoringPoint,
  // AdjustmentTrigger
} from '../../domain/services/reasoning-engine.interface';
import { LLMRouter, LLMRequest, ModelCapability } from '../../domain/services/llm-provider.interface';

// Binance API响应类型定义
interface BinanceTickerResponse {
  symbol: string;
  priceChange: string;
  priceChangePercent: string;
  weightedAvgPrice: string;
  prevClosePrice: string;
  lastPrice: string;
  lastQty: string;
  bidPrice: string;
  askPrice: string;
  openPrice: string;
  highPrice: string;
  lowPrice: string;
  volume: string;
  quoteVolume: string;
  openTime: number;
  closeTime: number;
  firstId: number;
  lastId: number;
  count: number;
}

// 导入市场数据服务
import { MarketDataApplicationService } from '../../../../contexts/market-data/application/services/market-data-application-service';

// 导入三大模块的服务
import { IPureAITradingSignalGenerator } from '../../domain/services/pure-ai-analysis.interface';
import { IPureAIRiskAnalysisEngine } from '../../domain/services/pure-ai-analysis.interface';
// Trend analysis types are handled through application service

// 导入数据类型
// import { MultiTimeframeData } from '../../../../contexts/trend-analysis/domain/value-objects/multi-timeframe-data';
import { Position as RiskPosition, PositionType } from '../../../../contexts/risk-management/domain/value-objects/position';

@injectable()
export class AIUnifiedDecisionEngine implements UnifiedDecisionEngine {
  constructor(
    @inject(TYPES.AIReasoning.LLMRouter) private readonly llmRouter: LLMRouter,
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.AIReasoning.PureAITradingSignalGenerator) private readonly tradingSignalGenerator: IPureAITradingSignalGenerator,
    @inject(TYPES.AIReasoning.PureAIRiskAnalysisEngine) private readonly riskAnalysisEngine: IPureAIRiskAnalysisEngine,
    @inject(TYPES.TrendAnalysis.TrendAnalysisApplicationService) private readonly trendAnalysisService: any,
    @inject(TYPES.MarketData.MarketDataApplicationService) private readonly marketDataService: MarketDataApplicationService,
    @inject(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator)
    private readonly technicalCalculator: IUnifiedTechnicalIndicatorCalculator
  ) {}

  async makeInvestmentDecision(request: InvestmentRequest): Promise<InvestmentDecision> {
    try {
      this.logger.info('开始制定投资决策', {
        symbol: request.symbol,
        analysisDepth: request.analysisDepth
      });

      // 标准化Symbol格式 - 将BTC转换为BTC/USDT
      const normalizedRequest = {
        ...request,
        symbol: this.normalizeSymbol(request.symbol)
      };

      this.logger.info('Symbol格式标准化', {
        original: request.symbol,
        normalized: normalizedRequest.symbol
      });

      // 1. 获取真实市场数据
      this.logger.info('🔍 获取真实市场数据...');
      const realMarketData = await this.getRealMarketData(normalizedRequest.symbol);
      this.logger.info('✅ 真实市场数据获取完成', {
        currentPrice: realMarketData.currentPrice,
        change24h: realMarketData.change24h,
        volume24h: realMarketData.volume24h
      });

      // 2. 并行获取三大模块分析结果（使用真实数据）
      const requestWithMarketData = {
        ...normalizedRequest,
        marketData: realMarketData
      };

      // 优化：并行获取历史数据和执行分析
      const [historicalDataPromise, tradingSignalPromise, trendIntelligencePromise] = [
        this.marketDataService.getHistoricalData({
          symbol: normalizedRequest.symbol,
          timeframe: '1h',
          limit: 100
        }),
        this.getTradingSignalAnalysis(requestWithMarketData),
        this.getTrendIntelligenceAnalysis(requestWithMarketData)
      ];

      // 等待历史数据，然后启动风险评估
      const historicalData = await historicalDataPromise;
      const priceHistory = historicalData.map(data => ({
        timestamp: new Date(data.timestamp),
        open: data.open,
        high: data.high,
        low: data.low,
        close: data.close,
        volume: data.volume
      }));

      const riskAssessmentPromise = this.getRiskAssessmentAnalysisWithHistory(requestWithMarketData, priceHistory);

      // 等待所有分析完成
      const [tradingSignal, riskAssessment, trendIntelligence] = await Promise.all([
        tradingSignalPromise,
        riskAssessmentPromise,
        trendIntelligencePromise
      ]);

      // 2. AI综合决策
      const decision = await this.synthesizeDecision(
        request,
        tradingSignal,
        riskAssessment,
        trendIntelligence
      );

      this.logger.info('投资决策制定完成', {
        symbol: request.symbol,
        action: decision.action,
        confidence: decision.confidence
      });

      return decision;
    } catch (error) {
      this.logger.error('投资决策制定失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async evaluateDecisionQuality(decision: InvestmentDecision): Promise<DecisionQualityAssessment> {
    try {
      this.logger.info('开始评估决策质量', { decisionId: decision.timestamp });

      const model = await this.llmRouter.selectOptimalModel(
        { prompt: 'decision quality assessment' } as LLMRequest,
        { capabilities: [ModelCapability.REASONING] }
      );

      const provider = (await this.llmRouter.getAvailableProviders())
        .find(p => p.supportedModels.includes(model));

      if (!provider) {
        throw new Error('未找到可用的LLM提供者');
      }

      const request: LLMRequest = {
        prompt: `请评估以下投资决策的质量：

决策内容:
- 行动: ${decision.action}
- 执行计划: ${JSON.stringify(decision.executionPlan)}
- 风险控制: ${JSON.stringify(decision.riskControls)}
- 预期收益: ${JSON.stringify(decision.expectedReturn)}
- 推理过程: ${decision.reasoning.join('; ')}
- 置信度: ${decision.confidence}

请从以下维度评估决策质量（0-1分）：
1. 准确性 - 决策的逻辑准确性
2. 一致性 - 各组件间的一致性
3. 风险适当性 - 风险控制的合理性
4. 用户匹配度 - 与用户特征的匹配度
5. 执行可行性 - 执行计划的可行性

请按以下格式返回：
{
  "accuracy": 0.85,
  "consistency": 0.90,
  "riskAppropriateness": 0.80,
  "userAlignment": 0.88,
  "executionFeasibility": 0.92,
  "overallQuality": 0.87,
  "improvementSuggestions": ["建议1", "建议2"]
}`,
        systemPrompt: `你是一个专业的投资决策质量评估专家。`,
        reasoningFramework: {
          "逻辑检查": "检查决策的逻辑一致性",
          "风险评估": "评估风险控制的合理性",
          "可行性分析": "分析执行计划的可行性",
          "改进建议": "提供具体的改进建议"
        }
      };

      const response = await provider.reason(request);
      const assessment = typeof response.content === 'string'
        ? JSON.parse(this.cleanJsonFromMarkdown(response.content))
        : response.content;

      this.logger.info('决策质量评估完成', {
        overallQuality: assessment.overallQuality
      });

      return assessment;
    } catch (error) {
      this.logger.error('决策质量评估失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  async updateDecisionStrategy(feedback: DecisionFeedback): Promise<void> {
    try {
      this.logger.info('开始更新决策策略', { decisionId: feedback.decisionId });

      // 分析反馈并更新策略
      const model = await this.llmRouter.selectOptimalModel(
        { prompt: 'strategy update' } as LLMRequest,
        { capabilities: [ModelCapability.REASONING] }
      );

      const provider = (await this.llmRouter.getAvailableProviders())
        .find(p => p.supportedModels.includes(model));

      if (!provider) {
        throw new Error('未找到可用的LLM提供者');
      }

      const request: LLMRequest = {
        prompt: `基于以下反馈更新决策策略：

反馈信息:
- 决策ID: ${feedback.decisionId}
- 实际结果: ${JSON.stringify(feedback.actualOutcome)}
- 用户满意度: ${feedback.userSatisfaction}
- 性能指标: ${JSON.stringify(feedback.performanceMetrics)}
- 经验教训: ${feedback.lessonsLearned.join('; ')}

请分析反馈并提供策略更新建议：
1. 识别决策中的问题
2. 分析问题的根本原因
3. 提出具体的改进措施
4. 制定实施计划

请按以下格式返回：
{
  "identifiedIssues": ["问题1", "问题2"],
  "rootCauses": ["原因1", "原因2"],
  "improvementMeasures": ["措施1", "措施2"],
  "implementationPlan": ["步骤1", "步骤2"],
  "expectedImpact": "预期影响"
}`,
        systemPrompt: `你是一个专业的策略优化专家，擅长从反馈中学习并改进决策策略。`
      };

      const response = await provider.reason(request);
      const updatePlan = typeof response.content === 'string'
        ? JSON.parse(this.cleanJsonFromMarkdown(response.content))
        : response.content;

      // 这里应该实际更新策略参数
      this.logger.info('决策策略更新完成', {
        decisionId: feedback.decisionId,
        improvementMeasures: updatePlan.improvementMeasures.length
      });

    } catch (error) {
      this.logger.error('决策策略更新失败', { error: error instanceof Error ? error.message : String(error) });
      throw error;
    }
  }

  private async synthesizeDecision(
    request: InvestmentRequest,
    tradingSignal: any,
    riskAssessment: any,
    trendIntelligence: any
  ): Promise<InvestmentDecision> {
    this.logger.info('开始综合决策分析');

    try {
      // 直接使用稳定可用的Gemini模型，避免模型选择错误
      const model = 'gemini-2.0-flash';
      this.logger.info('使用固定模型进行投资决策', { model });

      const availableProviders = await this.llmRouter.getAvailableProviders();
      this.logger.info('可用提供者', {
        providers: availableProviders.map(p => ({
          name: p.name,
          models: p.supportedModels
        }))
      });

      const provider = availableProviders.find(p => p.supportedModels.includes(model));

      if (!provider) {
        this.logger.error('未找到支持模型的提供者', {
          model,
          availableProviders: availableProviders.map(p => p.name)
        });
        throw new Error(`未找到支持模型 ${model} 的LLM提供者`);
      }

    this.logger.info('使用提供者', { provider: provider.name, model });

    const llmRequest: LLMRequest = {
      prompt: `作为专业投资顾问，综合以下分析结果，为客户提供最终投资建议：

交易信号分析: ${JSON.stringify(tradingSignal)}
风险评估分析: ${JSON.stringify(riskAssessment)}
趋势分析: ${JSON.stringify(trendIntelligence)}
用户画像: ${JSON.stringify(request.userProfile)}
当前持仓: ${JSON.stringify(request.position)}

请提供最终投资决策，包括：
1. 明确的行动建议（STRONG_BUY/BUY/HOLD/SELL/STRONG_SELL）
2. 具体的执行计划
3. 风险控制措施
4. 预期收益目标
5. 决策的核心逻辑
6. 需要监控的关键指标
7. 调整决策的触发条件

请按以下格式返回：
{
  "action": "BUY",
  "executionPlan": {
    "targetPrice": 42000,
    "positionSize": 0.05,
    "timeframe": "1-2 weeks",
    "splitOrders": true,
    "orderType": "limit",
    "executionStrategy": "分批建仓"
  },
  "riskControls": {
    "stopLoss": 40000,
    "takeProfit": 48000,
    "maxDrawdown": "15%",
    "positionLimit": 0.1,
    "riskLevel": "medium"
  },
  "expectedReturn": {
    "target": 0.15,
    "timeframe": "30 days",
    "probability": 0.7,
    "scenarios": [
      {"name": "乐观", "probability": 0.3, "return": 0.25, "description": "突破关键阻力位"},
      {"name": "基准", "probability": 0.4, "return": 0.15, "description": "正常上涨趋势"},
      {"name": "悲观", "probability": 0.3, "return": 0.05, "description": "遇到阻力回调"}
    ]
  },
  "reasoning": ["推理1", "推理2", "推理3"],
  "monitoringPoints": [
    {"metric": "价格", "threshold": 41000, "action": "加仓", "priority": "high"},
    {"metric": "成交量", "threshold": 1000000, "action": "观察", "priority": "medium"}
  ],
  "adjustmentTriggers": [
    {"condition": "跌破支撑位40000", "action": "止损", "reasoning": "技术面恶化"},
    {"condition": "突破阻力位45000", "action": "加仓", "reasoning": "趋势确认"}
  ],
  "confidence": 0.78
}`,
      systemPrompt: `你是一个专业的投资顾问，具有丰富的加密货币投资经验。

决策原则：
- 风险控制优先于收益追求
- 基于客观分析，避免主观偏见
- 考虑用户的风险承受能力
- 提供可执行的具体方案
- 保持适度的保守性`,
      reasoningFramework: {
        "一致性检查": "检查三个模块结论的一致性",
        "风险收益分析": "评估风险收益比的合理性",
        "用户匹配": "确保建议符合用户特征",
        "执行可行性": "确保方案具有可执行性"
      }
    };

    const response = await provider.reason(llmRequest);
    const decisionData = typeof response.content === 'string'
      ? JSON.parse(this.cleanJsonFromMarkdown(response.content))
      : response.content;

    // 转换下划线命名为驼峰命名，确保与TypeScript接口一致
    return {
      action: decisionData.action,
      executionPlan: {
        targetPrice: decisionData.executionPlan?.targetPrice || null,
        positionSize: decisionData.executionPlan?.positionSize || 0,
        timeframe: decisionData.executionPlan?.timeframe || 'N/A',
        splitOrders: decisionData.executionPlan?.splitOrders || false,
        orderType: decisionData.executionPlan?.orderType || 'market',
        executionStrategy: decisionData.executionPlan?.executionStrategy || '标准执行'
      },
      riskControls: {
        stopLoss: decisionData.riskControls?.stopLoss || null,
        takeProfit: decisionData.riskControls?.takeProfit || null,
        maxDrawdown: decisionData.riskControls?.maxDrawdown || '10%',
        positionLimit: decisionData.riskControls?.positionLimit || 0.1,
        riskLevel: decisionData.riskControls?.riskLevel || 'medium'
      },
      expectedReturn: {
        target: decisionData.expectedReturn?.target || 0,
        timeframe: decisionData.expectedReturn?.timeframe || 'N/A',
        probability: decisionData.expectedReturn?.probability || 0.5,
        scenarios: decisionData.expectedReturn?.scenarios || []
      },
      reasoning: decisionData.reasoning || [],
      monitoringPoints: decisionData.monitoringPoints || [],
      adjustmentTriggers: decisionData.adjustmentTriggers || [],
      confidence: decisionData.confidence || 0.5,
      timestamp: new Date()
    };
    } catch (error) {
      this.logger.error('AI综合决策失败，尝试使用其他AI提供者', {
        error: error instanceof Error ? error.message : String(error)
      });

      // 尝试使用其他可用的AI提供者
      return this.retryWithAlternativeProviders(request, tradingSignal, riskAssessment, trendIntelligence, error);
    }
  }



  private async getTradingSignalAnalysis(request: InvestmentRequest & { marketData?: any }): Promise<any> {
    try {
      this.logger.info('📈 开始智能交易信号分析');

      const marketData = request.marketData;
      const signalRequest = {
        symbol: { symbol: request.symbol },
        timeframe: { value: '1h' },
        analysisDepth: request.analysisDepth || 'standard',
        marketContext: {
          currentPrice: marketData?.currentPrice || (() => {
            throw new Error(`无法获取${request.symbol}的当前价格数据，无法进行准确的交易信号分析`);
          })(),
          volatility: marketData?.volatility || 0.03,
          volume24h: marketData?.volume24h || (() => {
            throw new Error(`无法获取${request.symbol}的24小时交易量数据，无法进行准确的交易信号分析`);
          })(),
          marketSentiment: marketData?.marketSentiment || 'neutral',
          high24h: marketData?.high24h,
          low24h: marketData?.low24h,
          change24h: marketData?.change24h || 0,
          isRealData: marketData?.isRealData || false
        }
      };

      const tradingSignal = await this.tradingSignalGenerator.generatePureSignal(signalRequest);

      return {
        signal: tradingSignal.direction,
        strength: tradingSignal.strength,
        confidence: tradingSignal.confidence,
        reasoning: [tradingSignal.reasoning],
        keyFactors: tradingSignal.technicalIndicators || [],
        positionSize: tradingSignal.positionSize,
        stopLoss: null,
        takeProfit: null,
        timeHorizon: tradingSignal.timeHorizon || '短期',
        riskWarning: '基于纯净AI分析'
      };
    } catch (error) {
      this.logger.warn('⚠️ 交易信号分析失败，尝试使用其他AI提供者', { error: error instanceof Error ? error.message : String(error) });

      // 尝试使用其他AI提供者重新生成交易信号
      return this.retryTradingSignalWithAlternativeProviders(request, error);
    }
  }

  private async getRiskAssessmentAnalysis(request: InvestmentRequest & { marketData?: any }): Promise<any> {
    try {
      this.logger.info('⚠️ 开始智能风险评估分析');

      const marketData = request.marketData;
      if (!marketData?.currentPrice) {
        throw new Error(`无法获取${request.symbol}的当前价格数据，无法进行准确的风险评估`);
      }
      const currentPrice = marketData.currentPrice;

      // 构建持仓对象
      const position = request.position ? 
        RiskPosition.create(
          request.position.symbol,
          PositionType.LONG,
          request.position.quantity || 0.1,
          request.position.averagePrice || currentPrice,
          currentPrice,
          new Date()
        ) : 
        RiskPosition.create(
          request.symbol,
          PositionType.LONG,
          0.1,
          currentPrice,
          currentPrice,
          new Date()
        );

      // 现在使用真实市场数据进行风险评估
      this.logger.info('使用真实市场数据进行风险评估');

      return this.performRiskAssessmentWithData(request, position, marketData);
    } catch (error) {
      this.logger.warn('⚠️ 风险评估分析失败，尝试使用其他AI提供者', { error: error instanceof Error ? error.message : String(error) });

      // 尝试使用其他AI提供者重新生成风险评估
      return this.retryRiskAssessmentWithAlternativeProviders(request, error);
    }
  }

  /**
   * 获取带历史数据的风险评估分析（优化版本）
   */
  private async getRiskAssessmentAnalysisWithHistory(request: any, priceHistory: any[]): Promise<any> {
    try {
      this.logger.info('⚠️ 开始智能风险评估分析（带历史数据）');

      const marketData = request.marketData;
      const currentPrice = marketData.currentPrice;
      const position = request.position ?
        RiskPosition.create(
          request.position.symbol,
          PositionType.LONG,
          request.position.quantity || 0.1,
          request.position.averagePrice || currentPrice,
          currentPrice,
          new Date()
        ) :
        RiskPosition.create(
          request.symbol,
          PositionType.LONG,
          0.1,
          currentPrice,
          currentPrice,
          new Date()
        );

      return this.performRiskAssessmentWithHistory(request, position, marketData, priceHistory);
    } catch (error) {
      this.logger.error('风险评估分析失败', { error: error instanceof Error ? error.message : String(error) });
      throw new Error(`风险评估分析服务不可用: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 执行风险评估（获取历史数据）
   */
  private async performRiskAssessmentWithData(request: any, position: any, marketData: any): Promise<any> {
    // 获取历史价格数据用于风险计算
    const historicalData = await this.marketDataService.getHistoricalData({
      symbol: request.symbol,
      timeframe: '1h',
      limit: 100
    });

    // 转换历史数据为PricePoint格式
    const priceHistory = historicalData.map(data => ({
      timestamp: new Date(data.timestamp),
      open: data.open,
      high: data.high,
      low: data.low,
      close: data.close,
      volume: data.volume
    }));

    return this.performRiskAssessmentWithHistory(request, position, marketData, priceHistory);
  }

  /**
   * 执行风险评估（使用已有历史数据）
   */
  private async performRiskAssessmentWithHistory(request: any, position: any, marketData: any, priceHistory: any[]): Promise<any> {
    const riskAssessment = await this.riskAnalysisEngine.generatePureRiskAssessment({
      position: {
        symbol: request.symbol,
        quantity: position.quantity,
        currentPrice: position.currentPrice
      },
      marketData: {
        symbol: request.symbol,
        currentPrice: marketData?.currentPrice || (() => {
          throw new Error('缺少真实的当前价格数据，拒绝使用默认值');
        })(),
        volume24h: marketData?.volume24h || (() => {
          throw new Error('缺少真实的24小时交易量数据，拒绝使用默认值');
        })(),
        volatility: marketData?.volatility || 0.03, // 波动率可以有合理备用值
        trend: 'SIDEWAYS' as const,
        marketPhase: 'ACCUMULATION' as const,
        sentiment: 'NEUTRAL' as const,
        change24h: 0,
        priceHistory: priceHistory.map(p => ({
          timestamp: new Date(p.timestamp),
          price: p.price,
          volume: p.volume || 0
        })),
        marketSentiment: 'neutral' as const,
        technicalIndicators: {
          rsi: 50,
          macd: { macd: 0, signal: 0, histogram: 0 },
          bollinger: {
            upper: (marketData?.currentPrice || 0) * 1.02,
            middle: marketData?.currentPrice || 0,
            lower: (marketData?.currentPrice || 0) * 0.98
          },
          movingAverages: {
            ma20: marketData?.currentPrice || 0,
            ma50: marketData?.currentPrice || 0,
            ma200: marketData?.currentPrice || 0
          }
        },
        orderBook: {
          bids: [],
          asks: []
        },
        timestamp: new Date(),
        dataQuality: 'MEDIUM' as const,
        high24h: marketData?.high24h,
        low24h: marketData?.low24h,
        // 添加市场情绪作为扩展属性
        ...(marketData?.marketSentiment && {
          marketSentiment: typeof marketData.marketSentiment === 'string'
            ? marketData.marketSentiment
            : marketData.marketSentiment?.overall || 'neutral'
        })
      }
    });

    return {
      overallRisk: riskAssessment.riskLevel,
      riskScore: riskAssessment.riskScore,
      keyRiskFactors: riskAssessment.riskFactors,
      maxLoss: riskAssessment.maxDrawdown,
      recommendations: [riskAssessment.reasoning],
      alertThresholds: null,
      contingencyPlan: null,
      confidence: 0.8,
      reasoning: riskAssessment.reasoning
    };
  }

  private async getTrendIntelligenceAnalysis(request: InvestmentRequest & { marketData?: any }): Promise<any> {
    try {
      this.logger.info('📊 开始趋势智能分析');

      // AI3.0核心：回答"趋势如何"这个关键问题
      // 调用趋势分析应用服务获取智能分析结果
      const trendIntelligence = await this.trendAnalysisService.analyzeTrend({
        symbol: request.symbol,
        primaryTimeframe: '1h',
        analysisTimeframes: ['1h', '4h', '1d'],
        analysisDepth: request.analysisDepth === 'quick' ? 'basic' : request.analysisDepth || 'standard',
        includePatterns: true,
        includePredictions: true
      });

      return {
        direction: trendIntelligence.direction?.value || trendIntelligence.direction,
        strength: trendIntelligence.strength?.value || trendIntelligence.strength,
        persistence: trendIntelligence.persistence,
        keyLevels: trendIntelligence.keyLevels,
        reversalSignals: trendIntelligence.reversalSignals,
        timeframeAlignment: trendIntelligence.timeframeAlignment,
        insights: trendIntelligence.insights,
        recommendations: trendIntelligence.recommendations,
        riskWarnings: trendIntelligence.riskWarnings,
        confidence: trendIntelligence.confidence
      };
    } catch (error) {
      this.logger.error('趋势分析失败', { error: error instanceof Error ? error.message : String(error) });

      // 抛出真实错误，不使用默认分析
      throw new Error(`趋势分析服务失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取真实市场数据
   */
  private async getRealMarketData(symbol: string): Promise<any> {
    try {
      // 获取最新价格数据
      const priceData = await this.marketDataService.getSymbolPrice(symbol);
      
      if (!priceData) {
        throw new Error(`未找到符号 ${symbol} 的价格数据`);
      }

      // 获取历史数据用于计算技术指标
      const historicalData = await this.marketDataService.getHistoricalData({
        symbol,
        timeframe: '1h',
        limit: 100
      });

      // 计算简单的波动率
      let volatility = 0.03; // 备用值
      if (historicalData.length > 1) {
        const prices = historicalData.map(d => d.close);
        const returns = prices.slice(1).map((price, i) => (price - prices[i]) / prices[i]);
        const variance = returns.reduce((sum, ret) => sum + ret * ret, 0) / returns.length;
        volatility = Math.sqrt(variance);
      }

      return {
        currentPrice: priceData.price,
        change24h: priceData.changePercent24h,
        volume24h: priceData.volume24h,
        high24h: priceData.high24h || priceData.price,
        low24h: priceData.low24h || priceData.price,
        marketCap: priceData.marketCap || null,
        volatility,
        marketSentiment: priceData.changePercent24h > 2 ? 'bullish' : 
                        priceData.changePercent24h < -2 ? 'bearish' : 'neutral',
        technicalIndicators: await this.calculateRealTechnicalIndicators(symbol),
        timestamp: new Date(),
        isRealData: true
      };
    } catch (error) {
      this.logger.warn('⚠️ 主要数据源失败，尝试备用数据源', {
        error: error instanceof Error ? error.message : String(error)
      });

      // 尝试备用数据获取策略
      return await this.getFallbackMarketData(symbol, error);
    }
  }

  /**
   * 备用市场数据获取策略
   */
  private async getFallbackMarketData(symbol: string, originalError: any): Promise<any> {
    const fallbackStrategies = [
      () => this.getDataFromBinanceAPI(symbol),
      () => this.getDataFromCoinGeckoAPI(symbol),
      () => this.getBasicMarketData(symbol)
    ];

    for (let i = 0; i < fallbackStrategies.length; i++) {
      try {
        this.logger.info(`尝试备用数据源 ${i + 1}`, { symbol });
        const data = await fallbackStrategies[i]();

        this.logger.info(`备用数据源 ${i + 1} 成功`, { symbol, hasData: !!data });
        return {
          ...data,
          isRealData: true,
          isFallbackData: true,
          fallbackLevel: i + 1,
          originalError: originalError instanceof Error ? originalError.message : String(originalError)
        };
      } catch (fallbackError) {
        this.logger.warn(`备用数据源 ${i + 1} 失败`, {
          symbol,
          error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
        });
        continue;
      }
    }

    // 如果所有备用策略都失败，返回保守的基础数据
    this.logger.error('所有数据源都失败，使用保守的基础数据', { symbol });
    return this.getConservativeMarketData(symbol, originalError);
  }

  /**
   * 从Binance API获取数据
   */
  private async getDataFromBinanceAPI(symbol: string): Promise<any> {
    const binanceSymbol = symbol.replace('/', '').toUpperCase();
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    try {
      const response = await fetch(`https://api.binance.com/api/v3/ticker/24hr?symbol=${binanceSymbol}`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Binance API错误: ${response.status}`);
      }

      const data = await response.json() as BinanceTickerResponse;
      return {
        currentPrice: parseFloat(data.lastPrice),
        change24h: parseFloat(data.priceChangePercent),
        volume24h: parseFloat(data.volume),
        high24h: parseFloat(data.highPrice),
        low24h: parseFloat(data.lowPrice),
        volatility: Math.abs(parseFloat(data.priceChangePercent)) / 100,
        marketSentiment: parseFloat(data.priceChangePercent) > 2 ? 'bullish' :
                        parseFloat(data.priceChangePercent) < -2 ? 'bearish' : 'neutral',
        technicalIndicators: null, // 简化版本不包含技术指标
        timestamp: new Date(),
        dataSource: 'Binance'
      };
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 从CoinGecko API获取数据
   */
  private async getDataFromCoinGeckoAPI(symbol: string): Promise<any> {
    // 简化的CoinGecko API调用
    const coinId = symbol.toLowerCase().replace('/', '-');
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);

    try {
      const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true`, {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`CoinGecko API错误: ${response.status}`);
      }

      const data = await response.json();
      const coinData = data[coinId];

      if (!coinData) {
        throw new Error(`CoinGecko未找到符号: ${symbol}`);
      }

      return {
        currentPrice: coinData.usd,
        change24h: coinData.usd_24h_change || 0,
        volume24h: coinData.usd_24h_vol || 0,
        high24h: coinData.usd * 1.02, // 估算
        low24h: coinData.usd * 0.98, // 估算
        volatility: Math.abs(coinData.usd_24h_change || 0) / 100,
        marketSentiment: (coinData.usd_24h_change || 0) > 2 ? 'bullish' :
                        (coinData.usd_24h_change || 0) < -2 ? 'bearish' : 'neutral',
        technicalIndicators: null,
        timestamp: new Date(),
        dataSource: 'CoinGecko'
      };
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 获取基础市场数据（最后的备用方案）
   */
  private getBasicMarketData(symbol: string): Promise<any> {
    // 基于历史数据或缓存的基础数据
    return Promise.resolve({
      currentPrice: 0,
      change24h: 0,
      volume24h: 0,
      high24h: 0,
      low24h: 0,
      volatility: 0.02, // 假设2%的基础波动率
      marketSentiment: 'neutral',
      technicalIndicators: null,
      timestamp: new Date(),
      dataSource: 'Basic',
      note: '基础数据，仅用于系统稳定性'
    });
  }

  /**
   * 保守的市场数据（当所有数据源都失败时）
   */
  private getConservativeMarketData(symbol: string, originalError: any): any {
    return {
      currentPrice: 0,
      change24h: 0,
      volume24h: 0,
      high24h: 0,
      low24h: 0,
      volatility: 0.05, // 保守的5%波动率
      marketSentiment: 'neutral',
      technicalIndicators: null,
      timestamp: new Date(),
      isRealData: false,
      isConservativeData: true,
      dataQuality: 'low',
      originalError: originalError instanceof Error ? originalError.message : String(originalError),
      warning: '数据获取失败，使用保守估值，不建议进行交易决策'
    };
  }

  /**
   * 标准化Symbol格式
   * 将简单的币种符号（如BTC）转换为交易对格式（如BTC/USDT）
   */
  private normalizeSymbol(symbol: string): string {
    // 如果已经是交易对格式，直接返回
    if (symbol.includes('/') || symbol.includes('-')) {
      return symbol;
    }

    // 将单一币种转换为USDT交易对
    return `${symbol.toUpperCase()}/USDT`;
  }

  /**
   * 计算真实技术指标
   */
  private async calculateRealTechnicalIndicators(symbol: string): Promise<any> {
    try {
      // 获取历史价格数据用于技术指标计算
      const historicalData = await this.marketDataService.getHistoricalKlines({
        symbol: symbol,
        timeframe: '1h',
        limit: 100
      });

      if (!historicalData || historicalData.length < 20) {
        this.logger.warn('历史数据不足，返回默认技术指标');
        return {
          rsi: 50,
          macd: 'neutral',
          bollinger: 'middle'
        };
      }

      const prices = historicalData.map(candle => candle.close);
      const volumes = historicalData.map(candle => candle.volume);

      // 计算技术指标 - 使用统一技术指标计算器
      const rsiResult = this.technicalCalculator.calculateRSI(prices);
      const macdResult = this.technicalCalculator.calculateMACD(prices);
      const bollingerResult = this.technicalCalculator.calculateBollingerBands(prices);

      return {
        rsi: rsiResult.value,
        rsiSignal: rsiResult.signal,
        macd: macdResult.signalType,
        macdValue: macdResult.macd,
        bollinger: bollingerResult.position,
        bollingerBands: {
          upper: bollingerResult.upper,
          middle: bollingerResult.middle,
          lower: bollingerResult.lower,
          position: bollingerResult.position
        }
      };
    } catch (error) {
      this.logger.error('计算技术指标失败', { error });
      return {
        rsi: 50,
        macd: 'neutral',
        bollinger: 'middle'
      };
    }
  }

  // 注意：重复的RSI计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行RSI计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突

  // 注意：重复的MACD计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行MACD计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突

  // 注意：重复的布林带和EMA计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行所有技术指标计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突



  /**
   * 清理markdown中的JSON内容
   */
  private cleanJsonFromMarkdown(content: string): string {
    // 移除markdown代码块标记
    let cleaned = content.replace(/```json\s*/g, '').replace(/```\s*$/g, '');

    // 移除可能的前后空白
    cleaned = cleaned.trim();

    // 如果内容以```开头但不是```json，也要移除
    if (cleaned.startsWith('```')) {
      const firstNewline = cleaned.indexOf('\n');
      if (firstNewline !== -1) {
        cleaned = cleaned.substring(firstNewline + 1);
      }
    }

    // 移除末尾的```
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.substring(0, cleaned.length - 3);
    }

    return cleaned.trim();
  }

  /**
   * 使用其他AI提供者重试决策 - 当主要AI提供者失败时
   */
  private async retryWithAlternativeProviders(
    request: InvestmentRequest,
    tradingSignal: any,
    riskAssessment: any,
    trendIntelligence: any,
    originalError: any
  ): Promise<InvestmentDecision> {
    this.logger.info('尝试使用其他AI提供者重新生成决策');

    try {
      // 获取所有可用的AI提供者
      const availableProviders = await this.llmRouter.getAvailableProviders();
      this.logger.info('可用的AI提供者', {
        count: availableProviders.length,
        providers: availableProviders.map(p => p.name)
      });

      if (availableProviders.length === 0) {
        throw new Error('没有可用的AI提供者');
      }

      // 尝试使用不同的模型
      const alternativeModels = ['claude-sonnet-4-20250514', 'o3-mini', 'gemini-1.5-pro'];

      for (const model of alternativeModels) {
        const provider = availableProviders.find(p => p.supportedModels.includes(model));
        if (provider) {
          try {
            this.logger.info(`尝试使用替代模型: ${model}`);

            const llmRequest = {
              prompt: this.buildDecisionPrompt(request, tradingSignal, riskAssessment, trendIntelligence),
              model,
              temperature: 0.3,
              maxTokens: 2000,
              systemPrompt: `你是一个专业的投资决策专家。`,
              reasoningFramework: {
                "综合分析": "综合交易信号、风险评估和趋势分析",
                "决策制定": "基于分析结果制定投资决策",
                "风险控制": "确保决策符合风险管理原则"
              }
            };

            const response = await provider.reason(llmRequest);
            const decisionData = typeof response.content === 'string'
              ? JSON.parse(this.cleanJsonFromMarkdown(response.content))
              : response.content;

            this.logger.info(`使用替代模型 ${model} 成功生成决策`);

            return {
              action: decisionData.action || 'HOLD',
              confidence: decisionData.confidence || 0.5,
              reasoning: decisionData.reasoning || [],
              monitoringPoints: decisionData.monitoringPoints || [],
              adjustmentTriggers: decisionData.adjustmentTriggers || [],
              timestamp: new Date(),
              executionPlan: {
                positionSize: 0.1,
                timeframe: '1d',
                splitOrders: false,
                orderType: 'limit',
                executionStrategy: '保守执行'
              },
              riskControls: {
                maxDrawdown: '5%',
                positionLimit: 0.2,
                riskLevel: 'medium'
              },
              expectedReturn: {
                target: 0.05,
                timeframe: '30d',
                probability: 0.6,
                scenarios: [{
                  name: '保守场景',
                  probability: 0.6,
                  return: 0.05,
                  description: '基于AI备用分析的保守预期'
                }]
              }
            };

          } catch (modelError) {
            this.logger.warn(`模型 ${model} 也失败了`, {
              error: modelError instanceof Error ? modelError.message : String(modelError)
            });
            continue;
          }
        }
      }

      // 如果所有替代模型都失败了，抛出真实错误
      throw new Error(`所有AI提供者都不可用。原始错误: ${originalError instanceof Error ? originalError.message : String(originalError)}`);

    } catch (error) {
      this.logger.error('所有AI提供者重试失败', {
        originalError: originalError instanceof Error ? originalError.message : String(originalError),
        retryError: error instanceof Error ? error.message : String(error)
      });

      // 返回真实的错误，不提供任何虚假决策
      throw new Error(`AI决策服务完全不可用。原因: ${error instanceof Error ? error.message : String(error)}。请检查网络连接和API配置，或稍后重试。`);
    }
  }

  /**
   * 构建决策提示词
   */
  private buildDecisionPrompt(
    request: InvestmentRequest,
    tradingSignal: any,
    riskAssessment: any,
    trendIntelligence: any
  ): string {
    return `请基于以下信息制定投资决策：

交易信号分析：
${JSON.stringify(tradingSignal, null, 2)}

风险评估：
${JSON.stringify(riskAssessment, null, 2)}

趋势分析：
${JSON.stringify(trendIntelligence, null, 2)}

请返回JSON格式的投资决策：
{
  "action": "BUY|SELL|HOLD",
  "confidence": 0.85,
  "reasoning": ["原因1", "原因2"],
  "monitoringPoints": ["监控点1", "监控点2"],
  "adjustmentTriggers": ["触发条件1", "触发条件2"]
}`;
  }

  /**
   * 使用其他AI提供者重试交易信号分析
   */
  private async retryTradingSignalWithAlternativeProviders(
    request: InvestmentRequest & { marketData?: any },
    originalError: any
  ): Promise<any> {
    this.logger.info('尝试使用其他AI提供者重新生成交易信号');

    try {
      const availableProviders = await this.llmRouter.getAvailableProviders();
      if (availableProviders.length === 0) {
        throw new Error('没有可用的AI提供者');
      }

      // 尝试使用不同的模型
      const alternativeModels = ['claude-sonnet-4-20250514', 'gpt-4o-mini', 'gemini-2.0-flash'];

      for (const model of alternativeModels) {
        const provider = availableProviders.find(p => p.supportedModels.includes(model));
        if (provider) {
          try {
            this.logger.info(`尝试使用替代模型生成交易信号: ${model}`);

            // 直接调用AI服务生成交易信号
            const currentPrice = request.marketData?.currentPrice || '未知';
            const response = await provider.reason({
              prompt: `分析${request.symbol}交易信号，基于当前市场数据：价格${currentPrice}，请提供买入/卖出/持有建议。注意：如果价格数据缺失，请给出保守建议。`,
              model,
              temperature: 0.3,
              maxTokens: 1500
            });

            this.logger.info(`使用替代模型 ${model} 成功生成交易信号`);

            // 解析AI响应，不使用硬编码默认值
            const signalData = this.parseAISignalResponse(response.content);

            return {
              signal: signalData.signal,
              strength: signalData.strength,
              confidence: signalData.confidence,
              reasoning: [`使用${model}模型分析`, signalData.reasoning],
              keyFactors: signalData.keyFactors || ['AI服务重试成功'],
              positionSize: signalData.positionSize,
              stopLoss: signalData.stopLoss,
              takeProfit: signalData.takeProfit,
              timeHorizon: signalData.timeHorizon,
              riskWarning: `使用备用AI模型${model}生成`
            };

          } catch (modelError) {
            this.logger.warn(`模型 ${model} 交易信号分析也失败了`, {
              error: modelError instanceof Error ? modelError.message : String(modelError)
            });
            continue;
          }
        }
      }

      throw new Error(`所有AI提供者都不可用进行交易信号分析。原始错误: ${originalError instanceof Error ? originalError.message : String(originalError)}`);

    } catch (error) {
      this.logger.error('所有AI提供者重试交易信号分析失败', {
        originalError: originalError instanceof Error ? originalError.message : String(originalError),
        retryError: error instanceof Error ? error.message : String(error)
      });

      // 抛出真实的错误，不提供虚假信号
      throw new Error(`交易信号AI分析服务完全不可用。原因: ${error instanceof Error ? error.message : String(error)}。请检查网络连接和API配置，或稍后重试。`);
    }
  }

  /**
   * 使用其他AI提供者重试风险评估分析
   */
  private async retryRiskAssessmentWithAlternativeProviders(
    request: InvestmentRequest & { marketData?: any },
    originalError: any
  ): Promise<any> {
    this.logger.info('尝试使用其他AI提供者重新生成风险评估');

    try {
      const availableProviders = await this.llmRouter.getAvailableProviders();
      if (availableProviders.length === 0) {
        throw new Error('没有可用的AI提供者');
      }

      // 尝试使用不同的模型
      const alternativeModels = ['claude-sonnet-4-20250514', 'gpt-4o-mini', 'gemini-2.0-flash'];

      for (const model of alternativeModels) {
        const provider = availableProviders.find(p => p.supportedModels.includes(model));
        if (provider) {
          try {
            this.logger.info(`尝试使用替代模型生成风险评估: ${model}`);

            // 直接调用AI服务生成风险评估
            const currentPrice = request.marketData?.currentPrice || '未知';
            const response = await provider.reason({
              prompt: `评估${request.symbol}投资风险，基于当前市场数据：价格${currentPrice}，请提供风险评估。注意：如果价格数据缺失，请给出保守的风险评估。`,
              model,
              temperature: 0.3,
              maxTokens: 1500
            });

            this.logger.info(`使用替代模型 ${model} 成功生成风险评估`);

            // 解析AI响应以获取真实的风险评估结果
            const riskAssessment = this.parseRiskAssessmentResponse(response.content);
            if (!riskAssessment) {
              throw new Error(`AI模型 ${model} 返回的风险评估响应无法解析`);
            }

            return riskAssessment;

          } catch (modelError) {
            this.logger.warn(`模型 ${model} 风险评估分析也失败了`, {
              error: modelError instanceof Error ? modelError.message : String(modelError)
            });
            continue;
          }
        }
      }

      throw new Error(`所有AI提供者都不可用进行风险评估分析。原始错误: ${originalError instanceof Error ? originalError.message : String(originalError)}`);

    } catch (error) {
      this.logger.error('所有AI提供者重试风险评估分析失败', {
        originalError: originalError instanceof Error ? originalError.message : String(originalError),
        retryError: error instanceof Error ? error.message : String(error)
      });

      // 抛出真实的错误，不提供虚假评估
      throw new Error(`风险评估AI分析服务完全不可用。原因: ${error instanceof Error ? error.message : String(error)}。请检查网络连接和API配置，或稍后重试。`);
    }
  }

  /**
   * 解析AI信号响应
   */
  private parseAISignalResponse(content: string): any {
    try {
      const parsed = typeof content === 'string' ? JSON.parse(content) : content;

      // 验证必需字段
      if (!parsed.signal) {
        throw new Error('AI响应缺少signal字段');
      }

      return {
        signal: parsed.signal,
        strength: parsed.strength || 0.5,
        confidence: parsed.confidence || 0.5,
        reasoning: parsed.reasoning || '无推理信息',
        keyFactors: parsed.keyFactors || [],
        positionSize: parsed.positionSize || 0.1,
        stopLoss: parsed.stopLoss || null,
        takeProfit: parsed.takeProfit || null,
        timeHorizon: parsed.timeHorizon || '短期'
      };
    } catch (error) {
      throw new Error(`AI信号响应解析失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 解析AI风险评估响应
   */
  private parseRiskAssessmentResponse(content: string): any {
    try {
      const parsed = typeof content === 'string' ? JSON.parse(content) : content;

      // 验证必需字段
      if (!parsed.overallRisk) {
        throw new Error('AI响应缺少overallRisk字段');
      }

      return {
        overallRisk: parsed.overallRisk,
        riskScore: parsed.riskScore || 0.5,
        keyRiskFactors: parsed.keyRiskFactors || [],
        maxLoss: parsed.maxLoss || null,
        recommendations: parsed.recommendations || [],
        alertThresholds: parsed.alertThresholds || null,
        contingencyPlan: parsed.contingencyPlan || null,
        confidence: parsed.confidence || 0.5,
        reasoning: parsed.reasoning || '无推理信息'
      };
    } catch (error) {
      throw new Error(`AI风险评估响应解析失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

}
