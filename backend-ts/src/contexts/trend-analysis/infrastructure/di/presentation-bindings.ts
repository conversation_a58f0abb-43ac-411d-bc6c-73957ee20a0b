import { ContainerModule } from 'inversify';
import { TREND_ANALYSIS_TYPES } from './types';

/**
 * 趋势分析表现层绑定模块
 * 专门处理Controller等表现层组件的绑定
 * 避免Application层直接依赖Presentation层
 *
 * 注意：TrendAnalysisController已移至API层统一管理
 * @see src/api/controllers/trend-analysis-controller.ts
 */
export const trendAnalysisPresentationBindings = new ContainerModule((bind) => {
  // 控制器绑定已移至API层统一管理
  // 如需添加其他表现层组件，请在此处添加
});
