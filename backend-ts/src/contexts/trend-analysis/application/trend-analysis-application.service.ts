import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { Logger } from 'winston';
import { TYPES } from '../../../shared/infrastructure/di/types';
import {
  IDynamicWeightingService,
  TimeframeTrend,
  MarketCondition,
  IPatternRecognitionService,
  IMultiTimeframeService
} from '../../../shared/infrastructure/analysis';

import { Timeframe } from '../../../contexts/market-data/domain/value-objects/timeframe';
import { MarketSymbol } from '../../../contexts/market-data/domain/entities/market-symbol';
import { TradingSymbol } from '../../../contexts/market-data/domain/value-objects/trading-symbol';

import { IPureAITrendAnalysisEngine } from '../../../contexts/ai-reasoning/domain/services/pure-ai-analysis.interface';
import { ITrendPredictionService, PredictionHorizon } from '../domain/services/trend-prediction.interface';
import { ITrendAnalysisEngine } from '../domain/services/trend-analysis-engine.interface';
import { TREND_ANALYSIS_TYPES } from '../infrastructure/di/types';

// 服务工厂 - 解决循环依赖
import { LazyServiceResolver, ServiceFactoryManager } from '../../../shared/infrastructure/di/base/service-factory';
import { getContainer } from '../../../shared/infrastructure/di/container';
import { Container } from 'inversify';

// 导入基础应用服务
import { BaseApplicationService } from '../../../shared/application/services/base-application-service';
import { RequestContext, ValidationResult, BusinessRuleResult } from '../../../shared/application/interfaces/application-service';
import {
  BaseRequest,
  BaseResponse,
  BaseDTOMapper,
  IApplicationService,
  HealthCheckResult
} from '../../../shared/application/application-service-interfaces';
import { IEventBus } from '../../../shared/domain/events/domain-event';

// 来自OptimizedTrendAnalysisService的接口
export interface OptimizedTrendAnalysisRequest {
  symbol: string;
  timeframe: string;
  dataPoints?: number;
}

export interface TrendAnalysisResult {
  trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  confidence: number;
  timeframe: string;
  timestamp: Date;
}

// 趋势分析请求接口
export interface TrendAnalysisRequest extends BaseRequest {
  symbol: string;
  primaryTimeframe: string;
  analysisTimeframes?: string[];
  analysisDepth?: 'basic' | 'standard' | 'comprehensive';
  includePatterns?: boolean;
  includePredictions?: boolean;
}

// 趋势分析响应接口
export interface TrendAnalysisResponse extends BaseResponse {
  data: {
    symbol: string;
    primaryTimeframe: string;
    trend: {
      direction: string;
      strength: number;
      confidence: number;
    };
    keyLevels?: {
      support: number[];
      resistance: number[];
    };
    technicalIndicators?: {
      macd?: {
        histogram: number;
      };
      rsi?: {
        value: number;
      };
      bollinger?: any;
    };
    patterns?: any[];
    predictions?: any[];
    analysis: any;
  };
}

// 快速评估请求接口
export interface QuickAssessmentRequest extends BaseRequest {
  symbol: string;
  timeframe: string;
}

// 快速评估响应接口
export interface QuickAssessmentResponse extends BaseResponse {
  data: {
    symbol: string;
    timeframe: string;
    direction: string;
    strength: number;
    confidence: number;
    rsi: number;
    lastUpdate: Date;
  };
}

/**
 * 趋势分析应用服务 - 用例编排层
 * 专注于趋势分析用例编排、业务规则执行和结果格式化
 * 技术实现委托给纯净AI层
 *
 * 重构后继承BaseApplicationService，消除请求编排模式重复
 */
@injectable()
export class TrendAnalysisApplicationService extends BaseApplicationService implements IApplicationService {
  readonly serviceName = 'TrendAnalysisApplicationService';

  // 使用服务工厂解决循环依赖问题
  private serviceFactoryManager: ServiceFactoryManager;
  private lazyServiceResolver: LazyServiceResolver;

  // 延迟加载的服务
  private pureAITrendAnalysisEngine: any = null;
  private comprehensiveTrendAnalysisEngine: any = null;
  private patternRecognitionService: IPatternRecognitionService | null = null;
  private trendPredictionService: any = null;
  private multiTimeframeService: IMultiTimeframeService | null = null;
  private dynamicWeightingService: IDynamicWeightingService | null = null;
  private trendAnalysisService: any = null; // 添加趋势分析服务引用

  constructor(
    @inject(TYPES.Logger) logger: Logger,
    @inject(TYPES.EventBus) eventBus: IEventBus
  ) {
    super(logger, eventBus);

    // 初始化服务工厂
    const container = getContainer();
    this.serviceFactoryManager = new ServiceFactoryManager(container);
    this.lazyServiceResolver = LazyServiceResolver.getInstance(container as Container);

    // 初始化延迟服务
    this.initializeLazyServices();

    this.logger.info('趋势分析应用服务初始化完成 - 使用服务工厂解决循环依赖');
  }
  
  /**
   * 健康检查
   */
  async healthCheck(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    try {
      // 检查关键依赖服务
      const servicesStatus = {
        pureAITrendAnalysisEngine: !!this.pureAITrendAnalysisEngine,
        multiTimeframeService: !!this.multiTimeframeService,
        patternRecognitionService: !!this.patternRecognitionService,
        dynamicWeightingService: !!this.dynamicWeightingService,
        trendPredictionService: !!this.trendPredictionService,
        trendAnalysisService: !!this.trendAnalysisService
      };
      
      const availableServices = Object.values(servicesStatus).filter(Boolean).length;
      const totalServices = Object.keys(servicesStatus).length;
      const availabilityRatio = availableServices / totalServices;
      
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      let message = '趋势分析服务运行正常';
      
      if (availabilityRatio < 0.5) {
        status = 'unhealthy';
        message = '趋势分析服务大部分依赖不可用';
      } else if (availabilityRatio < 1) {
        status = 'degraded';
        message = '趋势分析服务部分依赖不可用，但仍可运行';
      }
      
      return {
        status,
        message,
        details: {
          services: servicesStatus,
          availabilityRatio
        },
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        message: `健康检查失败: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: new Date(),
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * 初始化延迟服务
   */
  private initializeLazyServices(): void {
    try {
      // 获取统一分析服务
      const container = getContainer();

      if (container.isBound(TYPES.Shared.PatternRecognitionService)) {
        this.patternRecognitionService = container.get<IPatternRecognitionService>(TYPES.Shared.PatternRecognitionService);
      }

      if (container.isBound(TYPES.Shared.MultiTimeframeService)) {
        this.multiTimeframeService = container.get<IMultiTimeframeService>(TYPES.Shared.MultiTimeframeService);
      }

      if (container.isBound(TYPES.Shared.DynamicWeightingService)) {
        this.dynamicWeightingService = container.get<IDynamicWeightingService>(TYPES.Shared.DynamicWeightingService);
      }

      // 🔥 修复：初始化PureAITrendAnalysisEngine
      if (container.isBound(TYPES.AIReasoning.PureAITrendAnalysisEngine)) {
        this.pureAITrendAnalysisEngine = container.get<IPureAITrendAnalysisEngine>(TYPES.AIReasoning.PureAITrendAnalysisEngine);
      }
      
      // 初始化趋势分析服务
      if (container.isBound(TREND_ANALYSIS_TYPES.TrendAnalysisService)) {
        this.trendAnalysisService = container.get(TREND_ANALYSIS_TYPES.TrendAnalysisService);
      }
      
      // 初始化趋势预测服务
      if (container.isBound(TREND_ANALYSIS_TYPES.TrendPredictionService)) {
        this.trendPredictionService = container.get<ITrendPredictionService>(TREND_ANALYSIS_TYPES.TrendPredictionService);
      }

      this.logger.info('延迟服务初始化完成', {
        patternRecognition: !!this.patternRecognitionService,
        multiTimeframe: !!this.multiTimeframeService,
        dynamicWeighting: !!this.dynamicWeightingService,
        pureAITrendAnalysisEngine: !!this.pureAITrendAnalysisEngine,
        trendAnalysisService: !!this.trendAnalysisService,
        trendPredictionService: !!this.trendPredictionService
      });
    } catch (error) {
      this.logger.warn('部分延迟服务初始化失败，将在运行时重试', { error });
    }
  }

  /**
   * 执行完整的趋势分析 - 使用统一请求编排模式
   */
  async analyzeTrend(request: TrendAnalysisRequest): Promise<TrendAnalysisResponse> {
    const result = await this.processRequest<TrendAnalysisRequest, TrendAnalysisResponse>(
      request,
      '完整趋势分析',
      {
        skipValidation: false,
        skipBusinessRules: false,
        skipAdditionalData: false,
        enablePerformanceTracking: true
      }
    );

    if (!result.success) {
      throw new Error(result.error || '趋势分析失败');
    }

    return result.data!;
  }

  /**
   * 快速趋势评估 - 使用统一请求编排模式
   */
  async quickTrendAssessment(request: QuickAssessmentRequest): Promise<QuickAssessmentResponse> {
    const result = await this.processRequest<QuickAssessmentRequest, QuickAssessmentResponse>(
      request,
      '快速趋势评估',
      {
        skipValidation: false,
        skipBusinessRules: true, // 快速评估跳过业务规则
        skipAdditionalData: true, // 快速评估跳过附加数据
        enablePerformanceTracking: true
      }
    );

    if (!result.success) {
      throw new Error(result.error || '快速趋势评估失败');
    }

    return result.data!;
  }

  /**
   * 获取趋势预测
   */
  async getTrendPrediction(request: {
    symbol: string;
    horizon: 'shortTerm' | 'mediumTerm' | 'longTerm';
    timeframes?: string[];
  }) {
    try {
      // 修复符号处理逻辑
      const normalizedSymbol = this.normalizeSymbolInput(request.symbol);
      const symbol = new TradingSymbol(normalizedSymbol);
      const timeframes: Timeframe[] = request.timeframes?.map(tf => new Timeframe(tf)) ??
                        (() => { throw new Error('时间框架参数缺失，请提供有效的时间框架列表'); })();

      // 🔥 实现逻辑 - 收集真实的多时间框架数据
      const horizon = request.horizon as PredictionHorizon;

      // 收集多时间框架数据 - 使用统一多时间框架服务
      let multiTimeframeData;
      try {
        multiTimeframeData = await this.multiTimeframeService.collectData({
          symbol: symbol,
          timeframes: timeframes.map(tf => tf.value),
          lookbackPeriods: new Map([
            ['15m', 100],
            ['1h', 100],
            ['4h', 100]
          ])
        });
      } catch (dataError) {
        this.logger.warn('多时间框架数据收集失败，使用简化预测', {
          symbol: symbol.symbol,
          error: dataError instanceof Error ? dataError.message : String(dataError)
        });

        // 返回简化的预测结果
        return {
          symbol: request.symbol,
          horizon: request.horizon,
          prediction: {
            direction: 'SIDEWAYS',
            strength: 5,
            confidence: 0.3,
            priceTargets: [],
            expectedDuration: '未知',
            probabilityDistribution: {},
            keyDrivers: ['数据不足'],
            riskFactors: ['历史数据缺失']
          },
          reliability: 0.3,
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          timestamp: new Date().toISOString()
        };
      }

      // 检查数据是否足够
      if (!multiTimeframeData || Object.keys(multiTimeframeData).length === 0) {
        throw new Error(`无法获取${symbol.symbol}的多时间框架数据，请检查数据源连接和符号有效性`);
      }

      const prediction = await this.trendPredictionService.predictTrend(multiTimeframeData, horizon);

      return {
        symbol: request.symbol,
        horizon: prediction.predictionHorizon,
        prediction: {
          direction: prediction.predictedDirection?.value ?? (() => { throw new Error('预测方向数据缺失'); })(),
          strength: prediction.predictedStrength?.value ?? (() => { throw new Error('预测强度数据缺失'); })(),
          confidence: prediction.confidence ?? (() => { throw new Error('预测置信度数据缺失'); })(),
          priceTargets: prediction.priceTargets ?? (() => { throw new Error('价格目标数据缺失'); })(),
          expectedDuration: prediction.expectedDuration ?? (() => { throw new Error('预期持续时间数据缺失'); })(),
          probabilityDistribution: prediction.probabilityDistribution ?? (() => { throw new Error('概率分布数据缺失'); })(),
          keyDrivers: prediction.keyDrivers ?? (() => { throw new Error('关键驱动因素数据缺失'); })(),
          riskFactors: prediction.riskFactors ?? (() => { throw new Error('风险因素数据缺失'); })()
        },
        reliability: prediction.reliability ?? (() => { throw new Error('预测可靠性数据缺失'); })(),
        validUntil: prediction.validUntil?.toISOString() ?? (() => { throw new Error('预测有效期数据缺失'); })(),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('趋势预测获取失败', {
        symbol: request.symbol,
        error: errorMessage
      });
      throw error;
    }
  }

  /**
   * 识别技术形态
   */
  async recognizePatterns(request: {
    symbol: string;
    timeframes: string[];
    patternTypes?: string[];
    minConfidence?: number;
  }) {
    try {
      const normalizedSymbol = this.normalizeSymbolInput(request.symbol);
      const symbol = new TradingSymbol(normalizedSymbol);
      const timeframes: Timeframe[] = request.timeframes.map(tf => new Timeframe(tf));

      // 收集真实的多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol: symbol,
        timeframes: timeframes.map(tf => tf.value),
        lookbackPeriods: new Map(timeframes.map(tf => [tf.value, 100]))
      });

      // 检查数据是否足够
      if (!multiTimeframeData || Object.keys(multiTimeframeData).length === 0) {
        throw new Error(`无法获取${symbol.value}的多时间框架数据进行模式识别，请检查数据源连接`);
      }

      // 从多时间框架数据中提取K线数据进行模式识别
      const allPatterns: any[] = [];

      for (const [timeframeKey, timeframeData] of multiTimeframeData.timeframeData) {
        if (timeframeData.klines && timeframeData.klines.length > 0) {
          const patterns = await this.patternRecognitionService.recognize(
            timeframeData.klines,
            undefined // 识别所有支持的形态
          );

          // 为每个形态添加时间框架信息
          const timeframePatterns = patterns.map(pattern => ({
            ...pattern,
            timeframe: timeframeKey,
            symbol: symbol.value
          }));

          allPatterns.push(...timeframePatterns);
        }
      }

      const patternResults = allPatterns;

      const patterns = [];
      for (const [timeframe, timeframePatterns] of patternResults) {
        patterns.push(...timeframePatterns.map(pattern => ({
          timeframe,
          type: pattern.type,
          name: pattern.name,
          description: pattern.description,
          confidence: pattern.confidence,
          signal: pattern.signal,
          strength: pattern.strength,
          priceTargets: pattern.priceTargets,
          tradingAdvice: pattern.tradingAdvice,
          riskLevel: pattern.riskLevel
        })));
      }

      return {
        symbol: request.symbol,
        patterns: patterns.sort((a, b) => b.confidence - a.confidence),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('技术形态识别失败', {
        symbol: request.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 生成趋势跟踪策略
   */
  async generateTrendStrategy(request: {
    symbol: string;
    userProfile?: {
      riskTolerance: 'low' | 'medium' | 'high';
      timeHorizon: 'shortTerm' | 'mediumTerm' | 'longTerm';
      experience: 'beginner' | 'intermediate' | 'advanced';
    };
  }) {
    try {
      const normalizedSymbol = this.normalizeSymbolInput(request.symbol);
      const symbol = MarketSymbol.fromSymbolString(normalizedSymbol);

      // 先进行趋势分析
      const analysisResult = await this.analyzeTrend({
        symbol: request.symbol,
        primaryTimeframe: '1h',
        analysisDepth: 'standard'
      });

      // 构建趋势智能对象（简化版）
      const trendIntelligence = {
        symbol,
        direction: { value: analysisResult.data?.trend?.direction || 'UNKNOWN' },
        strength: { value: analysisResult.data?.trend?.strength || 5 },
        confidence: analysisResult.data?.trend?.confidence || 0.5
      } as any;

      // 转换用户配置为正确的类型
      const userProfile = request.userProfile ? {
        riskTolerance: request.userProfile.riskTolerance,
        timeHorizon: request.userProfile.timeHorizon === 'shortTerm' ? PredictionHorizon.SHORT_TERM :
                    request.userProfile.timeHorizon === 'mediumTerm' ? PredictionHorizon.MEDIUM_TERM :
                    PredictionHorizon.LONG_TERM,
        experience: request.userProfile.experience
      } : undefined;

      // 生成策略
      const strategy = await this.trendPredictionService.generateTrendFollowingStrategy(
        trendIntelligence,
        userProfile
      );

      return {
        symbol: request.symbol,
        strategy: {
          name: strategy.strategyName,
          description: strategy.description,
          entryConditions: strategy.entryConditions,
          exitConditions: strategy.exitConditions,
          positionSizing: strategy.positionSizing,
          riskManagement: {
            stopLoss: strategy.stopLoss,
            takeProfit: strategy.takeProfit
          },
          expectedMetrics: {
            expectedReturn: strategy.expectedReturn,
            maxDrawdown: strategy.maxDrawdown,
            winRate: strategy.winRate,
            riskRewardRatio: strategy.riskRewardRatio
          },
          implementation: {
            steps: strategy.implementationSteps,
            monitoring: strategy.monitoringPoints,
            adjustments: strategy.adjustmentTriggers
          }
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('趋势策略生成失败', {
        symbol: request.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 获取市场状态评估
   */
  async assessMarketState(request: {
    symbol: string;
    timeframes?: string[];
  }) {
    try {
      const normalizedSymbol = this.normalizeSymbolInput(request.symbol);
      const symbol = MarketSymbol.fromSymbolString(normalizedSymbol);
      const timeframes: Timeframe[] = request.timeframes?.map(tf => new Timeframe(tf)) ??
                        (() => { throw new Error('时间框架参数缺失，请提供有效的时间框架列表'); })();

      // 委托给预测服务评估市场状态（让其自己收集数据）
      const multiTimeframeData = { symbol, timeframes } as any; // 简化的数据结构
      const marketState = await this.trendPredictionService.assessMarketState(multiTimeframeData);

      return {
        symbol: request.symbol,
        marketState: {
          regime: marketState.marketRegime,
          trendability: marketState.trendability,
          volatility: marketState.volatility,
          momentum: marketState.momentum,
          recommendations: marketState.recommendations
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('市场状态评估失败', {
        symbol: request.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  // ==================== 新增的用例编排层辅助方法 ====================

  /**
   * 标准化符号输入
   * 将单一符号（如"BTC"）转换为完整交易对（如"BTCUSDT"）
   */
  private normalizeSymbolInput(symbol: string): string {
    if (!symbol || typeof symbol !== 'string') {
      throw new Error('符号参数无效');
    }

    const normalizedSymbol = symbol.trim().toUpperCase();

    // 如果包含斜杠，转换为无斜杠格式（BTC/USDT -> BTCUSDT）
    if (normalizedSymbol.includes('/')) {
      return normalizedSymbol.replace('/', '');
    }

    // 如果已经是完整的交易对格式（如BTCUSDT），直接返回
    // 检查是否为真正的交易对格式（长度大于基础资产长度）
    if ((normalizedSymbol.endsWith('USDT') && normalizedSymbol.length > 4) ||
        (normalizedSymbol.endsWith('USD') && normalizedSymbol.length > 3) ||
        (normalizedSymbol.endsWith('BTC') && normalizedSymbol.length > 3) ||
        (normalizedSymbol.endsWith('ETH') && normalizedSymbol.length > 3)) {
      return normalizedSymbol;
    }

    // 单一符号，默认添加USDT（无斜杠格式）
    return `${normalizedSymbol}USDT`;
  }

  /**
   * 验证趋势分析请求
   */
  private validateTrendAnalysisRequest(request: any): void {
    if (!request.symbol || typeof request.symbol !== 'string') {
      throw new Error('Invalid symbol parameter');
    }
    if (!request.primaryTimeframe || typeof request.primaryTimeframe !== 'string') {
      throw new Error('Invalid primaryTimeframe parameter');
    }
  }

  /**
   * 验证快速评估请求
   */
  private validateQuickAssessmentRequest(request: any): void {
    if (!request.symbol || typeof request.symbol !== 'string') {
      throw new Error('Invalid symbol parameter');
    }
    if (!request.timeframe || typeof request.timeframe !== 'string') {
      throw new Error('Invalid timeframe parameter');
    }
  }

  /**
   * 应用趋势业务规则
   */
  private applyTrendBusinessRules(trendIntelligence: any, request: any): any {
    // 基于分析深度调整置信度
    if (request.analysisDepth === 'basic') {
      trendIntelligence.confidence = Math.max(0.3, trendIntelligence.confidence - 0.2);
    } else if (request.analysisDepth === 'comprehensive') {
      trendIntelligence.confidence = Math.min(0.95, trendIntelligence.confidence + 0.1);
    }

    // 基于时间框架数量调整强度
    const timeframeCount = request.analysisTimeframes?.length ?? 3;
    if (timeframeCount >= 5) {
      trendIntelligence.strength = Math.min(10, trendIntelligence.strength + 1);
    }

    return trendIntelligence;
  }

  /**
   * 获取技术形态（委托给专门服务）
   */
  private async getPatterns(symbol: MarketSymbol, timeframes: Timeframe[]): Promise<any[]> {
    try {
      // 正确收集多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol,
        timeframes: timeframes.map((tf: Timeframe) => tf.value),
        lookbackPeriods: new Map(timeframes.map((tf: Timeframe) => [tf.value, 100]))
      });

      const patternResults = await this.patternRecognitionService.recognizeMultiTimeframePatterns(
        multiTimeframeData,
        { symbol, timeframes }
      );
      return Array.from(patternResults.values()).flat();
    } catch (error) {
      this.logger.error('获取技术形态失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: symbol?.symbol ?? String(symbol),
        timeframes: timeframes?.map((tf: any) => tf.value ?? String(tf)) ?? []
      });
      // 返回空数组而不是抛出错误，让主流程继续
      return [];
    }
  }

  /**
   * 获取趋势预测（委托给专门服务）
   */
  private async getPredictions(symbol: MarketSymbol, timeframes: Timeframe[]): Promise<any[]> {
    try {
      // 正确收集多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol,
        timeframes: timeframes.map((tf: Timeframe) => tf.value),
        lookbackPeriods: new Map(timeframes.map((tf: Timeframe) => [tf.value, 100]))
      });

      // 转换为domain MultiTimeframeData类
      const { MultiTimeframeData: DomainMultiTimeframeData } = await import('../domain/value-objects/multi-timeframe-data');
      const domainMultiTimeframeData = new DomainMultiTimeframeData({
        symbol,
        timeframeData: multiTimeframeData.timeframeData,
        timestamp: new Date()
      });
      
      const shortTermPrediction = await this.trendPredictionService.predictTrend(
        domainMultiTimeframeData,
        PredictionHorizon.SHORT_TERM
      );
      const mediumTermPrediction = await this.trendPredictionService.predictTrend(
        domainMultiTimeframeData,
        PredictionHorizon.MEDIUM_TERM
      );

      return [shortTermPrediction, mediumTermPrediction];
    } catch (error) {
      this.logger.error('获取趋势预测失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: symbol?.symbol ?? String(symbol),
        timeframes: timeframes?.map((tf: any) => tf.value ?? String(tf)) ?? []
      });
      // 返回空数组而不是抛出错误，让主流程继续
      return [];
    }
  }

  /**
   * 格式化趋势分析响应
   */
  private formatTrendAnalysisResponse(
    request: any,
    trendIntelligence: any,
    patterns: any[],
    predictions: any[]
  ): any {
    return {
      symbol: request.symbol,
      timestamp: new Date().toISOString(),
      trendAnalysis: {
        direction: trendIntelligence.trend,
        strength: trendIntelligence.strength,
        confidence: trendIntelligence.confidence,
        duration: trendIntelligence.duration,
        keyLevels: trendIntelligence.keyLevels ?? [],
        reasoning: trendIntelligence.reasoning
      },
      patterns: patterns.map(pattern => ({
        type: pattern.type,
        confidence: pattern.confidence,
        timeframe: pattern.timeframe,
        description: pattern.description ?? '',
        implications: pattern.implications ?? []
      })),
      predictions: predictions.map(prediction => ({
        horizon: prediction.predictionHorizon,
        direction: prediction.predictedDirection?.value ?? 'UNKNOWN',
        strength: prediction.predictedStrength?.value ?? 5,
        confidence: prediction.confidence,
        priceTargets: prediction.priceTargets ?? [],
        expectedDuration: prediction.expectedDuration
      })),
      metadata: {
        analysisDepth: request.analysisDepth ?? 'standard',
        dataQuality: 0.9, // 简化
        marketCondition: 'normal', // 简化
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * 格式化快速评估响应
   */
  private formatQuickAssessmentResponse(request: any, assessment: any): any {
    return {
      symbol: request.symbol,
      timeframe: request.timeframe,
      direction: assessment.trend,
      strength: assessment.strength,
      confidence: assessment.confidence,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 执行优化的趋势分析（来自OptimizedTrendAnalysisService）
   * 提供高性能的简化趋势分析
   */
  async analyzeOptimizedTrend(request: OptimizedTrendAnalysisRequest): Promise<TrendAnalysisResult> {
    this.logger.info('开始执行优化趋势分析', {
      symbol: request.symbol,
      timeframe: request.timeframe
    });

    try {
      // 使用现有的趋势分析引擎进行快速分析
      const normalizedSymbol = this.normalizeSymbolInput(request.symbol);
      const symbol = MarketSymbol.fromSymbolString(normalizedSymbol);
      const timeframe = new Timeframe(request.timeframe);

      // 收集多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol: symbol.symbol,
        timeframes: [timeframe.value],
        lookbackPeriods: new Map([[timeframe.value, 100]])
      });

      const analysisResult = await this.pureAITrendAnalysisEngine.generatePureTrendAnalysis({
        multiTimeframeData,
        query: `快速分析${symbol.symbol}在${timeframe.value}时间框架的趋势`
      });

      // 验证分析结果完整性
      if (!analysisResult.confidence) {
        throw new Error('趋势分析结果缺少置信度数据');
      }

      // 转换为优化结果格式
      const result: TrendAnalysisResult = {
        trend: this.mapTrendDirection(analysisResult.trend),
        confidence: analysisResult.confidence,
        timeframe: request.timeframe,
        timestamp: new Date()
      };

      this.logger.info('优化趋势分析完成', { result });
      return result;
    } catch (error) {
      this.logger.error('优化趋势分析失败', { error, request });

      // 返回默认结果
      return {
        trend: 'SIDEWAYS',
        confidence: 0.5,
        timeframe: request.timeframe,
        timestamp: new Date()
      };
    }
  }

  /**
   * 映射趋势方向
   */
  private mapTrendDirection(direction?: string): 'BULLISH' | 'BEARISH' | 'SIDEWAYS' {
    if (!direction) return 'SIDEWAYS';

    switch (direction.toLowerCase()) {
      case 'up':
      case 'bullish':
      case 'uptrend':
        return 'BULLISH';
      case 'down':
      case 'bearish':
      case 'downtrend':
        return 'BEARISH';
      default:
        return 'SIDEWAYS';
    }
  }

  /**
   * 获取支持的时间框架
   */
  getSupportedTimeframes(): string[] {
    return ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
  }

  /**
   * 获取支持的分析类型
   */
  getSupportedAnalysisTypes(): string[] {
    return ['trend', 'pattern', 'prediction', 'multi-timeframe'];
  }

  // 健康检查已移至统一健康检查聚合器 - 避免重复实现

  /**
   * 验证请求
   */
  protected async validateRequest<T>(request: T, context: any): Promise<any> {
    return { isValid: true, errors: [] };
  }

  /**
   * 转换为领域对象
   */
  protected convertToDomainObjects(request: any): any {
    return request;
  }

  /**
   * 执行核心业务逻辑
   */
  protected async executeCoreBusinessLogic(domainObjects: any): Promise<any> {
    return domainObjects;
  }

  /**
   * 应用业务规则
   */
  protected applyBusinessRules(result: any): any {
    return result;
  }

  /**
   * 丰富额外数据
   */
  protected async enrichWithAdditionalData(result: any): Promise<any> {
    return result;
  }

  /**
   * 格式化响应
   */
  protected formatResponse(result: any): any {
    return result;
  }

  /**
   * 多时间框架分析
   * 修复CRIT-003：提供多时间框架分析方法
   */
  async analyzeMultiTimeframe(request: {
    symbol: string;
    timeframes: string[];
    analysisDepth?: 'basic' | 'standard' | 'comprehensive';
  }): Promise<{
    symbol: string;
    timeframes: Array<{
      timeframe: string;
      trend: {
        direction: string;
        strength: number;
        confidence: number;
      };
      rsi: number;
      volume: number;
      lastUpdate: Date;
    }>;
    overallTrend: {
      direction: string;
      strength: number;
      confidence: number;
    };
    metadata: {
      analysisDepth: string;
      lastUpdate: Date;
    };
  }> {
    try {
      this.logger.info('开始多时间框架分析', {
        symbol: request.symbol,
        timeframes: request.timeframes
      });

      const timeframeAnalyses = [];

      // 并行处理时间框架分析，添加超时控制
      const analysisPromises = request.timeframes.map(async (timeframe) => {
        try {
          // 添加超时控制，避免长时间等待
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('分析超时')), 30000); // 30秒超时
          });

          const analysisPromise = this.analyzeTrend({
            symbol: request.symbol,
            primaryTimeframe: timeframe,
            analysisDepth: request.analysisDepth ?? (() => { throw new Error('分析深度参数缺失'); })()
          });

          const analysisResult = await Promise.race([analysisPromise, timeoutPromise]);

          return {
            timeframe,
            trend: {
              direction: (analysisResult as any).trend.direction,
              strength: (analysisResult as any).trend.strength,
              confidence: (analysisResult as any).trend.confidence
            },
            rsi: (() => {
              const rsiValue = (analysisResult as any).technicalIndicators?.rsi?.value;
              if (rsiValue === undefined || rsiValue === null) {
                throw new Error('技术分析结果缺少RSI指标数据');
              }
              return rsiValue;
            })(),
            volume: await this.getRealVolumeData(request.symbol, timeframe),
            lastUpdate: new Date()
          };
        } catch (error) {
          this.logger.error(`时间框架 ${timeframe} 分析失败，跳过该时间框架`, {
            timeframe,
            error: error instanceof Error ? error.message : String(error)
          });
          // 不返回默认数据，直接跳过失败的时间框架
          return null;
        }
      });

      // 等待所有分析完成，过滤掉null值
      const analysisResults = await Promise.all(analysisPromises);
      const validAnalyses = analysisResults.filter(result => result !== null);
      timeframeAnalyses.push(...validAnalyses);

      // 计算整体趋势
      const directions = timeframeAnalyses.map(t => t.trend.direction);
      const avgStrength = timeframeAnalyses.reduce((sum, t) => sum + t.trend.strength, 0) / timeframeAnalyses.length;
      const avgConfidence = timeframeAnalyses.reduce((sum, t) => sum + t.trend.confidence, 0) / timeframeAnalyses.length;

      // 确定主导趋势
      const trendCounts = directions.reduce((acc, dir) => {
        if (!dir) {
          throw new Error('趋势方向数据缺失，无法统计趋势分布');
        }
        acc[dir] = (acc[dir] ?? 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const dominantTrend = Object.entries(trendCounts)
        .sort(([,a], [,b]) => (b as number) - (a as number))[0][0];

      return {
        symbol: request.symbol,
        timeframes: timeframeAnalyses,
        overallTrend: {
          direction: dominantTrend,
          strength: avgStrength,
          confidence: avgConfidence
        },
        metadata: {
          analysisDepth: request.analysisDepth ?? (() => { throw new Error('元数据中分析深度参数缺失'); })(),
          lastUpdate: new Date()
        }
      };
    } catch (error) {
      throw new Error(`多时间框架分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 计算技术指标
   * 修复CRIT-003：提供技术指标计算方法
   */
  async calculateTechnicalIndicators(request: {
    symbol: string;
    timeframe: string;
    indicators: string[];
  }): Promise<{
    symbol: string;
    timeframe: string;
    indicators: Record<string, {
      value: number;
      signal: string;
      confidence: number;
    }>;
    summary: {
      overallSignal: string;
      confidence: number;
    };
    metadata: {
      calculatedAt: Date;
    };
  }> {
    try {
      // 执行趋势分析以获取技术指标
      const analysisResult = await this.analyzeTrend({
        symbol: request.symbol,
        primaryTimeframe: request.timeframe,
        analysisDepth: 'standard'
      });

      const calculatedIndicators: Record<string, { value: number; signal: string; confidence: number; }> = {};

      // 处理请求的指标
      for (const indicator of request.indicators) {
        switch (indicator.toUpperCase()) {
          case 'RSI':
            const rsiValue = analysisResult.data?.technicalIndicators?.rsi?.value;
            if (rsiValue === undefined || rsiValue === null) {
              throw new Error('技术分析结果缺少RSI指标数据');
            }
            calculatedIndicators.RSI = {
              value: rsiValue,
              signal: rsiValue > 70 ? 'SELL' : rsiValue < 30 ? 'BUY' : 'HOLD',
              confidence: 0.8
            };
            break;
          case 'MACD':
            const macdValue = analysisResult.data?.technicalIndicators?.macd?.histogram;
            if (macdValue === undefined || macdValue === null) {
              throw new Error('技术分析结果缺少MACD指标数据');
            }
            calculatedIndicators.MACD = {
              value: macdValue,
              signal: macdValue > 0 ? 'BUY' : macdValue < 0 ? 'SELL' : 'HOLD',
              confidence: 0.7
            };
            break;
          case 'BOLLINGER':
            calculatedIndicators.BOLLINGER = {
              value: 0.5, // 相对位置
              signal: 'HOLD',
              confidence: 0.6
            };
            break;
          default:
            calculatedIndicators[indicator] = {
              value: 0,
              signal: 'HOLD',
              confidence: 0.5
            };
        }
      }

      // 计算整体信号
      const signals = Object.values(calculatedIndicators).map(ind => ind.signal);
      const buyCount = signals.filter(s => s === 'BUY').length;
      const sellCount = signals.filter(s => s === 'SELL').length;

      let overallSignal = 'HOLD';
      if (buyCount > sellCount) overallSignal = 'BUY';
      else if (sellCount > buyCount) overallSignal = 'SELL';

      const avgConfidence = Object.values(calculatedIndicators)
        .reduce((sum, ind) => sum + ind.confidence, 0) / Object.values(calculatedIndicators).length;

      return {
        symbol: request.symbol,
        timeframe: request.timeframe,
        indicators: calculatedIndicators,
        summary: {
          overallSignal,
          confidence: avgConfidence
        },
        metadata: {
          calculatedAt: new Date()
        }
      };
    } catch (error) {
      throw new Error(`技术指标计算失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 时间框架分析
   * 修复CRIT-003：提供时间框架分析方法
   */
  async analyzeTimeframes(request: {
    symbol: string;
    timeframes: string[];
  }): Promise<{
    symbol: string;
    analysis: Array<{
      timeframe: string;
      trend: string;
      strength: number;
      support: number;
      resistance: number;
      volume: number;
    }>;
    summary: {
      dominantTrend: string;
      consistency: number;
      reliability: number;
    };
    metadata: {
      analyzedAt: Date;
    };
  }> {
    try {
      const timeframeAnalyses = [];

      for (const timeframe of request.timeframes) {
        try {
          // 为每个时间框架执行分析
          const analysisResult = await this.analyzeTrend({
            symbol: request.symbol,
            primaryTimeframe: timeframe,
            analysisDepth: 'standard'
          });

          // 获取真实的成交量数据
          const realVolume = await this.getRealVolumeData(request.symbol, timeframe);

          timeframeAnalyses.push({
            timeframe,
            trend: analysisResult.data?.trend?.direction || 'UNKNOWN',
            strength: analysisResult.data?.trend?.strength || 5,
            support: (() => {
              const support = analysisResult.data?.keyLevels?.support || [];
              if (support === undefined || support === null) {
                throw new Error('技术分析结果缺少支撑位数据');
              }
              return support;
            })(),
            resistance: (() => {
              const resistance = analysisResult.data?.keyLevels?.resistance || [];
              if (resistance === undefined || resistance === null) {
                throw new Error('技术分析结果缺少阻力位数据');
              }
              return resistance;
            })(),
            volume: realVolume
          });
        } catch (error) {
          // 记录错误但不添加虚假默认数据
          this.logger.error(`时间框架 ${timeframe} 分析失败`, {
            symbol: request.symbol,
            timeframe,
            error: error instanceof Error ? error.message : String(error)
          });
          // 跳过失败的时间框架，不添加虚假数据
        }
      }

      // 检查是否有有效的分析结果
      if (timeframeAnalyses.length === 0) {
        this.logger.error('多时间框架分析失败：所有时间框架都无法获取有效数据', {
          symbol: request.symbol,
          timeframes: request.timeframes
        });
        throw new Error(`多时间框架分析失败：无法获取符号 ${request.symbol} 在任何时间框架的有效市场数据。请检查数据源或稍后重试。`);
      }

      // 计算主导趋势 - 基于真实分析结果
      const trends = timeframeAnalyses.map(t => t.trend);
      const trendCounts = trends.reduce((acc, trend) => {
        if (!trend) {
          throw new Error('趋势数据缺失，无法统计趋势分布');
        }
        acc[trend] = (acc[trend] ?? 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const dominantTrend = Object.entries(trendCounts)
        .sort(([,a], [,b]) => (b as number) - (a as number))[0][0];

      // 计算一致性 - 基于实际分析结果的一致性
      const dominantCount = trendCounts[dominantTrend];
      const consistency = dominantCount / trends.length;

      // 计算可靠性 - 基于平均强度和数据质量
      const avgStrength = timeframeAnalyses.reduce((sum, t) => sum + t.strength, 0) / timeframeAnalyses.length;
      const dataQuality = timeframeAnalyses.length / request.timeframes.length; // 成功分析的比例
      const reliability = Math.min(avgStrength / 10, 1) * dataQuality; // 考虑数据质量

      return {
        symbol: request.symbol,
        analysis: timeframeAnalyses,
        summary: {
          dominantTrend,
          consistency,
          reliability
        },
        metadata: {
          analyzedAt: new Date()
        }
      };
    } catch (error) {
      throw new Error(`时间框架分析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

/**
 * 优化趋势分析服务（向后兼容）
 * @deprecated 使用 TrendAnalysisApplicationService.analyzeOptimizedTrend 替代
 */
@injectable()
export class OptimizedTrendAnalysisService {
  private readonly trendAnalysisService: TrendAnalysisApplicationService;
  private readonly logger: IBasicLogger;
  private readonly pureAITrendAnalysisEngine: IPureAITrendAnalysisEngine;
  private readonly patternRecognitionService: IPatternRecognitionService;
  private readonly multiTimeframeService: IMultiTimeframeService;
  private readonly dynamicWeightingService: IDynamicWeightingService;
  private readonly serviceName = 'OptimizedTrendAnalysisService';

  constructor(
    @inject(TYPES.Logger) logger: IBasicLogger
  ) {
    // 🔥 修复：使用LazyServiceResolver解决循环依赖问题
    this.logger = logger;
    // 注意：这里应该通过依赖注入获取服务，而不是直接创建实例
    // 暂时注释掉，避免循环依赖
    // this.trendAnalysisService = new TrendAnalysisApplicationService(logger as Logger, eventBus);
    
    // 初始化依赖服务
    this.initializeDependentServices();

    logger.info('OptimizedTrendAnalysisService 初始化完成 - 已启用依赖服务');
  }

  /**
   * 初始化依赖服务
   */
  /**
   * 标准化交易对符号输入
   * @param symbol 输入的交易对符号
   * @returns 标准化后的交易对符号
   */
  private normalizeSymbolInput(symbol: string): string {
    if (!symbol || typeof symbol !== 'string') {
      throw new Error('符号参数无效');
    }

    const normalizedSymbol = symbol.trim().toUpperCase();

    // 如果包含斜杠，转换为无斜杠格式（BTC/USDT -> BTCUSDT）
    if (normalizedSymbol.includes('/')) {
      return normalizedSymbol.replace('/', '');
    }

    // 如果已经是完整的交易对格式（如BTCUSDT），直接返回
    // 检查是否为真正的交易对格式（长度大于基础资产长度）
    if ((normalizedSymbol.endsWith('USDT') && normalizedSymbol.length > 4) ||
        (normalizedSymbol.endsWith('USD') && normalizedSymbol.length > 3) ||
        (normalizedSymbol.endsWith('BTC') && normalizedSymbol.length > 3) ||
        (normalizedSymbol.endsWith('ETH') && normalizedSymbol.length > 3)) {
      return normalizedSymbol;
    }

    // 单一符号，默认添加USDT（无斜杠格式）
    return `${normalizedSymbol}USDT`;
  }

  /**
   * 映射趋势方向到标准格式
   * @param trend 趋势方向
   * @returns 标准化的趋势方向
   */
  private mapTrendDirection(direction?: string): 'BULLISH' | 'BEARISH' | 'SIDEWAYS' {
    if (!direction) return 'SIDEWAYS';

    switch (direction.toLowerCase()) {
      case 'up':
      case 'bullish':
      case 'uptrend':
        return 'BULLISH';
      case 'down':
      case 'bearish':
      case 'downtrend':
        return 'BEARISH';
      default:
        return 'SIDEWAYS';
    }
  }

  private initializeDependentServices(): void {
    try {
      const container = getContainer();
      const lazyServiceResolver = LazyServiceResolver.getInstance(container);

      // 使用LazyServiceResolver解决循环依赖问题
      if (container.isBound(TYPES.AIReasoning.PureAITrendAnalysisEngine)) {
        this.pureAITrendAnalysisEngine = container.get<IPureAITrendAnalysisEngine>(TYPES.AIReasoning.PureAITrendAnalysisEngine);
      }

      if (container.isBound(TYPES.Shared.PatternRecognitionService)) {
        this.patternRecognitionService = container.get<IPatternRecognitionService>(TYPES.Shared.PatternRecognitionService);
      }

      if (container.isBound(TYPES.Shared.MultiTimeframeService)) {
        this.multiTimeframeService = container.get<IMultiTimeframeService>(TYPES.Shared.MultiTimeframeService);
      }

      if (container.isBound(TYPES.Shared.DynamicWeightingService)) {
        this.dynamicWeightingService = container.get<IDynamicWeightingService>(TYPES.Shared.DynamicWeightingService);
      }

      this.logger.info('依赖服务初始化完成', {
        pureAITrendAnalysisEngine: !!this.pureAITrendAnalysisEngine,
        patternRecognition: !!this.patternRecognitionService,
        multiTimeframe: !!this.multiTimeframeService,
        dynamicWeighting: !!this.dynamicWeightingService
      });
    } catch (error) {
      this.logger.warn('部分依赖服务初始化失败，将使用TrendAnalysisApplicationService作为后备', { error });
    }
  }

  /**
   * 执行优化的趋势分析（兼容接口）
   */
  async analyzeTrend(request: OptimizedTrendAnalysisRequest): Promise<TrendAnalysisResult> {
    this.logger.info('开始执行优化趋势分析', {
      symbol: request.symbol,
      timeframe: request.timeframe
    });

    try {
      // 检查依赖服务是否可用
      if (!this.pureAITrendAnalysisEngine || !this.multiTimeframeService) {
        this.logger.warn('依赖服务不可用，委托给TrendAnalysisApplicationService');
        return this.trendAnalysisService.analyzeOptimizedTrend(request);
      }

      // 使用现有的趋势分析引擎进行快速分析
      const normalizedSymbol = this.normalizeSymbolInput(request.symbol);
      const symbol = MarketSymbol.fromSymbolString(normalizedSymbol);
      const timeframe = new Timeframe(request.timeframe);

      // 收集多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol: symbol.symbol,
        timeframes: [timeframe.value],
        lookbackPeriods: new Map([[timeframe.value, 100]])
      });

      const analysisResult = await this.pureAITrendAnalysisEngine.generatePureTrendAnalysis({
        multiTimeframeData,
        query: `快速分析${symbol.symbol}在${timeframe.value}时间框架的趋势`
      });

      // 验证分析结果完整性
      if (!analysisResult.confidence) {
        throw new Error('趋势分析结果缺少置信度数据');
      }

      // 转换为优化结果格式
      const result: TrendAnalysisResult = {
        trend: this.mapTrendDirection(analysisResult.trend),
        confidence: analysisResult.confidence,
        timeframe: request.timeframe,
        timestamp: new Date()
      };

      this.logger.info('优化趋势分析完成', { result });
      return result;
    } catch (error) {
      this.logger.error('优化趋势分析失败', { error, request });

      // 尝试使用TrendAnalysisApplicationService作为后备
      if (this.trendAnalysisService) {
        try {
          return await this.trendAnalysisService.analyzeOptimizedTrend(request);
        } catch (backupError) {
          this.logger.error('后备分析也失败', { backupError });
        }
      } else {
        this.logger.warn('后备趋势分析服务不可用');
      }
      
      // 返回默认结果
      return {
        trend: 'SIDEWAYS',
        confidence: 0.5,
        timeframe: request.timeframe,
        timestamp: new Date()
      };
    }
  }

  // 已删除重复的normalizeSymbolInput方法

  /**
   * 获取技术形态（委托给专门服务）
   */
  private async getPatterns(symbol: any, timeframes: any[]): Promise<any[]> {
    try {
      // 正确收集多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol,
        timeframes: timeframes.map((tf: any) => tf.value),
        lookbackPeriods: new Map(timeframes.map((tf: any) => [tf.value, 100]))
      });

      const patternResults = await this.patternRecognitionService.recognizeMultiTimeframePatterns(
        multiTimeframeData,
        { symbol, timeframes }
      );
      return Array.from(patternResults.values()).flat();
    } catch (error) {
      this.logger.error('获取技术形态失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: symbol?.symbol ?? String(symbol),
        timeframes: timeframes?.map((tf: any) => tf.value ?? String(tf)) ?? []
      });
      // 返回空数组而不是抛出错误，让主流程继续
      return [];
    }
  }

  /**
   * 获取趋势预测（委托给专门服务）
   */
  private async getPredictions(symbol: MarketSymbol, timeframes: Timeframe[]): Promise<any[]> {
    try {
      // 正确收集多时间框架数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol,
        timeframes: timeframes.map((tf: Timeframe) => tf.value),
        lookbackPeriods: new Map(timeframes.map((tf: Timeframe) => [tf.value, 100]))
      });

      // 如果趋势预测服务可用，则使用它
      if (this.trendPredictionService) {
        return await this.trendPredictionService.predictTrends(multiTimeframeData, { symbol, timeframes });
      }
      
      this.logger.warn('趋势预测服务不可用，无法生成预测');
      return [];
    } catch (error) {
      this.logger.error('获取趋势预测失败', {
        error: error instanceof Error ? error.message : String(error),
        symbol: symbol?.symbol ?? String(symbol),
        timeframes: timeframes?.map((tf: Timeframe) => tf.value ?? String(tf)) ?? []
      });
      // 返回空数组而不是抛出错误，让主流程继续
      return [];
    }
  }

  /**
   * 计算整体趋势 - 使用统一的动态权重分配服务
   */
  private async calculateOverallTrend(timeframeAnalyses: any[]): Promise<{
    direction: string;
    strength: number;
    confidence: number;
  }> {
    if (timeframeAnalyses.length === 0) {
      throw new Error('无法计算整体趋势：所有时间框架分析都失败');
    }

    try {
      // 转换为统一的趋势数据格式
      const trends: TimeframeTrend[] = timeframeAnalyses.map(analysis => ({
        timeframe: analysis.timeframe,
        direction: this.mapTrendDirection(analysis.trend.direction),
        strength: (() => {
          const strength = analysis.trend.strength;
          if (strength === undefined || strength === null) {
            throw new Error(`时间框架${analysis.timeframe}的趋势分析缺少强度数据`);
          }
          return strength;
        })(),
        confidence: (() => {
          const confidence = analysis.trend.confidence;
          if (confidence === undefined || confidence === null) {
            throw new Error(`时间框架${analysis.timeframe}的趋势分析缺少置信度数据`);
          }
          return confidence;
        })(),
        volume: (() => {
          const volume = analysis.volume;
          if (volume === undefined || volume === null) {
            throw new Error(`时间框架${analysis.timeframe}的趋势分析缺少成交量数据`);
          }
          return volume;
        })(),
        momentum: this.calculateMomentum(analysis.trend.direction, analysis.trend.strength),
        timestamp: new Date()
      }));

      // 构造市场条件
      const marketCondition: MarketCondition = {
        type: this.determineMarketType(timeframeAnalyses),
        volatility: this.calculateVolatility(timeframeAnalyses),
        volume: this.calculateAverageVolume(timeframeAnalyses),
        trend: this.getDominantTrend(timeframeAnalyses),
        strength: this.calculateOverallStrength(timeframeAnalyses)
      };

      // 使用动态权重分配服务
      const weightingResult = await this.dynamicWeightingService.allocate(trends, marketCondition);

      // 使用分配的权重计算整体趋势
      let weightedBullish = 0;
      let weightedBearish = 0;
      let totalWeight = 0;

      timeframeAnalyses.forEach(analysis => {
        const weight = weightingResult.weights[analysis.timeframe];
        if (weight === undefined || weight === null) {
          throw new Error(`动态权重分配缺少时间框架${analysis.timeframe}的权重数据`);
        }
        totalWeight += weight;

        if (analysis.trend.direction === 'UPTREND') {
          weightedBullish += weight * analysis.trend.strength;
        } else if (analysis.trend.direction === 'DOWNTREND') {
          weightedBearish += weight * analysis.trend.strength;
        }
      });

      const direction = weightedBullish > weightedBearish ? 'UPTREND' :
                       weightedBearish > weightedBullish ? 'DOWNTREND' : 'SIDEWAYS';

      const strength = Math.min(10, Math.max(1, Math.abs(weightedBullish - weightedBearish) / totalWeight));
      const confidence = Math.min(1, strength / 10 * 0.8 + weightingResult.confidence * 0.2);

      this.logger.debug('整体趋势计算完成', {
        direction,
        strength,
        confidence,
        weightingStrategy: weightingResult.strategy,
        weightingConfidence: weightingResult.confidence
      });

      return { direction, strength, confidence };

    } catch (error) {
      this.logger.error('动态权重计算失败', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`动态权重计算失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取真实的成交量数据
   */
  private async getRealVolumeData(symbol: string, timeframe: string): Promise<number> {
    try {
      // 标准化符号格式
      const normalizedSymbol = this.normalizeSymbolInput(symbol);

      // 收集最近的市场数据 - 使用统一多时间框架服务
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol: new TradingSymbol(normalizedSymbol),
        timeframes: [new Timeframe(timeframe)],
        lookbackPeriods: new Map([[timeframe, 24]]) // 获取最近24个数据点
      });

      const timeframeData = multiTimeframeData.timeframeData.get(timeframe);
      if (!timeframeData || timeframeData.klines.length === 0) {
        throw new Error(`无法获取${symbol}在${timeframe}时间框架的成交量数据，请检查数据源连接`);
      }

      // 计算最近24小时的平均成交量
      const recentKlines = timeframeData.klines.slice(-24);
      const totalVolume = recentKlines.reduce((sum, kline) => sum + kline.volume, 0);
      const avgVolume = totalVolume / recentKlines.length;

      this.logger.debug('获取到真实成交量数据', {
        symbol,
        timeframe,
        avgVolume,
        dataPoints: recentKlines.length
      });

      return avgVolume;

    } catch (error) {
      this.logger.warn('主要成交量数据源失败，尝试备用方案', {
        symbol,
        timeframe,
        error: error instanceof Error ? error.message : String(error)
      });

      // 尝试备用成交量数据获取策略
      return await this.getFallbackVolumeData(symbol, timeframe, error);
    }
  }

  /**
   * 备用成交量数据获取策略
   */
  private async getFallbackVolumeData(symbol: string, timeframe: string, originalError: any): Promise<number> {
    const fallbackStrategies = [
      () => this.getVolumeFromBinanceAPI(symbol),
      () => this.getVolumeFromCoinGeckoAPI(symbol),
      () => this.getEstimatedVolumeData(symbol, timeframe)
    ];

    for (let i = 0; i < fallbackStrategies.length; i++) {
      try {
        this.logger.info(`尝试备用成交量数据源 ${i + 1}`, { symbol, timeframe });
        const volume = await fallbackStrategies[i]();

        if (volume > 0) {
          this.logger.info(`备用成交量数据源 ${i + 1} 成功`, { symbol, timeframe, volume });
          return volume;
        }
      } catch (fallbackError) {
        this.logger.warn(`备用成交量数据源 ${i + 1} 失败`, {
          symbol,
          timeframe,
          error: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
        });
        continue;
      }
    }

    // 如果所有备用策略都失败，返回保守的估算值
    this.logger.error('所有成交量数据源都失败，使用保守估算值', {
      symbol,
      timeframe,
      originalError: originalError instanceof Error ? originalError.message : String(originalError)
    });

    return this.getConservativeVolumeEstimate(symbol, timeframe);
  }

  /**
   * 从Binance API获取成交量数据
   */
  private async getVolumeFromBinanceAPI(symbol: string): Promise<number> {
    try {
      const binanceSymbol = symbol.replace('/', '').replace('-', '').toUpperCase();
      const response = await fetch(`https://api.binance.com/api/v3/ticker/24hr?symbol=${binanceSymbol}`, {
        timeout: 5000
      });

      if (!response.ok) {
        throw new Error(`Binance API错误: ${response.status}`);
      }

      const data = await response.json();
      const volume = parseFloat(data.volume);

      if (isNaN(volume) || volume <= 0) {
        throw new Error('Binance返回无效成交量数据');
      }

      return volume;
    } catch (error) {
      this.logger.error('Binance API成交量获取失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 从CoinGecko API获取成交量数据
   */
  private async getVolumeFromCoinGeckoAPI(symbol: string): Promise<number> {
    try {
      // 简化的CoinGecko API调用
      const coinId = symbol.toLowerCase().replace('/', '-').replace('usdt', 'usd');
      const response = await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${coinId}&vs_currencies=usd&include_24hr_vol=true`, {
        timeout: 5000
      });

      if (!response.ok) {
        throw new Error(`CoinGecko API错误: ${response.status}`);
      }

      const data = await response.json();
      const coinData = data[coinId];

      if (!coinData || !coinData.usd_24h_vol) {
        throw new Error(`CoinGecko未找到符号: ${symbol}`);
      }

      const volume = coinData.usd_24h_vol;
      if (isNaN(volume) || volume <= 0) {
        throw new Error('CoinGecko返回无效成交量数据');
      }

      return volume;
    } catch (error) {
      this.logger.error('CoinGecko API成交量获取失败', { symbol, error });
      throw error;
    }
  }

  /**
   * 获取估算的成交量数据（基于历史模式）
   */
  private async getEstimatedVolumeData(symbol: string, timeframe: string): Promise<number> {
    try {
      // 基于符号类型和时间框架的估算逻辑
      const baseVolume = this.getBaseVolumeBySymbol(symbol);
      const timeframeMultiplier = this.getTimeframeMultiplier(timeframe);

      const estimatedVolume = baseVolume * timeframeMultiplier;

      this.logger.info('使用估算成交量数据', {
        symbol,
        timeframe,
        baseVolume,
        timeframeMultiplier,
        estimatedVolume
      });

      return estimatedVolume;
    } catch (error) {
      this.logger.error('成交量估算失败', { symbol, timeframe, error });
      throw error;
    }
  }

  /**
   * 获取保守的成交量估算值
   */
  private getConservativeVolumeEstimate(symbol: string, timeframe: string): number {
    // 基于符号的保守估算
    const baseVolume = this.getBaseVolumeBySymbol(symbol);
    const conservativeMultiplier = 0.5; // 保守系数

    const conservativeVolume = baseVolume * conservativeMultiplier;

    this.logger.warn('使用保守成交量估算值', {
      symbol,
      timeframe,
      conservativeVolume,
      note: '数据质量较低，建议谨慎使用'
    });

    return conservativeVolume;
  }

  /**
   * 根据符号获取基础成交量
   */
  private getBaseVolumeBySymbol(symbol: string): number {
    const normalizedSymbol = symbol.toUpperCase();

    // 主流币种的基础成交量估算
    if (normalizedSymbol.includes('BTC')) {
      return 50000; // BTC基础成交量
    } else if (normalizedSymbol.includes('ETH')) {
      return 30000; // ETH基础成交量
    } else if (normalizedSymbol.includes('BNB') || normalizedSymbol.includes('ADA') || normalizedSymbol.includes('SOL')) {
      return 15000; // 主流山寨币基础成交量
    } else {
      return 5000; // 其他币种基础成交量
    }
  }

  /**
   * 根据时间框架获取乘数
   */
  private getTimeframeMultiplier(timeframe: string): number {
    switch (timeframe.toLowerCase()) {
      case '1m':
      case '5m':
        return 0.1; // 短期时间框架成交量较小
      case '15m':
      case '30m':
        return 0.3;
      case '1h':
      case '2h':
        return 0.6;
      case '4h':
      case '6h':
        return 1.0; // 基准时间框架
      case '12h':
      case '1d':
        return 1.5;
      case '3d':
      case '1w':
        return 2.0; // 长期时间框架成交量较大
      default:
        return 1.0; // 默认乘数
    }
  }



  // ==================== 动态权重分配辅助方法 ====================

  /**
   * 映射趋势方向到统一格式
   */
  private mapTrendDirection(direction: string): 'bullish' | 'bearish' | 'neutral' {
    switch (direction) {
      case 'UPTREND':
        return 'bullish';
      case 'DOWNTREND':
        return 'bearish';
      default:
        return 'neutral';
    }
  }

  /**
   * 计算动量
   */
  private calculateMomentum(direction: string, strength: number): number {
    const normalizedStrength = strength / 10; // 归一化到0-1
    switch (direction) {
      case 'UPTREND':
        return normalizedStrength;
      case 'DOWNTREND':
        return -normalizedStrength;
      default:
        return 0;
    }
  }

  /**
   * 确定市场类型
   */
  private determineMarketType(timeframeAnalyses: any[]): 'HIGH_VOLATILITY' | 'LOW_VOLATILITY' | 'TRENDING' | 'RANGING' {
    const directions = timeframeAnalyses.map(a => a.trend.direction);
    const uniqueDirections = new Set(directions);

    if (uniqueDirections.size === 1 && !directions.includes('SIDEWAYS')) {
      return 'TRENDING';
    } else if (uniqueDirections.size > 2) {
      return 'HIGH_VOLATILITY';
    } else if (directions.includes('SIDEWAYS')) {
      return 'RANGING';
    } else {
      return 'LOW_VOLATILITY';
    }
  }

  /**
   * 计算波动性
   */
  private calculateVolatility(timeframeAnalyses: any[]): number {
    const strengths = timeframeAnalyses.map(a => {
      const strength = a.trend.strength;
      if (strength === undefined || strength === null) {
        throw new Error('计算波动性时发现趋势强度数据缺失');
      }
      return strength;
    });
    const avgStrength = strengths.reduce((sum, s) => sum + s, 0) / strengths.length;
    const variance = strengths.reduce((sum, s) => sum + Math.pow(s - avgStrength, 2), 0) / strengths.length;
    return Math.sqrt(variance) / 10; // 归一化
  }

  /**
   * 计算平均成交量
   */
  private calculateAverageVolume(timeframeAnalyses: any[]): number {
    const volumes = timeframeAnalyses.map(a => {
      const volume = a.volume;
      if (volume === undefined || volume === null) {
        throw new Error('计算平均成交量时发现成交量数据缺失');
      }
      return volume;
    });
    return volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
  }

  /**
   * 获取主导趋势
   */
  private getDominantTrend(timeframeAnalyses: any[]): string {
    const directions = timeframeAnalyses.map(a => a.trend.direction);
    const counts = directions.reduce((acc, dir) => {
      if (!dir) {
        throw new Error('主导趋势计算时发现方向数据缺失');
      }
      acc[dir] = (acc[dir] ?? 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b);
  }

  /**
   * 计算整体强度
   */
  private calculateOverallStrength(timeframeAnalyses: any[]): number {
    const strengths = timeframeAnalyses.map(a => {
      const strength = a.trend.strength;
      if (strength === undefined || strength === null) {
        throw new Error('计算整体强度时发现趋势强度数据缺失');
      }
      return strength;
    });
    return strengths.reduce((sum, s) => sum + s, 0) / strengths.length;
  }

  // ========== BaseApplicationService 抽象方法实现 ==========

  /**
   * 验证请求输入 - 实现基类抽象方法
   */
  protected async validateRequest<T>(request: T, context: RequestContext): Promise<ValidationResult> {
    const errors: string[] = [];

    try {
      if (context.metadata?.operationName === '完整趋势分析') {
        const trendRequest = request as any;
        if (!trendRequest.symbol || typeof trendRequest.symbol !== 'string') {
          errors.push('符号参数无效或缺失');
        }
        if (!trendRequest.primaryTimeframe || typeof trendRequest.primaryTimeframe !== 'string') {
          errors.push('主要时间框架参数无效或缺失');
        }
        if (trendRequest.analysisDepth && !['basic', 'standard', 'comprehensive'].includes(trendRequest.analysisDepth)) {
          errors.push('分析深度参数无效');
        }
      } else if (context.metadata?.operationName === '快速趋势评估') {
        const quickRequest = request as any;
        if (!quickRequest.symbol || typeof quickRequest.symbol !== 'string') {
          errors.push('符号参数无效或缺失');
        }
        if (!quickRequest.timeframe || typeof quickRequest.timeframe !== 'string') {
          errors.push('时间框架参数无效或缺失');
        }
      }
    } catch (error) {
      errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 将请求参数转换为领域值对象 - 实现基类抽象方法
   */
  protected async convertToDomainObjects<T>(request: T, context: RequestContext): Promise<any> {
    const req = request as any;

    // 标准化符号输入
    const normalizedSymbol = this.normalizeSymbolInput(req.symbol);
    const symbol = MarketSymbol.fromSymbolString(normalizedSymbol);

    if (context.metadata?.operationName === '完整趋势分析') {
      const primaryTimeframe = new Timeframe(req.primaryTimeframe);
      const analysisTimeframes = req.analysisTimeframes?.map((tf: string) => new Timeframe(tf)) ??
                                [new Timeframe('1h'), new Timeframe('4h'), new Timeframe('1d')];

      return {
        symbol,
        primaryTimeframe,
        analysisTimeframes,
        analysisDepth: req.analysisDepth ?? 'standard',
        includePatterns: req.includePatterns ?? false,
        includePredictions: req.includePredictions ?? false
      };
    } else if (context.metadata?.operationName === '快速趋势评估') {
      const timeframe = new Timeframe(req.timeframe);

      return {
        symbol,
        timeframe
      };
    }

    return { symbol };
  }

  /**
   * 执行核心业务逻辑 - 实现基类抽象方法
   */
  protected async executeCoreBusinessLogic(domainObjects: any, context: RequestContext): Promise<any> {
    if (context.metadata?.operationName === '完整趋势分析') {
      // 构造多时间框架数据
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol: domainObjects.symbol.symbol,
        timeframes: domainObjects.analysisTimeframes.map((tf: Timeframe) => tf.value),
        lookbackPeriods: new Map(domainObjects.analysisTimeframes.map((tf: Timeframe) => [tf.value, 100]))
      });

      // 根据分析深度选择合适的分析引擎
      if (domainObjects.analysisDepth === 'comprehensive') {
        this.logger.info('使用纯净AI分析引擎（综合模式）', { symbol: domainObjects.symbol.symbol });
        // 直接使用MultiTimeframeData

        return await this.pureAITrendAnalysisEngine.generatePureTrendAnalysis({
          multiTimeframeData,
          query: `分析${domainObjects.symbol.symbol}在${domainObjects.primaryTimeframe.value}时间框架的趋势`
        });
      } else {
        this.logger.info('使用纯净AI分析引擎', { symbol: domainObjects.symbol.symbol });
        // 直接使用MultiTimeframeData

        return await this.pureAITrendAnalysisEngine.generatePureTrendAnalysis({
          multiTimeframeData,
          query: `分析${domainObjects.symbol.symbol}在${domainObjects.primaryTimeframe.value}时间框架的趋势`
        });
      }
    } else if (context.metadata?.operationName === '快速趋势评估') {
      // 构造多时间框架数据
      const multiTimeframeData = await this.multiTimeframeService.collectData({
        symbol: domainObjects.symbol.symbol,
        timeframes: [domainObjects.timeframe.value],
        lookbackPeriods: new Map([[domainObjects.timeframe.value, 100]])
      });

      return await this.pureAITrendAnalysisEngine.generatePureTrendAnalysis({
        multiTimeframeData,
        query: `快速评估${domainObjects.symbol.symbol}在${domainObjects.timeframe.value}时间框架的趋势`
      });
    }

    throw new Error(`不支持的操作: ${context.metadata?.operationName}`);
  }

  /**
   * 应用业务规则后处理 - 实现基类抽象方法
   */
  protected async applyBusinessRules<T>(
    coreResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<BusinessRuleResult> {
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // 应用趋势业务规则
    const processedResult = { ...coreResult };
    const req = originalRequest as any;

    // 基于分析深度调整置信度
    if (req.analysisDepth === 'basic') {
      processedResult.confidence = Math.max(0.3, processedResult.confidence - 0.2);
      warnings.push('基础分析深度可能影响结果准确性');
      recommendations.push('建议使用标准或综合分析深度获得更准确的结果');
    } else if (req.analysisDepth === 'comprehensive') {
      processedResult.confidence = Math.min(0.95, processedResult.confidence + 0.1);
      recommendations.push('综合分析提供了更全面的市场洞察');
    }

    // 基于时间框架数量调整强度
    const timeframeCount = req.analysisTimeframes?.length ?? 3;
    if (timeframeCount >= 5) {
      processedResult.strength = Math.min(10, processedResult.strength + 1);
      recommendations.push('多时间框架分析增强了趋势确认的可靠性');
    } else if (timeframeCount < 3) {
      warnings.push('时间框架数量较少，建议增加更多时间框架以提高分析可靠性');
    }

    // 置信度检查
    if (processedResult.confidence < 0.5) {
      warnings.push('分析置信度较低，建议谨慎使用结果');
      recommendations.push('考虑等待更多市场数据或使用更高的分析深度');
    }

    return {
      data: processedResult,
      warnings,
      recommendations
    };
  }

  /**
   * 获取可选的附加数据 - 实现基类抽象方法
   */
  protected async enrichWithAdditionalData<T>(
    processedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    const req = originalRequest as any;
    const enrichedResult = { ...processedResult };

    if (context.metadata?.operationName === '完整趋势分析') {
      // 获取技术形态（如果请求）
      if (req.includePatterns) {
        try {
          const domainObjects = await this.convertToDomainObjects(originalRequest, context);
          enrichedResult.patterns = await this.getPatterns(
            domainObjects.symbol.symbol,
            domainObjects.analysisTimeframes
          );
        } catch (error) {
          this.logger.warn('获取技术形态失败', { error: error instanceof Error ? error.message : String(error) });
          enrichedResult.patterns = [];
        }
      }

      // 获取趋势预测（如果请求）
      if (req.includePredictions) {
        try {
          const domainObjects = await this.convertToDomainObjects(originalRequest, context);
          enrichedResult.predictions = await this.getPredictions(
            domainObjects.symbol.symbol,
            domainObjects.analysisTimeframes
          );
        } catch (error) {
          this.logger.warn('获取趋势预测失败', { error: error instanceof Error ? error.message : String(error) });
          enrichedResult.predictions = [];
        }
      }
    }

    return enrichedResult;
  }

  /**
   * 格式化响应DTO - 实现基类抽象方法
   */
  protected async formatResponse<T>(
    enrichedResult: any,
    originalRequest: T,
    context: RequestContext
  ): Promise<any> {
    const req = originalRequest as any;

    if (context.metadata?.operationName === '完整趋势分析') {
      return {
        success: true,
        message: '趋势分析完成',
        timestamp: new Date(),
        requestId: context.requestId,
        data: {
          symbol: req.symbol,
          primaryTimeframe: req.primaryTimeframe,
          trend: {
            direction: enrichedResult.trend,
            strength: enrichedResult.strength,
            confidence: enrichedResult.confidence
          },
          patterns: enrichedResult.patterns || [],
          predictions: enrichedResult.predictions || [],
          analysis: enrichedResult
        }
      };
    } else if (context.metadata?.operationName === '快速趋势评估') {
      return {
        success: true,
        message: '快速趋势评估完成',
        timestamp: new Date(),
        requestId: context.requestId,
        data: {
          symbol: req.symbol,
          timeframe: req.timeframe,
          direction: enrichedResult.trend,
          strength: enrichedResult.strength,
          confidence: enrichedResult.confidence,
          rsi: enrichedResult.technicalIndicators?.rsi?.value || 50,
          lastUpdate: new Date()
        }
      };
    }

    throw new Error(`不支持的响应格式化: ${context.metadata?.operationName}`);
  }

  // 健康检查已移至统一健康检查聚合器 - 避免重复实现
  // 使用 TrendAnalysisHealthProvider 替代

  /**
   * 总结请求内容 - 重写基类方法
   */
  protected summarizeRequest<T>(request: T): any {
    const req = request as any;
    return {
      symbol: req.symbol,
      timeframe: req.primaryTimeframe || req.timeframe,
      analysisDepth: req.analysisDepth,
      includePatterns: req.includePatterns,
      includePredictions: req.includePredictions
    };
  }

  /**
   * 总结响应内容 - 重写基类方法
   */
  protected summarizeResponse<T>(response: T): any {
    const resp = response as any;
    return {
      success: resp.success,
      symbol: resp.data?.symbol,
      trend: resp.data?.trend?.direction,
      confidence: resp.data?.trend?.confidence,
      patternsCount: resp.data?.patterns?.length || 0,
      predictionsCount: resp.data?.predictions?.length || 0
    };
  }

  // ========== 私有辅助方法 ==========

  /**
   * 格式化趋势分析响应
   */
  private formatTrendAnalysisResponse(result: any): any {
    return {
      success: true,
      analysis: result,
      summary: {
        symbol: result.symbol?.value || result.symbol,
        primaryTrend: result.primaryTrend?.direction || 'NEUTRAL',
        confidence: result.primaryTrend?.confidence || 0.5,
        keyLevels: result.supportResistance?.length || 0,
        patterns: result.technicalPatterns?.length || 0
      },
      timestamp: new Date()
    };
  }

  /**
   * 格式化模式识别响应
   */
  private formatPatternRecognitionResponse(result: any): any {
    return {
      success: true,
      patterns: result,
      summary: {
        totalPatterns: Array.isArray(result) ? result.length : 0,
        highConfidencePatterns: Array.isArray(result) ? result.filter((p: any) => p.confidence > 0.7).length : 0
      },
      timestamp: new Date()
    };
  }
}
