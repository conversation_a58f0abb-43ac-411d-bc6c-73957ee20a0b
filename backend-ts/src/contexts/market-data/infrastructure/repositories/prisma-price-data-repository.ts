import { injectable, inject } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { IRepositoryBaseService } from '../../../../shared/infrastructure/database/repository-base-service.interface';
import { DataMappingUtils } from '../../../../shared/infrastructure/database/data-mapping-service';
import { UniqueEntityId } from '../../../../shared/domain/entities/base-entity';
import { PaginatedResult, PaginationParams } from '../../../../shared/application/interfaces/repository';
import {
  IPriceDataRepository,
  PriceDataEntity,
  PriceDataSearchCondition,
  TimeRange,
  PriceStatistics,
  PriceTrend
} from '../../domain/repositories/price-data-repository';
import { UnifiedMarketOverview } from '../../../../shared/infrastructure/types/unified-interfaces';
import { TradingSymbol } from '../../domain/value-objects/trading-symbol';
import { Price } from '../../domain/value-objects/price';
import { Volume } from '../../domain/value-objects/volume';

/**
 * 价格数据映射器
 * 处理领域对象与持久化对象之间的转换
 */
class PriceDataMapper {
  toPersistence(entity: PriceDataEntity): any {
    return {
      id: entity.id.toString(),
      symbolId: entity.symbolId.toString(),
      price: entity.price.value,
      change24h: entity.change24h,
      changePercent24h: entity.changePercent24h,
      volume24h: entity.volume24h.value,
      high24h: entity.high24h?.value,
      low24h: entity.low24h?.value,
      marketCap: entity.marketCap,
      timestamp: entity.timestamp,
      createdAt: entity.createdAt
    };
  }

  toDomain(record: any): PriceDataEntity {
    return new PriceDataEntity({
      symbolId: new UniqueEntityId(record.symbolId),
      symbol: new TradingSymbol(record.Symbols?.symbol || 'BTC'),
      price: new Price(record.price?.toNumber ? record.price.toNumber() : Number(record.price)),
      change24h: record.change24h?.toNumber ? record.change24h.toNumber() : Number(record.change24h || 0),
      changePercent24h: record.changePercent24h?.toNumber ? record.changePercent24h.toNumber() : Number(record.changePercent24h || 0),
      volume24h: new Volume(record.volume24h?.toNumber ? record.volume24h.toNumber() : Number(record.volume24h || 0)),
      high24h: record.high24h ? new Price(record.high24h?.toNumber ? record.high24h.toNumber() : Number(record.high24h)) : undefined,
      low24h: record.low24h ? new Price(record.low24h?.toNumber ? record.low24h.toNumber() : Number(record.low24h)) : undefined,
      marketCap: record.marketCap?.toNumber ? record.marketCap.toNumber() : Number(record.marketCap || 0),
      timestamp: record.timestamp
    }, new UniqueEntityId(record.id));
  }
}

/**
 * 统一价格数据仓储实现 - 组合模式
 * 使用RepositoryBaseService提供统一的基础功能
 */
@injectable()
export class UnifiedPriceDataRepository implements IPriceDataRepository {
  private readonly dataMapper: PriceDataMapper;

  constructor(
    @inject(TYPES.Database) private readonly prisma: PrismaClient,
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.RepositoryBaseService) private readonly baseService: IRepositoryBaseService<PriceDataEntity>,
    @inject(TYPES.Shared.DataMappingService) private readonly dataMappingService: DataMappingService
  ) {
    this.dataMapper = new PriceDataMapper();
  }

  // 获取数据库模型
  private getModel() {
    return this.prisma.priceData;
  }

  // 获取表名
  private getTableName(): string {
    return 'priceData';
  }

  // 数据转换方法
  toPersistence(entity: PriceDataEntity): any {
    return this.dataMapper.toPersistence(entity);
  }

  toDomain(data: any): PriceDataEntity {
    return this.dataMapper.toDomain(data);
  }

  // 使用baseService的执行监控包装器
  private async executeWithMonitoring<T>(
    operation: string,
    executor: () => Promise<T>,
    options?: { cache?: boolean; cacheKey?: string; cacheTTL?: number }
  ): Promise<T> {
    return this.baseService.executeWithMonitoring(operation, executor, options);
  }

  // 实现IPriceDataRepository接口的业务特定方法
  async findById(id: UniqueEntityId): Promise<PriceDataEntity | null> {
    return this.executeWithMonitoring(
      'findById',
      async () => {
        const record = await this.getModel().findUnique({
          where: { id: id.toString() },
          include: { Symbols: true },
        });

        return record ? this.toDomain(record) : null;
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:${id.toString()}`,
        cacheTTL: 60000 // 1分钟缓存
      }
    );
  }

  /**
   * 根据交易对查找最新价格
   */
  async findLatestBySymbol(symbol: TradingSymbol): Promise<PriceDataEntity | null> {
    return this.executeWithMonitoring(
      'findLatestBySymbol',
      async () => {
        const record = await this.getModel().findFirst({
          where: {
            Symbols: { symbol: symbol.symbol },
          },
          include: { Symbols: true },
          orderBy: { timestamp: 'desc' },
        });

        return record ? this.toDomain(record) : null;
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:latest:${symbol.symbol}`,
        cacheTTL: 30000 // 30秒缓存
      }
    );
  }

  /**
   * 根据交易对ID查找最新价格
   */
  async findLatestBySymbolId(symbolId: UniqueEntityId): Promise<PriceDataEntity | null> {
    return this.executeWithMonitoring(
      'findLatestBySymbolId',
      async () => {
        const record = await this.getModel().findFirst({
          where: { symbolId: symbolId.toString() },
          include: { Symbols: true },
          orderBy: { timestamp: 'desc' },
        });

        return record ? this.toDomain(record) : null;
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:latest:symbolId:${symbolId.toString()}`,
        cacheTTL: 30000 // 30秒缓存
      }
    );
  }

  /**
   * 批量获取最新价格
   */
  async findLatestBySymbols(symbols: TradingSymbol[]): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'findLatestBySymbols',
      async () => {
        const symbolStrings = symbols.map(s => s.symbol);
        const records = await this.getModel().findMany({
          where: {
            Symbols: { symbol: { in: symbolStrings } },
          },
          include: { Symbols: true },
          orderBy: { timestamp: 'desc' },
        });

        return records.map(record => this.toDomain(record));
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:latest:batch:${symbols.map(s => s.symbol).join(',')}`,
        cacheTTL: 30000
      }
    );
  }

  /**
   * 根据时间范围查找价格数据
   */
  async findByTimeRange(
    symbol: TradingSymbol,
    startTime: Date,
    endTime: Date
  ): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'findByTimeRange',
      async () => {
        const records = await this.getModel().findMany({
          where: {
            Symbols: { symbol: symbol.symbol },
            timestamp: {
              gte: startTime,
              lte: endTime,
            },
          },
          include: { Symbols: true },
          orderBy: { timestamp: 'asc' },
        });

        return records.map(record => this.toDomain(record));
      }
    );
  }

  /**
   * 获取价格历史记录
   */
  async findPriceHistory(
    symbol: TradingSymbol,
    limit: number = 100,
    offset: number = 0
  ): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'findPriceHistory',
      async () => {
        const records = await this.getModel().findMany({
          where: {
            Symbols: { symbol: symbol.symbol },
          },
          include: { Symbols: true },
          orderBy: { timestamp: 'desc' },
          take: limit,
          skip: offset,
        });

        return records.map(record => this.toDomain(record));
      }
    );
  }

  /**
   * 分页查询价格数据
   */
  async findWithPagination(
    condition: PriceDataSearchCondition,
    params: PaginationParams
  ): Promise<PaginatedResult<PriceDataEntity>> {
    return this.executeWithMonitoring(
      'findWithPagination',
      async () => {
        const whereCondition = this.buildWhereCondition(condition);

        const [records, total] = await Promise.all([
          this.getModel().findMany({
            where: whereCondition,
            include: { Symbols: true },
            orderBy: { timestamp: 'desc' },
            take: params.limit,
            skip: params.offset,
          }),
          this.getModel().count({ where: whereCondition })
        ]);

        const page = Math.floor(params.offset / params.limit) + 1;
        const totalPages = Math.ceil(total / params.limit);
        const domainEntities = records.map(record => this.toDomain(record));

        return {
          data: domainEntities, // 向后兼容
          items: domainEntities,
          total,
          page,
          limit: params.limit,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          hasMore: page < totalPages
        };
      }
    );
  }

  /**
   * 获取价格统计信息
   */
  async getPriceStatistics(
    symbol: TradingSymbol,
    timeRange: TimeRange
  ): Promise<PriceStatistics> {
    return this.executeWithMonitoring(
      'getPriceStatistics',
      async () => {
        const endTime = new Date();
        const startTime = new Date(endTime.getTime() - this.getTimeRangeMilliseconds(timeRange));

        const records = await this.getModel().findMany({
          where: {
            Symbols: { symbol: symbol.symbol },
            timestamp: {
              gte: startTime,
              lte: endTime,
            },
          },
          orderBy: { timestamp: 'asc' },
        });

        if (records.length === 0) {
          throw new Error(`没有找到 ${symbol.symbol} 在指定时间范围内的价格数据`);
        }

        const prices = records.map(r => r.price);
        const volumes = records.map(r => r.volume24h);
        const firstPrice = records[0].price;
        const lastPrice = records[records.length - 1].price;

        return {
          symbol: symbol,
          timeRange,
          startTime,
          endTime,
          dataPoints: records.length,
          openPrice: new Price(firstPrice.toNumber()),
          closePrice: new Price(lastPrice.toNumber()),
          currentPrice: new Price(lastPrice.toNumber()), // 添加缺失的属性
          highPrice: new Price(Math.max(...prices.map(p => p.toNumber()))),
          lowPrice: new Price(Math.min(...prices.map(p => p.toNumber()))),
          avgPrice: new Price(prices.reduce((sum, price) => sum + price.toNumber(), 0) / prices.length),
          totalVolume: new Volume(volumes.reduce((sum, vol) => sum + vol.toNumber(), 0)),
          avgVolume: new Volume(volumes.reduce((sum, vol) => sum + vol.toNumber(), 0) / volumes.length),
          priceChange: new Price(lastPrice.toNumber() - firstPrice.toNumber()),
          priceChangePercent: ((lastPrice.toNumber() - firstPrice.toNumber()) / firstPrice.toNumber()) * 100,
          volatility: this.calculateVolatility(prices.map(p => p.toNumber())),
          firstTimestamp: records[0].timestamp, // 添加缺失的属性
          lastTimestamp: records[records.length - 1].timestamp // 添加缺失的属性
        };
      }
    );
  }

  /**
   * 获取价格变化排行
   */
  async getTopGainers(limit: number = 10): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'getTopGainers',
      async () => {
        const records = await this.getModel().findMany({
          where: {
            changePercent24h: { gt: 0 },
          },
          include: { Symbols: true },
          orderBy: { changePercent24h: 'desc' },
          take: limit,
        });

        return records.map(record => this.toDomain(record));
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:topGainers:${limit}`,
        cacheTTL: 60000
      }
    );
  }

  /**
   * 获取价格下跌排行
   */
  async getTopLosers(limit: number = 10): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'getTopLosers',
      async () => {
        const records = await this.getModel().findMany({
          where: {
            changePercent24h: { lt: 0 },
          },
          include: { Symbols: true },
          orderBy: { changePercent24h: 'asc' },
          take: limit,
        });

        return records.map(record => this.toDomain(record));
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:topLosers:${limit}`,
        cacheTTL: 60000
      }
    );
  }

  /**
   * 获取交易量排行
   */
  async getTopByVolume(limit: number = 10): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'getTopByVolume',
      async () => {
        const records = await this.getModel().findMany({
          include: { Symbols: true },
          orderBy: { volume24h: 'desc' },
          take: limit,
        });

        return records.map(record => this.toDomain(record));
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:topByVolume:${limit}`,
        cacheTTL: 60000
      }
    );
  }

  /**
   * 批量保存价格数据
   */
  async saveBatch(priceData: Omit<PriceDataEntity, 'id' | 'createdAt'>[]): Promise<void> {
    return this.executeWithMonitoring(
      'saveBatch',
      async () => {
        const batchSize = 100;
        const batches = this.chunkArray(priceData, batchSize);

        for (const batch of batches) {
          const data = batch.map(entity => ({
            symbolId: entity.symbolId.toString(),
            price: entity.price.value,
            change24h: entity.change24h,
            changePercent24h: entity.changePercent24h,
            volume24h: entity.volume24h.value,
            high24h: entity.high24h?.value,
            low24h: entity.low24h?.value,
            marketCap: entity.marketCap,
            timestamp: entity.timestamp,
          }));

          await this.getModel().createMany({
            data,
            skipDuplicates: true,
          });
        }

        this.logger.info('批量保存价格数据成功', { count: priceData.length });
      }
    );
  }

  /**
   * 更新最新价格
   */
  async updateLatestPrice(
    symbolId: UniqueEntityId,
    priceData: Omit<PriceDataEntity, 'id' | 'symbolId' | 'symbol' | 'createdAt'>
  ): Promise<void> {
    return this.executeWithMonitoring(
      'updateLatestPrice',
      async () => {
        await this.getModel().create({
          data: {
            symbolId: symbolId.toString(),
            price: priceData.price.value,
            change24h: priceData.change24h,
            changePercent24h: priceData.changePercent24h,
            volume24h: priceData.volume24h.value,
            high24h: priceData.high24h?.value,
            low24h: priceData.low24h?.value,
            marketCap: priceData.marketCap,
            timestamp: priceData.timestamp,
          },
        });

        this.logger.debug('更新最新价格成功', { symbolId: symbolId.toString() });
      }
    );
  }

  /**
   * 删除过期数据
   */
  async deleteExpiredData(beforeDate: Date): Promise<number> {
    return this.executeWithMonitoring(
      'deleteExpiredData',
      async () => {
        const result = await this.getModel().deleteMany({
          where: {
            timestamp: { lt: beforeDate },
          },
        });

        this.logger.info('删除过期价格数据', {
          beforeDate,
          deletedCount: result.count,
        });

        return result.count;
      }
    );
  }

  /**
   * 获取价格数据数量
   */
  async countBySymbol(symbol: TradingSymbol): Promise<number> {
    return this.executeWithMonitoring(
      'countBySymbol',
      async () => {
        return await this.getModel().count({
          where: {
            Symbols: { symbol: symbol.symbol },
          },
        });
      }
    );
  }

  /**
   * 检查是否有最新数据
   */
  async hasRecentData(symbol: TradingSymbol, withinMinutes: number): Promise<boolean> {
    return this.executeWithMonitoring(
      'hasRecentData',
      async () => {
        const cutoffTime = new Date(Date.now() - withinMinutes * 60 * 1000);

        const count = await this.getModel().count({
          where: {
            Symbols: { symbol: symbol.symbol },
            timestamp: { gte: cutoffTime },
          },
        });

        return count > 0;
      }
    );
  }

  /**
   * 获取价格变化趋势
   */
  async getPriceTrend(symbol: TradingSymbol, timeRange: TimeRange): Promise<PriceTrend> {
    return this.executeWithMonitoring(
      'getPriceTrend',
      async () => {
        const statistics = await this.getPriceStatistics(symbol, timeRange);

        // 基于价格变化百分比确定趋势
        let trend: 'bullish' | 'bearish' | 'sideways';
        if (statistics.priceChangePercent > 5) {
          trend = 'bullish';
        } else if (statistics.priceChangePercent < -5) {
          trend = 'bearish';
        } else {
          trend = 'sideways';
        }

        return {
          symbol: symbol,
          timeRange,
          trend,
          strength: Math.abs(statistics.priceChangePercent),
          confidence: this.calculateTrendConfidence(statistics),
          priceChange: statistics.priceChange,
          priceChangePercent: statistics.priceChangePercent,
          volume: statistics.totalVolume,
          volatility: statistics.volatility,
          support: statistics.lowPrice,
          resistance: statistics.highPrice,
          movingAverages: {
            ma5: statistics.avgPrice,
            ma10: statistics.avgPrice,
            ma20: statistics.avgPrice,
            ma50: statistics.avgPrice,
            ma200: statistics.avgPrice
          },
          timestamp: new Date()
        };
      }
    );
  }

  /**
   * 获取市场概览
   */
  async getMarketOverview(): Promise<UnifiedMarketOverview> {
    return this.executeWithMonitoring(
      'getMarketOverview',
      async () => {
        const [topGainers, topLosers, topByVolume] = await Promise.all([
          this.getTopGainers(5),
          this.getTopLosers(5),
          this.getTopByVolume(5)
        ]);

        // 计算总交易量
        const totalVolume24h = topByVolume.reduce(
          (sum, item) => sum + item.volume24h.value,
          0
        );

        const activeSymbolsCount = await this.getModel().groupBy({
          by: ['symbolId'],
          where: {
            timestamp: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24小时内
            },
          },
        });

        return {
          totalMarketCap: 0, // 需要从外部数据源获取
          totalVolume24h,
          btcDominance: 0, // 需要计算
          activeSymbols: activeSymbolsCount.length,
          topGainers: topGainers.map(item => ({
            symbol: item.symbol.symbol,
            changePercent: item.changePercent24h,
            price: item.price.value,
            volume: item.volume24h.value
          })),
          topLosers: topLosers.map(item => ({
            symbol: item.symbol.symbol,
            changePercent: item.changePercent24h,
            price: item.price.value,
            volume: item.volume24h.value
          })),
          topByVolume: topByVolume.map(item => ({
            symbol: item.symbol.symbol,
            volume: item.volume24h.value,
            price: item.price.value,
            changePercent: item.changePercent24h
          })),
          timestamp: new Date()
        };
      }
    );
  }

  // 实现基础仓储方法
  async findAll(): Promise<PriceDataEntity[]> {
    return this.executeWithMonitoring(
      'findAll',
      async () => {
        const records = await this.getModel().findMany({
          include: { Symbols: true },
          orderBy: { timestamp: 'desc' },
          take: 1000, // 限制数量避免性能问题
        });
        return records.map(record => this.toDomain(record));
      },
      {
        cache: true,
        cacheKey: `${this.getTableName()}:findAll`,
        cacheTTL: 30000 // 30秒缓存
      }
    );
  }

  async save(entity: PriceDataEntity): Promise<void> {
    return this.executeWithMonitoring(
      'save',
      async () => {
        const data = this.toPersistence(entity);
        await this.getModel().create({ data });
      }
    );
  }

  async update(entity: PriceDataEntity): Promise<void> {
    return this.executeWithMonitoring(
      'update',
      async () => {
        const data = this.toPersistence(entity);
        await this.getModel().update({
          where: { id: entity.id.toString() },
          data,
        });
      }
    );
  }

  async delete(id: UniqueEntityId): Promise<void> {
    return this.executeWithMonitoring(
      'delete',
      async () => {
        await this.getModel().delete({
          where: { id: id.toString() },
        });
      }
    );
  }

  async exists(id: UniqueEntityId): Promise<boolean> {
    return this.executeWithMonitoring(
      'exists',
      async () => {
        const count = await this.getModel().count({
          where: { id: id.toString() },
        });
        return count > 0;
      }
    );
  }

  async deleteMany(ids: UniqueEntityId[]): Promise<void> {
    await this.getModel().deleteMany({
      where: {
        id: { in: ids.map(id => id.toString()) },
      },
    });
  }

  // 辅助方法
  private getTimeRangeMilliseconds(timeRange: TimeRange): number {
    switch (timeRange) {
      case '1h':
        return 60 * 60 * 1000;
      case '4h':
        return 4 * 60 * 60 * 1000;
      case '1d':
        return 24 * 60 * 60 * 1000;
      case '7d':
        return 7 * 24 * 60 * 60 * 1000;
      case '30d':
        return 30 * 24 * 60 * 60 * 1000;
      default:
        return 24 * 60 * 60 * 1000; // 默认1天
    }
  }

  private buildWhereCondition(condition: PriceDataSearchCondition): any {
    const where: any = {};

    if (condition.symbol) {
      where.Symbols = { symbol: condition.symbol.symbol };
    }

    if (condition.symbolIds && condition.symbolIds.length > 0) {
      where.symbolId = { in: condition.symbolIds.map(id => id.toString()) };
    }

    if (condition.priceRange) {
      where.price = {
        gte: condition.priceRange.min.value,
        lte: condition.priceRange.max.value,
      };
    }

    if (condition.volumeRange) {
      where.volume24h = {
        gte: condition.volumeRange.min.value,
        lte: condition.volumeRange.max.value,
      };
    }

    if (condition.changePercentRange) {
      where.changePercent24h = {
        gte: condition.changePercentRange.min,
        lte: condition.changePercentRange.max,
      };
    }

    if (condition.timeRange) {
      where.timestamp = {
        gte: condition.timeRange.start,
        lte: condition.timeRange.end,
      };
    }

    return where;
  }

  private calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;

    const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    return Math.sqrt(variance) / mean; // 相对波动率
  }

  private calculateTrendConfidence(statistics: PriceStatistics): number {
    // 基于数据点数量、波动率和价格变化幅度计算置信度
    const dataPointsFactor = Math.min(statistics.dataPoints / 100, 1); // 数据点越多置信度越高
    const volatilityFactor = Math.max(0, 1 - statistics.volatility); // 波动率越低置信度越高
    const changeFactor = Math.min(Math.abs(statistics.priceChangePercent) / 10, 1); // 变化幅度适中时置信度较高

    return (dataPointsFactor + volatilityFactor + changeFactor) / 3;
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 批量保存价格数据
   */
  async saveMany(entities: PriceDataEntity[]): Promise<void> {
    return this.executeWithMonitoring(
      'saveMany',
      async () => {
        const chunks = this.chunkArray(entities, 100); // 分批处理

        for (const chunk of chunks) {
          const records = chunk.map(entity => this.toPersistence(entity));
          await this.getModel().createMany({
            data: records,
            skipDuplicates: true
          });
        }
      }
    );
  }

  /**
   * 批量更新价格数据
   */
  async updateMany(entities: PriceDataEntity[]): Promise<void> {
    return this.executeWithMonitoring(
      'updateMany',
      async () => {
        const chunks = this.chunkArray(entities, 100); // 分批处理

        for (const chunk of chunks) {
          for (const entity of chunk) {
            const record = this.toPersistence(entity);
            await this.getModel().upsert({
              where: {
                symbolId_timestamp: {
                  symbolId: entity.symbol.symbol,
                  timestamp: entity.timestamp
                }
              },
              update: record,
              create: record
            });
          }
        }
      }
    );
  }
}

/**
 * 向后兼容性导出
 * 保持原有的类名，但使用新的统一实现
 */
export const PrismaPriceDataRepository = UnifiedPriceDataRepository;
