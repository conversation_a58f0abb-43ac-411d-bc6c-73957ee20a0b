/**
 * WebSocket连接管理器
 * 统一管理所有WebSocket适配器，提供连接池、负载均衡和健康检查功能
 */

import { injectable } from 'inversify';
import { EventEmitter } from 'events';
import { randomBytes } from 'crypto';
import { IBasicLogger } from '../../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import {
  IWebSocketAdapter,
  IWebSocketConnectionManager,
  WebSocketSubscription,
  WebSocketConnectionStats,
  WebSocketEventType,
  WebSocketEventData,
  WebSocketPoolConfig,
  WebSocketLoadBalanceConfig,
  WebSocketLoadBalanceStrategy,
  DEFAULT_POOL_CONFIG,
  DEFAULT_LOAD_BALANCE_CONFIG
} from '../types/websocket-types';

/**
 * 适配器包装器，包含额外的管理信息
 */
interface AdapterWrapper {
  adapter: IWebSocketAdapter;
  priority: number;
  weight: number;
  isActive: boolean;
  lastUsed: Date;
  connectionCount: number;
  errorCount: number;
  healthCheckFailures: number;
}

/**
 * WebSocket连接管理器实现
 */
@injectable()
export class WebSocketConnectionManager extends EventEmitter implements IWebSocketConnectionManager {
  private readonly adapters: Map<string, AdapterWrapper> = new Map();
  private readonly logger: IBasicLogger;
  private readonly poolConfig: WebSocketPoolConfig;
  private readonly loadBalanceConfig: WebSocketLoadBalanceConfig;
  
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private roundRobinIndex = 0;
  
  constructor(
    logger: IBasicLogger,
    poolConfig: Partial<WebSocketPoolConfig> = {},
    loadBalanceConfig: Partial<WebSocketLoadBalanceConfig> = {}
  ) {
    super();
    this.logger = logger;
    this.poolConfig = { ...DEFAULT_POOL_CONFIG, ...poolConfig };
    this.loadBalanceConfig = { ...DEFAULT_LOAD_BALANCE_CONFIG, ...loadBalanceConfig };
  }
  
  /**
   * 添加WebSocket适配器
   */
  async addAdapter(adapter: IWebSocketAdapter): Promise<void> {
    if (this.adapters.has(adapter.id)) {
      throw new Error(`适配器已存在: ${adapter.id}`);
    }
    
    const wrapper: AdapterWrapper = {
      adapter,
      priority: this.loadBalanceConfig.priorities?.[adapter.name] || 1,
      weight: this.loadBalanceConfig.weights?.[adapter.name] || 1,
      isActive: true,
      lastUsed: new Date(),
      connectionCount: 0,
      errorCount: 0,
      healthCheckFailures: 0
    };
    
    this.adapters.set(adapter.id, wrapper);
    
    // 监听适配器事件（如果适配器支持事件监听）
    if ('on' in adapter && typeof adapter.on === 'function') {
      adapter.on(WebSocketEventType.CONNECTED, (data) => this.onAdapterEvent('connected', adapter.id, data));
      adapter.on(WebSocketEventType.DISCONNECTED, (data) => this.onAdapterEvent('disconnected', adapter.id, data));
      adapter.on(WebSocketEventType.ERROR, (data) => this.onAdapterEvent('error', adapter.id, data));
      adapter.on(WebSocketEventType.MESSAGE, (data) => this.onAdapterEvent('message', adapter.id, data));
    }
    
    this.logger.info('添加WebSocket适配器', { 
      adapterId: adapter.id, 
      name: adapter.name,
      priority: wrapper.priority,
      weight: wrapper.weight
    });
    
    // 如果管理器正在运行，尝试连接新适配器
    if (this.isRunning) {
      try {
        await adapter.connect();
      } catch (error) {
        this.logger.error('新适配器连接失败', { 
          adapterId: adapter.id, 
          error: error.message 
        });
        wrapper.errorCount++;
      }
    }
  }
  
  /**
   * 移除WebSocket适配器
   */
  async removeAdapter(adapterId: string): Promise<void> {
    const wrapper = this.adapters.get(adapterId);
    if (!wrapper) {
      this.logger.warn('适配器不存在', { adapterId });
      return;
    }
    
    try {
      await wrapper.adapter.disconnect();
    } catch (error) {
      this.logger.error('断开适配器连接失败', { 
        adapterId, 
        error: error.message 
      });
    }
    
    this.adapters.delete(adapterId);
    
    this.logger.info('移除WebSocket适配器', { adapterId });
  }
  
  /**
   * 获取WebSocket适配器
   */
  getAdapter(adapterId: string): IWebSocketAdapter | undefined {
    return this.adapters.get(adapterId)?.adapter;
  }
  
  /**
   * 获取所有WebSocket适配器
   */
  getAllAdapters(): IWebSocketAdapter[] {
    return Array.from(this.adapters.values()).map(wrapper => wrapper.adapter);
  }
  
  /**
   * 订阅数据流
   */
  async subscribe(adapterId: string, subscription: WebSocketSubscription): Promise<string> {
    const wrapper = this.adapters.get(adapterId);
    if (!wrapper) {
      throw new Error(`适配器不存在: ${adapterId}`);
    }
    
    if (!wrapper.isActive) {
      throw new Error(`适配器未激活: ${adapterId}`);
    }
    
    try {
      // 检查适配器是否支持详细订阅方法
      let subscriptionId: string;
      if ('subscribeWithSubscription' in wrapper.adapter && typeof wrapper.adapter.subscribeWithSubscription === 'function') {
        subscriptionId = await wrapper.adapter.subscribeWithSubscription(subscription);
      } else {
        // 使用统一接口的subscribe方法
        await wrapper.adapter.subscribe(subscription.channel);
        subscriptionId = subscription.id;
      }
      wrapper.connectionCount++;
      wrapper.lastUsed = new Date();
      
      this.logger.info('订阅成功', { 
        adapterId, 
        subscriptionId,
        channel: subscription.channel
      });
      
      return subscriptionId;
    } catch (error) {
      wrapper.errorCount++;
      this.logger.error('订阅失败', { 
        adapterId, 
        channel: subscription.channel,
        error: error.message 
      });
      throw error;
    }
  }
  
  /**
   * 取消订阅
   */
  async unsubscribe(adapterId: string, subscriptionId: string): Promise<void> {
    const wrapper = this.adapters.get(adapterId);
    if (!wrapper) {
      throw new Error(`适配器不存在: ${adapterId}`);
    }
    
    try {
      await wrapper.adapter.unsubscribe(subscriptionId);
      wrapper.connectionCount = Math.max(0, wrapper.connectionCount - 1);
      
      this.logger.info('取消订阅成功', { adapterId, subscriptionId });
    } catch (error) {
      wrapper.errorCount++;
      this.logger.error('取消订阅失败', { 
        adapterId, 
        subscriptionId,
        error: error.message 
      });
      throw error;
    }
  }
  
  /**
   * 广播消息到所有适配器
   */
  async broadcast(message: any): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (const wrapper of this.adapters.values()) {
      if (wrapper.isActive && wrapper.adapter.isConnected()) {
        promises.push(
          wrapper.adapter.send(message).catch(error => {
            wrapper.errorCount++;
            this.logger.error('广播消息失败', { 
              adapterId: wrapper.adapter.id,
              error: error.message 
            });
          })
        );
      }
    }
    
    await Promise.allSettled(promises);
  }
  
  /**
   * 获取健康状态
   */
  getHealthStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {};
    
    for (const [adapterId, wrapper] of this.adapters) {
      // 检查适配器是否有isHealthy方法
      if ('isHealthy' in wrapper.adapter && typeof wrapper.adapter.isHealthy === 'function') {
        status[adapterId] = wrapper.isActive && wrapper.adapter.isHealthy();
      } else {
        // 默认认为活跃的适配器是健康的
        status[adapterId] = wrapper.isActive;
      }
    }
    
    return status;
  }
  
  /**
   * 获取统计信息
   */
  getStats(): Record<string, WebSocketConnectionStats> {
    const stats: Record<string, WebSocketConnectionStats> = {};
    
    for (const [adapterId, wrapper] of this.adapters) {
      // 检查适配器是否有stats属性
      if ('stats' in wrapper.adapter) {
        stats[adapterId] = wrapper.adapter.stats;
      } else {
        stats[adapterId] = {
          connectionTime: new Date(),
          lastMessageTime: wrapper.lastUsed,
          messageCount: 0,
          errorCount: wrapper.errorCount,
          reconnectCount: 0,
          subscriptionCount: wrapper.connectionCount,
          averageLatency: 0,
          uptime: Date.now() - wrapper.lastUsed.getTime()
        };
      }
    }
    
    return stats;
  }
  
  /**
   * 启动连接管理器
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('连接管理器已在运行');
      return;
    }
    
    this.isRunning = true;
    
    // 连接所有适配器
    const connectionPromises: Promise<void>[] = [];
    for (const wrapper of this.adapters.values()) {
      if (wrapper.isActive) {
        connectionPromises.push(
          wrapper.adapter.connect().catch(error => {
            wrapper.errorCount++;
            this.logger.error('适配器连接失败', { 
              adapterId: wrapper.adapter.id,
              error: error.message 
            });
          })
        );
      }
    }
    
    await Promise.allSettled(connectionPromises);
    
    // 启动健康检查
    if (this.loadBalanceConfig.healthCheckEnabled) {
      this.startHealthCheck();
    }
    
    this.logger.info('WebSocket连接管理器已启动', { 
      adapterCount: this.adapters.size,
      healthCheckEnabled: this.loadBalanceConfig.healthCheckEnabled
    });
  }
  
  /**
   * 停止连接管理器
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }
    
    this.isRunning = false;
    
    // 停止健康检查
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    // 断开所有适配器
    const disconnectionPromises: Promise<void>[] = [];
    for (const wrapper of this.adapters.values()) {
      disconnectionPromises.push(
        wrapper.adapter.disconnect().catch(error => {
          this.logger.error('适配器断开失败', { 
            adapterId: wrapper.adapter.id,
            error: error.message 
          });
        })
      );
    }
    
    await Promise.allSettled(disconnectionPromises);
    
    this.logger.info('WebSocket连接管理器已停止');
  }

  /**
   * 根据负载均衡策略选择适配器
   */
  selectAdapter(): IWebSocketAdapter | null {
    const activeAdapters = Array.from(this.adapters.values())
      .filter(wrapper => {
        if (!wrapper.isActive) return false;
        if ('isHealthy' in wrapper.adapter && typeof wrapper.adapter.isHealthy === 'function') {
          return wrapper.adapter.isHealthy();
        }
        return true; // 默认认为活跃的适配器是健康的
      });

    if (activeAdapters.length === 0) {
      return null;
    }

    switch (this.loadBalanceConfig.strategy) {
      case WebSocketLoadBalanceStrategy.ROUND_ROBIN:
        return this.selectRoundRobin(activeAdapters);

      case WebSocketLoadBalanceStrategy.LEAST_CONNECTIONS:
        return this.selectLeastConnections(activeAdapters);

      case WebSocketLoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
        return this.selectWeightedRoundRobin(activeAdapters);

      case WebSocketLoadBalanceStrategy.PRIORITY_BASED:
        return this.selectPriorityBased(activeAdapters);

      default:
        return activeAdapters[0].adapter;
    }
  }

  /**
   * 轮询选择
   */
  private selectRoundRobin(adapters: AdapterWrapper[]): IWebSocketAdapter {
    const adapter = adapters[this.roundRobinIndex % adapters.length];
    this.roundRobinIndex++;
    return adapter.adapter;
  }

  /**
   * 最少连接选择
   */
  private selectLeastConnections(adapters: AdapterWrapper[]): IWebSocketAdapter {
    return adapters.reduce((min, current) =>
      current.connectionCount < min.connectionCount ? current : min,
      adapters[0]
    ).adapter;
  }

  /**
   * 加权轮询选择
   */
  private selectWeightedRoundRobin(adapters: AdapterWrapper[]): IWebSocketAdapter {
    const totalWeight = adapters.reduce((sum, adapter) => sum + adapter.weight, 0);
    // 使用加密安全的随机数生成器
    const randomBytes4 = randomBytes(4);
    const randomValue = randomBytes4.readUInt32BE(0) / 0xFFFFFFFF;
    let randomWeight = randomValue * totalWeight;

    for (const adapter of adapters) {
      randomWeight -= adapter.weight;
      if (randomWeight <= 0) {
        return adapter.adapter;
      }
    }

    return adapters[0].adapter;
  }

  /**
   * 基于优先级选择
   */
  private selectPriorityBased(adapters: AdapterWrapper[]): IWebSocketAdapter {
    return adapters.reduce((highest, current) =>
      current.priority > highest.priority ? current : highest,
      adapters[0]
    ).adapter;
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.poolConfig.healthCheckInterval);
  }

  /**
   * 执行健康检查
   */
  /**
   * 执行健康检查
   * 注意：健康检查已移至统一的健康检查聚合器
   * 建议使用 WebSocketHealthProvider 替代
   */
  private performHealthCheck(): void {
    // 健康检查逻辑已统一到 WebSocketHealthProvider
    // 这里只保留基本的活跃状态管理
    for (const [adapterId, wrapper] of this.adapters) {
      // 基本的活跃状态检查，详细健康检查由 WebSocketHealthProvider 处理
      if (wrapper.healthCheckFailures >= 3) {
        wrapper.isActive = false;
      }
    }
  }

  /**
   * 处理适配器事件
   */
  private onAdapterEvent(eventType: string, adapterId: string, data: WebSocketEventData): void {
    const wrapper = this.adapters.get(adapterId);
    if (!wrapper) {
      return;
    }

    switch (eventType) {
      case 'connected':
        wrapper.isActive = true;
        wrapper.healthCheckFailures = 0;
        this.logger.info('适配器连接成功', { adapterId });
        break;

      case 'disconnected':
        this.logger.warn('适配器连接断开', { adapterId });
        break;

      case 'error':
        wrapper.errorCount++;
        this.logger.error('适配器错误', {
          adapterId,
          error: data.error?.message
        });
        break;

      case 'message':
        wrapper.lastUsed = new Date();
        break;
    }

    // 转发事件
    this.emit(eventType, { ...data, managerId: this.constructor.name });
  }

  /**
   * 获取适配器详细信息
   */
  getAdapterDetails(): Record<string, any> {
    const details: Record<string, any> = {};

    for (const [adapterId, wrapper] of this.adapters) {
      details[adapterId] = {
        id: wrapper.adapter.id || adapterId,
        name: wrapper.adapter.name || adapterId,
        state: ('state' in wrapper.adapter) ? wrapper.adapter.state : 'unknown',
        isActive: wrapper.isActive,
        priority: wrapper.priority,
        weight: wrapper.weight,
        connectionCount: wrapper.connectionCount,
        errorCount: wrapper.errorCount,
        healthCheckFailures: wrapper.healthCheckFailures,
        lastUsed: wrapper.lastUsed,
        stats: ('stats' in wrapper.adapter) ? wrapper.adapter.stats : null,
        subscriptions: ('getSubscriptions' in wrapper.adapter && typeof wrapper.adapter.getSubscriptions === 'function')
          ? wrapper.adapter.getSubscriptions().length
          : 0
      };
    }

    return details;
  }

  /**
   * 重置适配器统计
   */
  resetAdapterStats(adapterId?: string): void {
    if (adapterId) {
      const wrapper = this.adapters.get(adapterId);
      if (wrapper) {
        wrapper.connectionCount = 0;
        wrapper.errorCount = 0;
        wrapper.healthCheckFailures = 0;
        this.logger.info('重置适配器统计', { adapterId });
      }
    } else {
      for (const wrapper of this.adapters.values()) {
        wrapper.connectionCount = 0;
        wrapper.errorCount = 0;
        wrapper.healthCheckFailures = 0;
      }
      this.logger.info('重置所有适配器统计');
    }
  }
}
