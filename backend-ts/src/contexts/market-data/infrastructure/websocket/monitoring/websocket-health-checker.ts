/**
 * WebSocket健康检查服务
 * 提供统一的WebSocket连接健康检查和自动恢复功能
 */

import { injectable } from 'inversify';
import { EventEmitter } from 'events';
import { IBasicLogger } from '../../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import {
  IWebSocketAdapter,
  WebSocketConnectionState,
  WebSocketEventType
} from '../types/websocket-types';

/**
 * 健康检查结果
 */
export interface HealthCheckResult {
  adapterId: string;
  adapterName: string;
  isHealthy: boolean;
  state: WebSocketConnectionState;
  lastCheck: Date;
  issues: string[];
  metrics: {
    responseTime: number;
    uptime: number;
    messageCount: number;
    errorCount: number;
    reconnectCount: number;
  };
}

/**
 * 健康检查配置
 */
export interface HealthCheckConfig {
  interval: number; // 检查间隔
  timeout: number; // 检查超时
  retryAttempts: number; // 重试次数
  autoRecover: boolean; // 自动恢复
  pingMessage?: string | object; // 心跳消息
}

/**
 * 默认健康检查配置
 */
export const DEFAULT_HEALTH_CHECK_CONFIG: HealthCheckConfig = {
  interval: 60000, // 1分钟
  timeout: 10000, // 10秒
  retryAttempts: 3,
  autoRecover: true,
  pingMessage: 'ping'
};

/**
 * WebSocket健康检查器
 */
@injectable()
export class WebSocketHealthChecker extends EventEmitter {
  private readonly logger: IBasicLogger;
  private readonly config: HealthCheckConfig;
  private readonly adapters: Map<string, IWebSocketAdapter> = new Map();
  private readonly lastResults: Map<string, HealthCheckResult> = new Map();
  
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(
    logger: IBasicLogger,
    config: Partial<HealthCheckConfig> = {}
  ) {
    super();
    this.logger = logger;
    this.config = { ...DEFAULT_HEALTH_CHECK_CONFIG, ...config };
  }

  /**
   * 启动健康检查
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.startHealthCheck();
    
    this.logger.info('WebSocket健康检查器已启动', {
      interval: this.config.interval,
      timeout: this.config.timeout,
      autoRecover: this.config.autoRecover
    });
  }

  /**
   * 停止健康检查
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    this.logger.info('WebSocket健康检查器已停止');
  }

  /**
   * 添加适配器健康检查
   */
  addAdapter(adapter: IWebSocketAdapter): void {
    if (this.adapters.has(adapter.id)) {
      return;
    }

    this.adapters.set(adapter.id, adapter);
    
    this.logger.info('添加适配器健康检查', { 
      adapterId: adapter.id, 
      name: adapter.name 
    });
  }

  /**
   * 移除适配器健康检查
   */
  removeAdapter(adapterId: string): void {
    this.adapters.delete(adapterId);
    this.lastResults.delete(adapterId);
    
    this.logger.info('移除适配器健康检查', { adapterId });
  }

  /**
   * 手动执行健康检查
   * 注意：健康检查已移至统一的健康检查聚合器
   * 建议使用 WebSocketHealthProvider 替代/**
   * 检查指定适配器或所有适配器的健康状态
   * 委托到 WebSocketHealthProvider 实现
   */
  async checkHealth(adapterId?: string): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];
    
    try {
      if (adapterId) {
        // 检查特定适配器
        const adapter = this.adapters.get(adapterId);
        if (adapter) {
          const result = await this.performHealthCheckForAdapter(adapter);
          results.push(result);
        }
      } else {
        // 检查所有适配器
        for (const [id, adapter] of this.adapters) {
          const result = await this.performHealthCheckForAdapter(adapter);
          results.push(result);
        }
      }
      
      // 更新最后检查结果
      for (const result of results) {
        this.lastResults.set(result.adapterId, result);
      }
      
      return results;
    } catch (error) {
      this.logger.error('健康检查执行失败', { error, adapterId });
      
      // 返回错误状态的结果并更新lastResults
      if (adapterId) {
        const adapter = this.adapters.get(adapterId);
        if (adapter) {
          const errorResult = this.createErrorResult(adapter, error);
          this.lastResults.set(errorResult.adapterId, errorResult);
          return [errorResult];
        }
      } else {
        // 为所有适配器返回错误状态并更新lastResults
        for (const [id, adapter] of this.adapters) {
          const errorResult = this.createErrorResult(adapter, error);
          results.push(errorResult);
          this.lastResults.set(errorResult.adapterId, errorResult);
        }
      }
      
      return results;
    }
  }

  /**
   * 获取最后的健康检查结果
   */
  getLastResults(adapterId?: string): HealthCheckResult[] {
    if (adapterId) {
      const result = this.lastResults.get(adapterId);
      return result ? [result] : [];
    }

    return Array.from(this.lastResults.values());
  }

  /**
   * 获取健康状态摘要
   */
  getHealthSummary(): {
    total: number;
    healthy: number;
    unhealthy: number;
    unknown: number;
    healthyPercentage: number;
  } {
    const results = Array.from(this.lastResults.values());
    const total = results.length;
    const healthy = results.filter(r => r.isHealthy).length;
    const unhealthy = results.filter(r => !r.isHealthy).length;
    const unknown = total - healthy - unhealthy;
    
    return {
      total,
      healthy,
      unhealthy,
      unknown,
      healthyPercentage: total > 0 ? (healthy / total) * 100 : 0
    };
  }

  /**
   * 启动定期健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performAllHealthChecks();
    }, this.config.interval);
  }

  /**
   * 执行所有适配器的健康检查
   */
  private async performAllHealthChecks(): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (const adapter of this.adapters.values()) {
      promises.push(
        this.performHealthCheck(adapter)
          .then(result => {
            this.lastResults.set(adapter.id, result);
            this.emit('healthCheck', result);
            
            if (!result.isHealthy && this.config.autoRecover) {
              this.attemptRecovery(adapter);
            }
          })
          .catch(error => {
            this.logger.error('健康检查失败', {
              adapterId: adapter.id,
              error: error.message
            });
          })
      );
    }
    
    await Promise.allSettled(promises);
  }

  /**
   * 执行单个适配器的健康检查
   * 注意：健康检查已移至统一的健康检查聚合器
   * 建议使用 WebSocketHealthProvider 替代
   */
  /**
   * 为单个适配器执行健康检查
   */
  private async performHealthCheckForAdapter(adapter: IWebSocketAdapter): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const issues: string[] = [];
    
    try {
      // 检查连接状态
      const isConnected = adapter.getConnectionState() === WebSocketConnectionState.CONNECTED;
      if (!isConnected) {
        issues.push('WebSocket连接未建立');
      }
      
      // 检查最近是否有错误
      const stats = adapter.getStatistics();
      if (stats.errorCount > 0) {
        const errorRate = stats.errorCount / Math.max(stats.messageCount, 1);
        if (errorRate > 0.1) { // 错误率超过10%
          issues.push(`错误率过高: ${(errorRate * 100).toFixed(1)}%`);
        }
      }
      
      // 检查重连次数
      if (stats.reconnectCount > 5) {
        issues.push(`重连次数过多: ${stats.reconnectCount}`);
      }
      
      // 执行ping测试（如果支持）
      let pingSuccessful = true;
      try {
        await this.performPingTest(adapter);
      } catch (error) {
        pingSuccessful = false;
        issues.push('Ping测试失败');
      }
      
      const responseTime = Date.now() - startTime;
      const isHealthy = issues.length === 0 && isConnected && pingSuccessful;
      
      return {
        adapterId: adapter.id,
        adapterName: adapter.name,
        isHealthy,
        state: adapter.getConnectionState() as WebSocketConnectionState,
        lastCheck: new Date(),
        issues,
        metrics: {
          responseTime,
          uptime: stats.uptime,
          messageCount: stats.messageCount,
          errorCount: stats.errorCount,
          reconnectCount: stats.reconnectCount
        }
      };
    } catch (error) {
      return this.createErrorResult(adapter, error);
    }
  }
  
  /**
   * 创建错误状态的健康检查结果
   */
  private createErrorResult(adapter: IWebSocketAdapter, error: any): HealthCheckResult {
    return {
      adapterId: adapter.id,
      adapterName: adapter.name,
      isHealthy: false,
      state: WebSocketConnectionState.DISCONNECTED,
      lastCheck: new Date(),
      issues: [`健康检查失败: ${error instanceof Error ? error.message : String(error)}`],
      metrics: {
        responseTime: 0,
        uptime: 0,
        messageCount: 0,
        errorCount: 1,
        reconnectCount: 0
      }
    };
  }
  
  private async performHealthCheck(adapter: IWebSocketAdapter): Promise<HealthCheckResult> {
     // 委托到新的实现
     return this.performHealthCheckForAdapter(adapter);
   }
   
   /**
    * 执行ping测试
    */
   private async performPingTest(adapter: IWebSocketAdapter): Promise<void> {
     return new Promise((resolve, reject) => {
       const timeout = setTimeout(() => {
         reject(new Error('Ping测试超时'));
       }, 5000); // 5秒超时
       
       try {
         // 发送ping消息（如果适配器支持）
         if (typeof adapter.ping === 'function') {
           adapter.ping()
             .then(() => {
               clearTimeout(timeout);
               resolve();
             })
             .catch(error => {
               clearTimeout(timeout);
               reject(error);
             });
         } else {
           // 如果不支持ping方法，检查连接状态
           if (adapter.getConnectionState() === WebSocketConnectionState.CONNECTED) {
             clearTimeout(timeout);
             resolve();
           } else {
             clearTimeout(timeout);
             reject(new Error('连接状态异常'));
           }
         }
       } catch (error) {
         clearTimeout(timeout);
         reject(error);
       }
     });
   }

  /**
   * 尝试恢复不健康的适配器
   */
  private async attemptRecovery(adapter: IWebSocketAdapter): Promise<void> {
    this.logger.info('尝试恢复不健康的适配器', { 
      adapterId: adapter.id,
      name: adapter.name
    });

    let attempts = 0;
    while (attempts < this.config.retryAttempts) {
      attempts++;
      
      try {
        // 如果适配器断开连接，尝试重新连接
        if ('state' in adapter && adapter.state === WebSocketConnectionState.DISCONNECTED) {
          if ('connect' in adapter && typeof adapter.connect === 'function') {
            await adapter.connect();
          }
        }

        // 等待一段时间后检查是否恢复
        await new Promise(resolve => setTimeout(resolve, 2000));

        const isHealthy = ('isHealthy' in adapter && typeof adapter.isHealthy === 'function')
          ? adapter.isHealthy()
          : true;

        if (isHealthy) {
          this.logger.info('适配器恢复成功', {
            adapterId: adapter.id,
            attempts
          });
          this.emit('recovery', {
            adapterId: adapter.id,
            adapterName: adapter.name,
            attempts
          });
          return;
        }
      } catch (error) {
        this.logger.warn('适配器恢复尝试失败', {
          adapterId: adapter.id,
          attempt: attempts,
          error: error.message
        });
      }
    }

    this.logger.error('适配器恢复失败', {
      adapterId: adapter.id,
      attempts
    });
    
    this.emit('recoveryFailed', {
      adapterId: adapter.id,
      adapterName: adapter.name,
      attempts
    });
  }
}
