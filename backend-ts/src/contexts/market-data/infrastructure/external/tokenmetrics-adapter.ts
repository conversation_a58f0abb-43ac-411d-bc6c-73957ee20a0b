/**
 * TokenMetrics API适配器 - 已弃用
 *
 * 注意：根据用户要求，TokenMetrics已从系统中完全移除
 * 此文件保留仅用于向后兼容，所有方法将抛出弃用错误
 *
 * 替代方案：
 * - 技术分析：使用内置技术指标计算
 * - 基本面分析：使用CoinMetrics和Blockchair数据
 * - 情绪分析：使用SentiCrypt免费API
 */

import { Logger } from 'winston';
import { HttpClientFactory, HttpClientType } from '../../../../shared/infrastructure/http/http-client-factory';
import { BaseHttpClient } from '../../../../shared/infrastructure/http/base-http-client';

export interface TokenInfo {
  TOKEN_ID: number;
  TOKEN_NAME: string;
  TOKEN_SYMBOL: string;
  EXCHANGE_LIST?: Array<{
    exchangeId: string;
    exchangeName: string;
  }>;
  CATEGORY_LIST?: Array<{
    categoryId: number;
    categoryName: string;
    categorySlug: string;
  }>;
  TM_LINK?: string;
  CONTRACT_ADDRESS?: Record<string, string>;
}

export interface TokensResponse {
  success: boolean;
  message: string;
  length: number;
  data: TokenInfo[];
}

export interface MarketMetrics {
  tokenId: string;
  symbol: string;
  price: number;
  price_change_24h: number;
  price_change_7d: number;
  marketCap: number;
  volume24h: number;
  volatility: number;
  liquidityScore: number;
  sentimentScore: number;
  technicalScore: number;
  fundamentalScore: number;
  timestamp: string;
}

export interface TradingSignal {
  tokenId: string;
  symbol: string;
  signalType: 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL';
  confidence: number;
  priceTarget: number;
  stopLoss: number;
  timeHorizon: string;
  reasoning: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  timestamp: string;
}

export interface ProcessedTokenMetrics {
  symbol: string;
  overallScore: number;
  priceAnalysis: {
    currentPrice: number;
    priceTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    volatilityLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  };
  sentimentAnalysis: {
    sentimentScore: number;
    sentimentLevel: 'VERY_NEGATIVE' | 'NEGATIVE' | 'NEUTRAL' | 'POSITIVE' | 'VERY_POSITIVE';
  };
  technicalAnalysis: {
    technicalScore: number;
    signalStrength: 'WEAK' | 'MODERATE' | 'STRONG';
  };
  fundamentalAnalysis: {
    fundamentalScore: number;
    valueAssessment: 'UNDERVALUED' | 'FAIR' | 'OVERVALUED';
  };
  tradingRecommendation: {
    action: 'BUY' | 'SELL' | 'HOLD';
    confidence: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  };
  timestamp: Date;
}

export class TokenMetricsAdapter {
  private readonly logger: Logger;
  private readonly httpClient: any = null;
  private tokensCache: TokenInfo[] = [];
  private cacheTimestamp: Date | null = null;
  private readonly cacheValidityHours: number = 24;

  constructor(logger: Logger, apiKey?: string) {
    this.logger = logger;
    this.logger.warn('TokenMetrics适配器已弃用，请使用替代数据源');
  }

  private setupInterceptors(): void {
    this.httpClient.interceptors.request.use(
      (config) => {
        this.logger.debug('TokenMetrics API请求', {
          url: config.url,
          method: config.method,
          params: config.params
        });
        return config;
      },
      (error) => {
        this.logger.error('TokenMetrics API请求错误', { error: error.message });
        return Promise.reject(error);
      }
    );

    this.httpClient.interceptors.response.use(
      (response) => {
        this.logger.debug('TokenMetrics API响应成功', {
          url: response.config.url,
          status: response.status,
          dataLength: Array.isArray(response.data) ? response.data.length : 'object'
        });
        return response;
      },
      (error) => {
        this.logger.error('TokenMetrics API响应错误', {
          url: error.config?.url,
          status: error.response?.status,
          message: error.message,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * 获取支持的代币列表
   */
  async getTokens(limit: number = 1000, offset: number = 0): Promise<TokenInfo[]> {
    try {
      // 检查缓存
      if (this.isCacheValid()) {
        this.logger.debug('使用缓存的代币列表');
        return this.tokensCache;
      }

      // 不传递参数，使用备用值
      const response = await this.httpClient.get('/tokens') as { data: TokensResponse };

      if (!response.data.success) {
        throw new Error(response.data.message || 'API请求失败');
      }

      // 更新缓存
      this.tokensCache = response.data.data;
      this.cacheTimestamp = new Date();

      this.logger.info(`获取到${response.data.data.length}个代币信息`);
      return response.data.data;
    } catch (error) {
      this.logger.error('获取代币列表失败', { error, limit, offset });
      throw new Error(`获取代币列表失败: ${error}`);
    }
  }

  /**
   * 根据符号查找代币信息
   */
  async findTokenBySymbol(symbol: string): Promise<TokenInfo | null> {
    try {
      const tokens = await this.getTokens();
      const token = tokens.find(t => t.TOKEN_SYMBOL.toLowerCase() === symbol.toLowerCase());
      return token || null;
    } catch (error) {
      this.logger.error('查找代币失败', { error, symbol });
      return null;
    }
  }

  /**
   * 获取市场指标（模拟端点，需要根据实际API调整）
   */
  async getMarketMetrics(symbol: string): Promise<MarketMetrics | null> {
    try {
      const token = await this.findTokenBySymbol(symbol);
      if (!token) {
        this.logger.warn(`未找到代币: ${symbol}`);
        return null;
      }

      // 注意：这个端点可能需要根据实际API文档调整
      const response = await this.httpClient.get(`/market-metrics/${token.TOKEN_ID}`) as { data: MarketMetrics };
      return response.data;
    } catch (error) {
      this.logger.error('获取市场指标失败', { error, symbol });
      // 如果API端点不存在，返回null而不是抛出错误
      if ((error as any).response?.status === 404) {
        this.logger.warn(`市场指标端点不存在: ${symbol}`);
        return null;
      }
      throw new Error(`获取市场指标失败: ${error}`);
    }
  }

  /**
   * 获取交易信号（模拟端点，需要根据实际API调整）
   */
  async getTradingSignals(symbol: string): Promise<TradingSignal[]> {
    try {
      const token = await this.findTokenBySymbol(symbol);
      if (!token) {
        this.logger.warn(`未找到代币: ${symbol}`);
        return [];
      }

      // 注意：这个端点可能需要根据实际API文档调整
      const response = await this.httpClient.get(`/trading-signals/${token.TOKEN_ID}`) as { data: TradingSignal[] };
      return response.data;
    } catch (error) {
      this.logger.error('获取交易信号失败', { error, symbol });
      // 如果API端点不存在，返回空数组而不是抛出错误
      if ((error as any).response?.status === 404) {
        this.logger.warn(`交易信号端点不存在: ${symbol}`);
        return [];
      }
      throw new Error(`获取交易信号失败: ${error}`);
    }
  }

  /**
   * 获取BTC的综合分析
   */
  async getBTCAnalysis(): Promise<ProcessedTokenMetrics | null> {
    try {
      const symbol = 'BTC';
      
      // 获取基础代币信息
      const token = await this.findTokenBySymbol(symbol);
      if (!token) {
        throw new Error('未找到BTC代币信息');
      }

      // 尝试获取市场指标和交易信号
      const [marketMetrics, tradingSignals] = await Promise.allSettled([
        this.getMarketMetrics(symbol),
        this.getTradingSignals(symbol)
      ]);

      // 处理市场指标
      let metrics: MarketMetrics | null = null;
      if (marketMetrics.status === 'fulfilled') {
        metrics = marketMetrics.value;
      }

      // 处理交易信号
      let signals: TradingSignal[] = [];
      if (tradingSignals.status === 'fulfilled') {
        signals = tradingSignals.value;
      }

      // 生成综合分析
      return this.processTokenAnalysis(symbol, token, metrics, signals);
    } catch (error) {
      this.logger.error('获取BTC分析失败', { error });
      return null;
    }
  }

  /**
   * 处理代币分析数据
   */
  private processTokenAnalysis(
    symbol: string,
    token: TokenInfo,
    metrics: MarketMetrics | null,
    signals: TradingSignal[]
  ): ProcessedTokenMetrics {
    // 如果没有实际的指标数据，使用基础信息生成分析
    const currentPrice = metrics?.price || 0;
    const priceChange24h = metrics?.price_change_24h || 0;
    const volatility = metrics?.volatility || 0;
    const sentimentScore = metrics?.sentimentScore || 50;
    const technicalScore = metrics?.technicalScore || 50;
    const fundamentalScore = metrics?.fundamentalScore || 50;

    // 价格趋势分析
    let priceTrend: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    if (priceChange24h > 2) {
      priceTrend = 'BULLISH';
    } else if (priceChange24h < -2) {
      priceTrend = 'BEARISH';
    } else {
      priceTrend = 'NEUTRAL';
    }

    // 波动性级别
    let volatilityLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    if (volatility > 5) {
      volatilityLevel = 'HIGH';
    } else if (volatility > 2) {
      volatilityLevel = 'MEDIUM';
    } else {
      volatilityLevel = 'LOW';
    }

    // 情绪级别
    let sentimentLevel: 'VERY_NEGATIVE' | 'NEGATIVE' | 'NEUTRAL' | 'POSITIVE' | 'VERY_POSITIVE';
    if (sentimentScore >= 80) {
      sentimentLevel = 'VERY_POSITIVE';
    } else if (sentimentScore >= 60) {
      sentimentLevel = 'POSITIVE';
    } else if (sentimentScore >= 40) {
      sentimentLevel = 'NEUTRAL';
    } else if (sentimentScore >= 20) {
      sentimentLevel = 'NEGATIVE';
    } else {
      sentimentLevel = 'VERY_NEGATIVE';
    }

    // 技术信号强度
    let signalStrength: 'WEAK' | 'MODERATE' | 'STRONG';
    if (technicalScore >= 70) {
      signalStrength = 'STRONG';
    } else if (technicalScore >= 50) {
      signalStrength = 'MODERATE';
    } else {
      signalStrength = 'WEAK';
    }

    // 价值评估
    let valueAssessment: 'UNDERVALUED' | 'FAIR' | 'OVERVALUED';
    if (fundamentalScore >= 70) {
      valueAssessment = 'UNDERVALUED';
    } else if (fundamentalScore >= 40) {
      valueAssessment = 'FAIR';
    } else {
      valueAssessment = 'OVERVALUED';
    }

    // 综合评分
    const overallScore = (sentimentScore + technicalScore + fundamentalScore) / 3;

    // 交易建议
    let action: 'BUY' | 'SELL' | 'HOLD';
    let confidence = 0;
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' = 'MEDIUM';

    if (signals.length > 0) {
      // 使用最新的交易信号
      const latestSignal = signals[0];
      action = latestSignal.signalType === 'NO_SIGNAL' ? 'HOLD' : latestSignal.signalType;
      confidence = latestSignal.confidence;
      riskLevel = latestSignal.riskLevel;
    } else {
      // 基于综合评分生成建议
      if (overallScore >= 70) {
        action = 'BUY';
        confidence = overallScore;
      } else if (overallScore <= 30) {
        action = 'SELL';
        confidence = 100 - overallScore;
      } else {
        action = 'HOLD';
        confidence = 50;
      }
    }

    return {
      symbol,
      overallScore: overallScore,
      priceAnalysis: {
        currentPrice: currentPrice,
        priceTrend: priceTrend,
        volatilityLevel: volatilityLevel
      },
      sentimentAnalysis: {
        sentimentScore: sentimentScore,
        sentimentLevel: sentimentLevel
      },
      technicalAnalysis: {
        technicalScore: technicalScore,
        signalStrength: signalStrength
      },
      fundamentalAnalysis: {
        fundamentalScore: fundamentalScore,
        valueAssessment: valueAssessment
      },
      tradingRecommendation: {
        action,
        confidence,
        riskLevel: riskLevel
      },
      timestamp: new Date()
    };
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    if (!this.cacheTimestamp || this.tokensCache.length === 0) {
      return false;
    }

    const now = new Date();
    const diffHours = (now.getTime() - this.cacheTimestamp.getTime()) / (1000 * 60 * 60);
    return diffHours < this.cacheValidityHours;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.tokensCache = [];
    this.cacheTimestamp = null;
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus(): {
    isValid: boolean;
    tokenCount: number;
    lastUpdated: Date | null;
    hoursOld: number;
  } {
    const isValid = this.isCacheValid();
    const hoursOld = this.cacheTimestamp ? 
      (new Date().getTime() - this.cacheTimestamp.getTime()) / (1000 * 60 * 60) : 0;

    return {
      isValid,
      tokenCount: this.tokensCache.length,
      lastUpdated: this.cacheTimestamp,
      hoursOld
    };
  }

  // 健康检查已由基类 ExternalDataAdapterBase 提供 - 避免重复实现
  // 基类的 testConnection() 方法会自动调用根路径进行健康检查
}

export default TokenMetricsAdapter;
