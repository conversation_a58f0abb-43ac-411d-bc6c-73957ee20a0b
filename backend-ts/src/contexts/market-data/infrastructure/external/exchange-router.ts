/**
 * 智能交易所路由器
 * 实现实时性能评估、数据质量评估、可用性检查和智能路由决策
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { createLogger, format, transports } from 'winston';
import { performance } from 'perf_hooks';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../../../../shared/infrastructure/error/unified-error-handler';
import { ErrorRecovery } from '../../../../shared/infrastructure/error/error-recovery-decorator';
import { ErrorType } from '../../../../shared/infrastructure/error/error-handling-config';
import { IExchangeAdapter } from '../../domain/services/exchange-adapter';
import { TradingSymbol } from '../../domain/value-objects/trading-symbol';
import { Timeframe } from '../../domain/value-objects/timeframe';
import { BinanceAdapter } from './binance-adapter';
import { OKXAdapter } from './okx-adapter';
import { CoinbaseAdapter } from './coinbase-adapter';

/**
 * 数据请求
 */
export interface DataRequest {
  symbol: string;
  dataType: 'price' | 'kline' | 'trade' | 'orderbook';
  priority: number; // 0-10, 10为最高优先级
  timeout: number;  // 超时时间（毫秒）
  qualityRequirement: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
}

/**
 * 数据源决策
 */
export interface DataSourceDecision {
  primaryExchange: string;
  backupExchanges: string[];
  routingReason: string;
  expectedLatency: number;
  expectedQuality: number;
  confidence: number; // 0-1
  timestamp: Date;
}

import { PerformanceMetrics as BasePerformanceMetrics } from '../../../../shared/infrastructure/types';

/**
 * 交换所性能指标（扩展统一性能指标）
 */
export interface ExchangePerformanceMetrics extends Partial<BasePerformanceMetrics> {
  exchange: string;
  latency: number;        // 平均延迟（毫秒）
  p95Latency: number;     // 95分位延迟
  p99Latency: number;     // 99分位延迟
  throughput: number;     // 吞吐量（msg/s）
  errorRate: number;      // 错误率（0-1）
  availability: number;   // 可用性（0-1）
  lastUpdate: Date;
}

// 向后兼容的类型别名
export type PerformanceMetrics = ExchangePerformanceMetrics;

/**
 * 质量指标
 */
export interface QualityMetrics {
  exchange: string;
  dataQuality: number;    // 数据质量评分（0-100）
  completeness: number;   // 数据完整性（0-1）
  accuracy: number;       // 数据准确性（0-1）
  timeliness: number;     // 数据及时性（0-1）
  consistency: number;    // 数据一致性（0-1）
  anomalyRate: number;    // 异常率（0-1）
  lastUpdate: Date;
}

/**
 * 可用性状态
 */
export interface AvailabilityStatus {
  exchange: string;
  isAvailable: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'error';
  lastHeartbeat: Date;
  consecutiveFailures: number;
  estimatedRecoveryTime?: Date;
}

/**
 * 路由上下文
 */
interface RoutingContext {
  performanceMetrics: Map<string, PerformanceMetrics>;
  qualityMetrics: Map<string, QualityMetrics>;
  availabilityStatus: Map<string, AvailabilityStatus>;
  request: DataRequest;
}

/**
 * 路由算法类型
 */
export enum RoutingAlgorithm {
  WEIGHTED_ROUND_ROBIN = 'weightedRoundRobin',
  LEAST_LATENCY = 'leastLatency',
  QUALITY_BASED = 'qualityBased',
  HYBRID_OPTIMIZATION = 'hybridOptimization'
}

/**
 * 故障检测配置
 */
interface FailureDetectionConfig {
  healthCheckInterval: number;     // 健康检查间隔（毫秒）
  failureThreshold: number;        // 故障阈值（连续失败次数）
  recoveryThreshold: number;       // 恢复阈值（连续成功次数）
  circuitBreakerTimeout: number;   // 断路器超时时间（毫秒）
  maxRetries: number;              // 最大重试次数
  retryDelay: number;              // 重试延迟（毫秒）
}

/**
 * 故障恢复策略
 */
interface FailureRecoveryStrategy {
  enableAutoFailover: boolean;     // 启用自动故障转移
  enableCircuitBreaker: boolean;   // 启用断路器
  enableGradualRecovery: boolean;  // 启用渐进式恢复
  enableHealthMonitoring: boolean; // 启用健康监控
}

/**
 * 交易所故障状态
 */
interface ExchangeFailureState {
  exchange: string;
  isHealthy: boolean;
  consecutiveFailures: number;
  consecutiveSuccesses: number;
  lastFailureTime: Date | null;
  lastSuccessTime: Date | null;
  circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  failureReasons: string[];
}

/**
 * 路由配置
 */
interface RoutingConfig {
  algorithm: RoutingAlgorithm;
  weights: {
    latency: number;      // 延迟权重
    quality: number;      // 质量权重
    availability: number; // 可用性权重
    cost: number;        // 成本权重
  };
  thresholds: {
    maxLatency: number;      // 最大可接受延迟（毫秒）
    minQuality: number;      // 最小质量要求（0-100）
    minAvailability: number; // 最小可用性要求（0-1）
    maxErrorRate: number;    // 最大错误率（0-1）
  };
  fallbackStrategy: 'roundRobin' | 'random' | 'bestEffort';
  failureDetection: FailureDetectionConfig;
  recoveryStrategy: FailureRecoveryStrategy;
}

/**
 * 智能交易所路由器
 */
@injectable()
export class ExchangeRouter {
  private readonly logger: Logger;
  private readonly config: RoutingConfig;
  private readonly supportedExchanges: string[];

  // 性能和质量监控
  private readonly performanceMetrics: Map<string, PerformanceMetrics> = new Map();
  private readonly qualityMetrics: Map<string, QualityMetrics> = new Map();
  private readonly availabilityStatus: Map<string, AvailabilityStatus> = new Map();

  // 故障检测和恢复
  private readonly failureStates: Map<string, ExchangeFailureState> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  // 路由统计
  private routingHistory: Array<{
    request: DataRequest;
    decision: DataSourceDecision;
    actualLatency?: number;
    actualQuality?: number;
    success: boolean;
    timestamp: Date;
  }> = [];
  
  // 自适应学习
  private exchangeWeights: Map<string, number> = new Map();
  private lastOptimization = Date.now();
  
  // 监控定时器
  private metricsUpdateInterval: NodeJS.Timeout | null = null;
  private optimizationInterval: NodeJS.Timeout | null = null;

  // 故障转移历史
  private failoverHistory: Array<{
    type: string;
    exchange: string;
    timestamp: Date;
    availableExchanges: string[];
  }> = [];

  // 交易所适配器实例
  private exchangeAdapters: Map<string, IExchangeAdapter> = new Map();

  constructor(
    @inject(TYPES.Logger) private readonly basicLogger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler
  ) {
    this.logger = createLogger({
      level: 'info',
      format: format.combine(
        format.timestamp(),
        format.label({ label: 'ExchangeRouter' }),
        format.simple()
      ),
      transports: [new transports.Console()]
    });

    // 初始化支持的交易所
    this.supportedExchanges = ['binance', 'okx', 'coinbase', 'kraken', 'huobi', 'bybit'];

    // 初始化路由配置
    this.config = {
      algorithm: RoutingAlgorithm.HYBRID_OPTIMIZATION,
      weights: {
        latency: 0.4,      // 40% 延迟权重
        quality: 0.3,      // 30% 质量权重
        availability: 0.2, // 20% 可用性权重
        cost: 0.1         // 10% 成本权重
      },
      thresholds: {
        maxLatency: 1000,      // 1秒最大延迟
        minQuality: 80,        // 80分最小质量
        minAvailability: 0.95, // 95%最小可用性
        maxErrorRate: 0.05     // 5%最大错误率
      },
      fallbackStrategy: 'bestEffort',
      failureDetection: {
        healthCheckInterval: 30000,    // 30秒健康检查
        failureThreshold: 3,           // 连续3次失败触发故障
        recoveryThreshold: 2,          // 连续2次成功恢复
        circuitBreakerTimeout: 60000,  // 1分钟断路器超时
        maxRetries: 3,                 // 最大重试3次
        retryDelay: 1000              // 1秒重试延迟
      },
      recoveryStrategy: {
        enableAutoFailover: true,      // 启用自动故障转移
        enableCircuitBreaker: true,    // 启用断路器
        enableGradualRecovery: true,   // 启用渐进式恢复
        enableHealthMonitoring: true   // 启用健康监控
      }
    };

    // 初始化交易所适配器
    this.initializeExchangeAdapters();

    // 初始化故障状态
    this.initializeFailureStates();

    // 启动健康监控
    if (this.config.recoveryStrategy.enableHealthMonitoring) {
      this.startHealthMonitoring();
    }

    // 初始化交易所权重
    this.initializeExchangeWeights();

    // 启动监控
    this.startMonitoring();
  }

  /**
   * 初始化故障状态
   */
  private initializeFailureStates(): void {
    for (const exchange of this.supportedExchanges) {
      this.failureStates.set(exchange, {
        exchange,
        isHealthy: true,
        consecutiveFailures: 0,
        consecutiveSuccesses: 0,
        lastFailureTime: null,
        lastSuccessTime: null,
        circuitBreakerState: 'CLOSED',
        failureReasons: []
      });
    }

    this.basicLogger.info('故障状态初始化完成', {
      exchanges: this.supportedExchanges.length
    });
  }

  /**
   * 启动健康监控
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      return; // 已经在监控中
    }

    this.basicLogger.info('启动交易所健康监控', {
      interval: this.config.failureDetection.healthCheckInterval
    });

    // 立即执行一次健康检查
    this.performHealthChecks();

    // 设置定期健康检查
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, this.config.failureDetection.healthCheckInterval);
  }

  /**
   * 停止健康监控
   */
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      this.basicLogger.info('停止交易所健康监控');
    }
  }

  /**
   * 执行健康检查
   */
  @ErrorRecovery({
    maxRetries: 1,
    enableCircuitBreaker: false,
    context: { operation: 'healthCheck' }
  })
  private async performHealthChecks(): Promise<void> {
    const healthCheckPromises = this.supportedExchanges.map(exchange =>
      this.checkExchangeHealth(exchange)
    );

    const results = await Promise.allSettled(healthCheckPromises);

    results.forEach((result, index) => {
      const exchange = this.supportedExchanges[index];
      if (result.status === 'fulfilled') {
        this.updateExchangeHealthStatus(exchange, result.value);
      } else {
        this.basicLogger.error('交易所健康检查失败', {
          exchange,
          error: result.reason
        });
        this.recordExchangeFailure(exchange, `健康检查失败: ${result.reason}`);
      }
    });
  }

  /**
   * 检查单个交易所健康状态
   */
  private async checkExchangeHealth(exchange: string): Promise<boolean> {
    try {
      const startTime = Date.now();
      let isHealthy = false;

      switch (exchange) {
        case 'binance':
          isHealthy = await this.checkBinanceHealth();
          break;
        case 'okx':
          isHealthy = await this.checkOKXHealth();
          break;
        case 'coinbase':
          isHealthy = await this.checkCoinbaseHealth();
          break;
        case 'kraken':
          isHealthy = await this.checkKrakenHealth();
          break;
        case 'huobi':
          isHealthy = await this.checkHuobiHealth();
          break;
        case 'bybit':
          isHealthy = await this.checkBybitHealth();
          break;
        default:
          this.basicLogger.warn('未知交易所', { exchange });
          return false;
      }

      const responseTime = Date.now() - startTime;

      this.basicLogger.debug('交易所健康检查完成', {
        exchange,
        isHealthy,
        responseTime
      });

      return isHealthy;
    } catch (error) {
      this.basicLogger.error('交易所健康检查异常', {
        exchange,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * 检查Binance健康状态
   */
  private async checkBinanceHealth(): Promise<boolean> {
    try {
      const response = await fetch('https://api.binance.com/api/v3/ping', {
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 检查OKX健康状态
   */
  private async checkOKXHealth(): Promise<boolean> {
    try {
      const response = await fetch('https://www.okx.com/api/v5/public/time', {
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 检查Coinbase健康状态
   */
  private async checkCoinbaseHealth(): Promise<boolean> {
    try {
      const response = await fetch('https://api.coinbase.com/v2/time', {
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 检查Kraken健康状态
   */
  private async checkKrakenHealth(): Promise<boolean> {
    try {
      const response = await fetch('https://api.kraken.com/0/public/Time', {
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 检查Huobi健康状态
   */
  private async checkHuobiHealth(): Promise<boolean> {
    try {
      const response = await fetch('https://api.huobi.pro/v1/common/timestamp', {
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 检查Bybit健康状态
   */
  private async checkBybitHealth(): Promise<boolean> {
    try {
      const response = await fetch('https://api.bybit.com/v2/public/time', {
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 更新交易所健康状态
   */
  private updateExchangeHealthStatus(exchange: string, isHealthy: boolean): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) return;

    const now = new Date();

    if (isHealthy) {
      // 记录成功
      failureState.consecutiveSuccesses++;
      failureState.consecutiveFailures = 0;
      failureState.lastSuccessTime = now;

      // 检查是否需要恢复
      if (!failureState.isHealthy &&
          failureState.consecutiveSuccesses >= this.config.failureDetection.recoveryThreshold) {
        this.recoverExchange(exchange);
      }
    } else {
      // 记录失败
      this.recordExchangeFailure(exchange, '健康检查失败');
    }
  }

  /**
   * 记录交易所失败
   */
  private recordExchangeFailure(exchange: string, reason: string): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) return;

    const now = new Date();

    failureState.consecutiveFailures++;
    failureState.consecutiveSuccesses = 0;
    failureState.lastFailureTime = now;
    failureState.failureReasons.push(`${now.toISOString()}: ${reason}`);

    // 保持最近10个失败原因
    if (failureState.failureReasons.length > 10) {
      failureState.failureReasons = failureState.failureReasons.slice(-10);
    }

    this.basicLogger.warn('交易所失败记录', {
      exchange,
      reason,
      consecutiveFailures: failureState.consecutiveFailures,
      threshold: this.config.failureDetection.failureThreshold
    });

    // 检查是否需要标记为不健康
    if (failureState.isHealthy &&
        failureState.consecutiveFailures >= this.config.failureDetection.failureThreshold) {
      this.markExchangeUnhealthy(exchange);
    }
  }

  /**
   * 标记交易所为不健康
   */
  private markExchangeUnhealthy(exchange: string): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) return;

    failureState.isHealthy = false;

    if (this.config.recoveryStrategy.enableCircuitBreaker) {
      failureState.circuitBreakerState = 'OPEN';
    }

    this.basicLogger.error('交易所标记为不健康', {
      exchange,
      consecutiveFailures: failureState.consecutiveFailures,
      circuitBreakerState: failureState.circuitBreakerState
    });

    // 触发故障转移
    if (this.config.recoveryStrategy.enableAutoFailover) {
      this.triggerFailover(exchange);
    }
  }

  /**
   * 恢复交易所
   */
  private recoverExchange(exchange: string): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) return;

    failureState.isHealthy = true;
    failureState.circuitBreakerState = 'CLOSED';
    failureState.failureReasons = [];

    this.basicLogger.info('交易所恢复健康', {
      exchange,
      consecutiveSuccesses: failureState.consecutiveSuccesses
    });
  }

  /**
   * 触发故障转移
   */
  private triggerFailover(failedExchange: string): void {
    this.basicLogger.warn('触发故障转移', { failedExchange });

    try {
      // 1. 更新路由权重，降低故障交易所的权重
      this.updateFailoverWeights(failedExchange);

      // 2. 重新计算可用交易所列表
      this.recalculateAvailableExchanges();

      // 3. 通知监控系统
      this.notifyFailoverEvent(failedExchange);

      // 4. 记录故障转移事件
      this.recordFailoverEvent(failedExchange);

      this.basicLogger.info('故障转移完成', {
        failedExchange,
        availableExchanges: this.getAvailableExchanges().length
      });
    } catch (error) {
      this.basicLogger.error('故障转移执行失败', {
        failedExchange,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 更新故障转移权重
   */
  private updateFailoverWeights(failedExchange: string): void {
    // 将故障交易所的权重设为0
    if (this.config.weights) {
      this.config.weights[failedExchange] = 0;
    }

    // 重新分配其他交易所的权重
    const availableExchanges = this.supportedExchanges.filter(ex =>
      ex !== failedExchange && this.isExchangeAvailable(ex)
    );

    if (availableExchanges.length > 0) {
      const equalWeight = 1.0 / availableExchanges.length;
      availableExchanges.forEach(exchange => {
        if (this.config.weights) {
          this.config.weights[exchange] = equalWeight;
        }
      });
    }

    this.basicLogger.debug('权重已重新分配', {
      failedExchange,
      newWeights: this.config.weights
    });
  }

  /**
   * 重新计算可用交易所列表
   */
  private recalculateAvailableExchanges(): void {
    const availableCount = this.supportedExchanges.filter(ex =>
      this.isExchangeAvailable(ex)
    ).length;

    this.basicLogger.info('可用交易所重新计算完成', {
      totalExchanges: this.supportedExchanges.length,
      availableExchanges: availableCount,
      unavailableExchanges: this.supportedExchanges.length - availableCount
    });
  }

  /**
   * 通知故障转移事件
   */
  private notifyFailoverEvent(failedExchange: string): void {
    // 这里可以发送事件到事件总线或通知系统
    // 暂时使用日志记录
    this.basicLogger.warn('故障转移事件通知', {
      event: 'EXCHANGE_FAILOVER',
      failedExchange,
      timestamp: new Date().toISOString(),
      severity: 'HIGH'
    });
  }

  /**
   * 记录故障转移事件
   */
  private recordFailoverEvent(failedExchange: string): void {
    const event = {
      type: 'FAILOVER',
      exchange: failedExchange,
      timestamp: new Date(),
      availableExchanges: this.getAvailableExchanges()
    };

    // 记录到故障转移历史
    if (!this.failoverHistory) {
      this.failoverHistory = [];
    }
    this.failoverHistory.push(event);

    // 保持历史记录在合理范围内
    if (this.failoverHistory.length > 100) {
      this.failoverHistory = this.failoverHistory.slice(-50);
    }
  }

  /**
   * 获取可用交易所列表
   */
  private getAvailableExchanges(): string[] {
    return this.supportedExchanges.filter(exchange =>
      this.isExchangeAvailable(exchange)
    );
  }

  /**
   * 获取降级数据（从缓存或历史数据）
   */
  private async getDegradedData(request: DataRequest): Promise<any | null> {
    try {
      // 这里应该从缓存或数据库获取最近的历史数据
      // 暂时返回null，实际实现时需要集成缓存服务

      this.basicLogger.debug('尝试获取降级数据', {
        symbol: request.symbol,
        dataType: request.dataType
      });

      // TODO: 集成缓存服务获取历史数据
      // const cachedData = await this.cacheService.getLatest(request.symbol);
      // if (cachedData && this.isDataRecentEnough(cachedData)) {
      //   return cachedData;
      // }

      return null;
    } catch (error) {
      this.basicLogger.error('获取降级数据失败', {
        symbol: request.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * 检查数据是否足够新
   */
  private isDataRecentEnough(data: any): boolean {
    if (!data.timestamp) return false;

    const dataAge = Date.now() - new Date(data.timestamp).getTime();
    const maxAge = 5 * 60 * 1000; // 5分钟

    return dataAge <= maxAge;
  }

  /**
   * 检查交易所是否可用（考虑断路器状态）
   */
  private isExchangeAvailable(exchange: string): boolean {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) return false;

    // 如果交易所不健康，检查断路器状态
    if (!failureState.isHealthy) {
      if (failureState.circuitBreakerState === 'OPEN') {
        // 检查是否可以进入半开状态
        const now = Date.now();
        const lastFailureTime = failureState.lastFailureTime?.getTime() || 0;

        if (now - lastFailureTime > this.config.failureDetection.circuitBreakerTimeout) {
          failureState.circuitBreakerState = 'HALF_OPEN';
          this.basicLogger.info('断路器进入半开状态', { exchange });
          return true; // 允许尝试
        }

        return false; // 断路器开启，拒绝请求
      }
    }

    return failureState.isHealthy;
  }

  /**
   * 带故障恢复的数据请求
   */
  @ErrorRecovery({
    maxRetries: 3,
    enableCircuitBreaker: true,
    retryableErrorTypes: [ErrorType.NETWORK, ErrorType.EXTERNAL_API],
    context: { operation: 'dataRequest' }
  })
  async requestDataWithFailover(request: DataRequest): Promise<any> {
    const decision = await this.selectOptimalDataSource(request);

    // 尝试主要交易所
    try {
      const result = await this.executeDataRequest(decision.primaryExchange, request);

      // 记录成功
      this.recordExchangeSuccess(decision.primaryExchange);

      return {
        data: result,
        source: decision.primaryExchange,
        latency: Date.now() - request.timestamp.getTime(),
        fromFailover: false
      };
    } catch (primaryError) {
      this.basicLogger.warn('主要交易所请求失败，尝试备用交易所', {
        primaryExchange: decision.primaryExchange,
        error: primaryError instanceof Error ? primaryError.message : String(primaryError)
      });

      // 记录主要交易所失败
      this.recordExchangeFailure(
        decision.primaryExchange,
        `数据请求失败: ${primaryError instanceof Error ? primaryError.message : String(primaryError)}`
      );

      // 尝试备用交易所
      for (const backupExchange of decision.backupExchanges) {
        if (!this.isExchangeAvailable(backupExchange)) {
          continue;
        }

        try {
          const result = await this.executeDataRequest(backupExchange, request);

          // 记录备用交易所成功
          this.recordExchangeSuccess(backupExchange);

          this.basicLogger.info('备用交易所请求成功', {
            backupExchange,
            originalExchange: decision.primaryExchange
          });

          return {
            data: result,
            source: backupExchange,
            latency: Date.now() - request.timestamp.getTime(),
            fromFailover: true
          };
        } catch (backupError) {
          this.basicLogger.warn('备用交易所请求失败', {
            backupExchange,
            error: backupError instanceof Error ? backupError.message : String(backupError)
          });

          this.recordExchangeFailure(
            backupExchange,
            `备用请求失败: ${backupError instanceof Error ? backupError.message : String(backupError)}`
          );
        }
      }

      // 所有交易所都失败，尝试降级处理
      this.basicLogger.error('所有交易所都失败，启用降级模式', {
        primaryExchange: decision.primaryExchange,
        backupExchanges: decision.backupExchanges,
        requestSymbol: request.symbol
      });

      // 尝试从缓存获取历史数据作为降级
      const degradedData = await this.getDegradedData(request);
      if (degradedData) {
        this.basicLogger.warn('使用降级数据响应请求', {
          source: 'DEGRADED_CACHE',
          symbol: request.symbol
        });

        return {
          data: degradedData,
          source: 'DEGRADED_CACHE',
          latency: Date.now() - request.timestamp.getTime(),
          fromFailover: true,
          isDegraded: true,
          warning: '使用历史数据，可能不是最新价格'
        };
      }

      // 完全失败
      throw new Error(`所有交易所都无法提供数据，且无可用的降级数据: 主要(${decision.primaryExchange}), 备用(${decision.backupExchanges.join(', ')})`);
    }
  }

  /**
   * 执行具体的数据请求
   * 🔥 修复虚假实现：使用真实的交易所API调用
   */
  private async executeDataRequest(exchange: string, request: DataRequest): Promise<any> {
    try {
      // 根据交易所类型调用相应的适配器
      const adapter = this.getExchangeAdapter(exchange);
      if (!adapter) {
        throw new Error(`不支持的交易所: ${exchange}`);
      }

      // 根据数据类型执行相应的API调用
      switch (request.dataType) {
        case 'price':
          return await this.fetchPriceData(adapter, request);
        case 'kline':
          return await this.fetchKlineData(adapter, request);
        case 'trade':
          return await this.fetchTradeData(adapter, request);
        case 'orderbook':
          return await this.fetchOrderBookData(adapter, request);
        default:
          throw new Error(`不支持的数据类型: ${request.dataType}`);
      }
    } catch (error) {
      this.logger.error('交易所API调用失败', {
        exchange,
        request,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 记录交易所成功
   */
  private recordExchangeSuccess(exchange: string): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) return;

    failureState.consecutiveSuccesses++;
    failureState.consecutiveFailures = 0;
    failureState.lastSuccessTime = new Date();

    // 如果交易所之前不健康，检查是否可以恢复
    if (!failureState.isHealthy &&
        failureState.consecutiveSuccesses >= this.config.failureDetection.recoveryThreshold) {
      this.recoverExchange(exchange);
    }
  }

  /**
   * 智能数据源选择
   */
  async selectOptimalDataSource(request: DataRequest): Promise<DataSourceDecision> {
    const startTime = performance.now();

    try {
      // 1. 实时性能评估
      const performanceMetrics = await this.assessRealTimePerformance();

      // 2. 数据质量评估
      const qualityMetrics = await this.assessDataQuality();

      // 3. 可用性检查
      const availabilityStatus = await this.checkAvailability();

      // 4. 智能路由决策
      const routingDecision = await this.makeRoutingDecision({
        performanceMetrics,
        qualityMetrics,
        availabilityStatus,
        request
      });

      // 记录路由历史
      this.recordRoutingDecision(request, routingDecision, performance.now() - startTime);

      this.logger.debug('智能路由决策完成', {
        primaryExchange: routingDecision.primaryExchange,
        expectedLatency: routingDecision.expectedLatency,
        expectedQuality: routingDecision.expectedQuality,
        confidence: routingDecision.confidence,
        reason: routingDecision.routingReason
      });

      return routingDecision;
      
    } catch (error) {
      this.logger.error('智能路由决策失败', {
        error: error instanceof Error ? error.message : String(error),
        request
      });
      
      // 抛出真实错误，不使用降级决策
      throw new Error(`智能交易所路由失败: ${error instanceof Error ? error.message : String(error)}。请检查网络连接和交易所API配置。`);
    }
  }

  /**
   * 更新性能指标
   */
  updatePerformanceMetrics(exchange: string, metrics: Partial<PerformanceMetrics>): void {
    const existing = this.performanceMetrics.get(exchange) || {
      exchange,
      latency: 0,
      p95Latency: 0,
      p99Latency: 0,
      throughput: 0,
      errorRate: 0,
      availability: 1,
      lastUpdate: new Date()
    };
    
    const updated: PerformanceMetrics = {
      ...existing,
      ...metrics,
      lastUpdate: new Date()
    };
    
    this.performanceMetrics.set(exchange, updated);
    
    this.logger.debug('更新性能指标', {
      exchange,
      latency: updated.latency,
      availability: updated.availability,
      errorRate: updated.errorRate
    });
  }

  /**
   * 更新质量指标
   */
  updateQualityMetrics(exchange: string, metrics: Partial<QualityMetrics>): void {
    const existing = this.qualityMetrics.get(exchange) || {
      exchange,
      dataQuality: 100,
      completeness: 1,
      accuracy: 1,
      timeliness: 1,
      consistency: 1,
      anomalyRate: 0,
      lastUpdate: new Date()
    };
    
    const updated: QualityMetrics = {
      ...existing,
      ...metrics,
      lastUpdate: new Date()
    };
    
    this.qualityMetrics.set(exchange, updated);
    
    this.logger.debug('更新质量指标', {
      exchange,
      dataQuality: updated.dataQuality,
      completeness: updated.completeness,
      anomalyRate: updated.anomalyRate
    });
  }

  /**
   * 更新可用性状态
   */
  updateAvailabilityStatus(exchange: string, status: Partial<AvailabilityStatus>): void {
    const existing = this.availabilityStatus.get(exchange) || {
      exchange,
      isAvailable: true,
      connectionStatus: 'connected',
      lastHeartbeat: new Date(),
      consecutiveFailures: 0
    };
    
    const updated: AvailabilityStatus = {
      ...existing,
      ...status,
      lastHeartbeat: new Date()
    };
    
    this.availabilityStatus.set(exchange, updated);
    
    this.logger.debug('更新可用性状态', {
      exchange,
      isAvailable: updated.isAvailable,
      connectionStatus: updated.connectionStatus,
      consecutiveFailures: updated.consecutiveFailures
    });
  }

  /**
   * 实时性能评估
   */
  private async assessRealTimePerformance(): Promise<Map<string, PerformanceMetrics>> {
    const currentMetrics = new Map<string, PerformanceMetrics>();

    for (const exchange of this.supportedExchanges) {
      const metrics = this.performanceMetrics.get(exchange);
      if (metrics) {
        // 检查指标是否过期（超过30秒）
        const isStale = Date.now() - metrics.lastUpdate.getTime() > 30000;
        if (!isStale) {
          currentMetrics.set(exchange, metrics);
        } else {
          // 性能指标过期，抛出错误要求更新
          throw new Error(`交易所 ${exchange} 的性能指标已过期，需要更新监控数据`);
        }
      } else {
        // 缺少性能指标，抛出错误要求初始化
        throw new Error(`交易所 ${exchange} 缺少性能指标，需要初始化监控系统`);
      }
    }

    return currentMetrics;
  }

  /**
   * 数据质量评估
   */
  private async assessDataQuality(): Promise<Map<string, QualityMetrics>> {
    const currentMetrics = new Map<string, QualityMetrics>();

    for (const exchange of this.supportedExchanges) {
      const metrics = this.qualityMetrics.get(exchange);
      if (metrics) {
        // 检查指标是否过期
        const isStale = Date.now() - metrics.lastUpdate.getTime() > 60000; // 1分钟
        if (!isStale) {
          currentMetrics.set(exchange, metrics);
        } else {
          // 质量指标过期，抛出错误要求更新
          throw new Error(`交易所 ${exchange} 的质量指标已过期，需要更新数据质量监控`);
        }
      } else {
        // 缺少质量指标，抛出错误要求初始化
        throw new Error(`交易所 ${exchange} 缺少质量指标，需要初始化数据质量监控系统`);
      }
    }

    return currentMetrics;
  }

  /**
   * 可用性检查
   */
  private async checkAvailability(): Promise<Map<string, AvailabilityStatus>> {
    const currentStatus = new Map<string, AvailabilityStatus>();

    for (const exchange of this.supportedExchanges) {
      const status = this.availabilityStatus.get(exchange);
      if (status) {
        // 检查心跳是否过期（超过10秒）
        const isStale = Date.now() - status.lastHeartbeat.getTime() > 10000;
        if (isStale) {
          // 标记为不可用
          const updatedStatus: AvailabilityStatus = {
            ...status,
            isAvailable: false,
            connectionStatus: 'disconnected',
            consecutiveFailures: status.consecutiveFailures + 1
          };
          currentStatus.set(exchange, updatedStatus);
        } else {
          currentStatus.set(exchange, status);
        }
      } else {
        // 缺少可用性状态，抛出错误要求初始化
        throw new Error(`交易所 ${exchange} 缺少可用性状态，需要初始化连接监控系统`);
      }
    }

    return currentStatus;
  }

  /**
   * 智能路由决策
   */
  private async makeRoutingDecision(context: RoutingContext): Promise<DataSourceDecision> {
    const { performanceMetrics, qualityMetrics, availabilityStatus, request } = context;

    // 过滤可用的交易所
    const availableExchanges = this.filterAvailableExchanges(
      performanceMetrics,
      qualityMetrics,
      availabilityStatus,
      request
    );

    if (availableExchanges.length === 0) {
      throw new Error('没有可用的交易所满足要求');
    }

    // 根据算法选择最优交易所
    const rankedExchanges = this.rankExchanges(
      availableExchanges,
      performanceMetrics,
      qualityMetrics,
      availabilityStatus,
      request
    );

    const primaryExchange = rankedExchanges[0];
    const backupExchanges = rankedExchanges.slice(1, 3); // 最多2个备用

    // 计算预期指标
    const primaryMetrics = performanceMetrics.get(primaryExchange.exchange)!;
    const primaryQuality = qualityMetrics.get(primaryExchange.exchange)!;

    return {
      primaryExchange: primaryExchange.exchange,
      backupExchanges: backupExchanges.map(e => e.exchange),
      routingReason: this.generateRoutingReason(primaryExchange, request),
      expectedLatency: primaryMetrics.latency,
      expectedQuality: primaryQuality.dataQuality,
      confidence: primaryExchange.score,
      timestamp: new Date()
    };
  }

  /**
   * 过滤可用交易所
   */
  private filterAvailableExchanges(
    performanceMetrics: Map<string, PerformanceMetrics>,
    qualityMetrics: Map<string, QualityMetrics>,
    availabilityStatus: Map<string, AvailabilityStatus>,
    request: DataRequest
  ): string[] {
    const available: string[] = [];

    for (const exchange of this.supportedExchanges) {
      const perf = performanceMetrics.get(exchange);
      const quality = qualityMetrics.get(exchange);
      const status = availabilityStatus.get(exchange);

      if (!perf || !quality || !status) continue;

      // 🔥 首先检查故障状态和断路器
      if (!this.isExchangeAvailable(exchange)) {
        this.basicLogger.debug('交易所不可用（故障状态或断路器）', { exchange });
        continue;
      }

      // 检查基本可用性
      if (!status.isAvailable || status.connectionStatus !== 'connected') {
        continue;
      }

      // 检查性能阈值
      if (perf.latency > this.config.thresholds.maxLatency) {
        continue;
      }

      if (perf.errorRate > this.config.thresholds.maxErrorRate) {
        continue;
      }

      if (perf.availability < this.config.thresholds.minAvailability) {
        continue;
      }

      // 检查质量阈值
      if (quality.dataQuality < this.config.thresholds.minQuality) {
        continue;
      }

      // 根据请求优先级调整要求
      if (request.priority >= 8) { // 高优先级请求
        if (perf.latency > 500 || quality.dataQuality < 90) {
          continue;
        }
      }

      available.push(exchange);
    }

    return available;
  }

  /**
   * 交易所排名
   */
  private rankExchanges(
    availableExchanges: string[],
    performanceMetrics: Map<string, PerformanceMetrics>,
    qualityMetrics: Map<string, QualityMetrics>,
    availabilityStatus: Map<string, AvailabilityStatus>,
    request: DataRequest
  ): Array<{ exchange: string; score: number; breakdown: any }> {
    const ranked = availableExchanges.map(exchange => {
      const score = this.calculateExchangeScore(
        exchange,
        performanceMetrics.get(exchange)!,
        qualityMetrics.get(exchange)!,
        availabilityStatus.get(exchange)!,
        request
      );

      return {
        exchange,
        score: score.totalScore,
        breakdown: score.breakdown
      };
    });

    // 按分数降序排序
    ranked.sort((a, b) => b.score - a.score);

    return ranked;
  }

  /**
   * 计算交易所评分
   */
  private calculateExchangeScore(
    exchange: string,
    performance: PerformanceMetrics,
    quality: QualityMetrics,
    availability: AvailabilityStatus,
    request: DataRequest
  ): { totalScore: number; breakdown: any } {
    // 基础分数计算
    const latencyScore = this.calculateLatencyScore(performance.latency, request);
    const qualityScore = quality.dataQuality / 100;
    const availabilityScore = performance.availability;
    const reliabilityScore = 1 - performance.errorRate;

    // 自适应权重
    const adaptiveWeights = this.getAdaptiveWeights(exchange, request);

    // 加权总分
    const totalScore =
      latencyScore * adaptiveWeights.latency +
      qualityScore * adaptiveWeights.quality +
      availabilityScore * adaptiveWeights.availability +
      reliabilityScore * adaptiveWeights.reliability;

    const breakdown = {
      latencyScore,
      qualityScore,
      availabilityScore,
      reliabilityScore,
      weights: adaptiveWeights,
      totalScore
    };

    return { totalScore, breakdown };
  }

  /**
   * 计算延迟评分
   */
  private calculateLatencyScore(latency: number, request: DataRequest): number {
    // 根据请求优先级调整延迟容忍度
    const maxAcceptableLatency = request.priority >= 8 ? 200 :
                                 request.priority >= 5 ? 500 : 1000;

    if (latency <= 50) return 1.0;      // 优秀
    if (latency <= 100) return 0.9;     // 很好
    if (latency <= 200) return 0.8;     // 好
    if (latency <= 500) return 0.6;     // 一般
    if (latency <= maxAcceptableLatency) return 0.4; // 可接受

    return 0.1; // 差
  }

  /**
   * 获取自适应权重
   */
  private getAdaptiveWeights(exchange: string, request: DataRequest): any {
    const baseWeights = { ...this.config.weights };

    // 根据请求类型调整权重
    switch (request.dataType) {
      case 'price':
        baseWeights.latency *= 1.2; // 价格数据更注重延迟
        break;
      case 'orderbook':
        baseWeights.quality *= 1.3; // 订单簿更注重质量
        break;
      case 'trade':
        baseWeights.availability *= 1.1; // 交易数据更注重可用性
        break;
    }

    // 根据请求优先级调整
    if (request.priority >= 8) {
      baseWeights.latency *= 1.5;
      baseWeights.quality *= 1.2;
    }

    // 根据历史表现调整
    const exchangeWeight = this.exchangeWeights.get(exchange) || 1.0;

    return {
      latency: baseWeights.latency * exchangeWeight,
      quality: baseWeights.quality,
      availability: baseWeights.availability,
      reliability: 1 - baseWeights.latency - baseWeights.quality - baseWeights.availability
    };
  }

  /**
   * 生成路由原因
   */
  private generateRoutingReason(
    selectedExchange: { exchange: string; score: number; breakdown: any },
    request: DataRequest
  ): string {
    const { exchange, breakdown } = selectedExchange;
    const reasons: string[] = [];

    if (breakdown.latencyScore >= 0.8) {
      reasons.push('低延迟');
    }

    if (breakdown.qualityScore >= 0.9) {
      reasons.push('高质量');
    }

    if (breakdown.availabilityScore >= 0.95) {
      reasons.push('高可用性');
    }

    if (breakdown.reliabilityScore >= 0.95) {
      reasons.push('高可靠性');
    }

    if (reasons.length === 0) {
      reasons.push('最佳可用选项');
    }

    return `选择${exchange}: ${reasons.join(', ')} (评分: ${selectedExchange.score.toFixed(3)})`;
  }

  /**
   * 记录路由决策
   */
  private recordRoutingDecision(
    request: DataRequest,
    decision: DataSourceDecision,
    processingTime: number
  ): void {
    this.routingHistory.push({
      request,
      decision,
      success: true,
      timestamp: new Date()
    });

    // 保留最近1000条记录
    if (this.routingHistory.length > 1000) {
      this.routingHistory.shift();
    }
  }





  /**
   * 初始化交易所权重
   */
  private initializeExchangeWeights(): void {
    for (const exchange of this.supportedExchanges) {
      this.exchangeWeights.set(exchange, 1.0); // 初始权重为1.0
    }
  }

  /**
   * 启动监控
   */
  private startMonitoring(): void {
    // 每30秒更新一次指标
    this.metricsUpdateInterval = setInterval(() => {
      this.updateMetricsFromHistory();
    }, 30000);

    // 每5分钟优化一次权重
    this.optimizationInterval = setInterval(() => {
      this.optimizeExchangeWeights();
    }, 300000);
  }

  /**
   * 从历史记录更新指标
   */
  private updateMetricsFromHistory(): void {
    // 分析最近的路由历史，更新性能和质量指标
    const recentHistory = this.routingHistory.filter(
      record => Date.now() - record.timestamp.getTime() < 300000 // 最近5分钟
    );

    if (recentHistory.length === 0) return;

    // 按交易所分组统计
    const exchangeStats = new Map<string, any>();

    for (const record of recentHistory) {
      const exchange = record.decision.primaryExchange;

      if (!exchangeStats.has(exchange)) {
        exchangeStats.set(exchange, {
          totalRequests: 0,
          successfulRequests: 0,
          totalLatency: 0,
          totalQuality: 0
        });
      }

      const stats = exchangeStats.get(exchange);
      stats.totalRequests++;

      if (record.success) {
        stats.successfulRequests++;

        if (record.actualLatency) {
          stats.totalLatency += record.actualLatency;
        }

        if (record.actualQuality) {
          stats.totalQuality += record.actualQuality;
        }
      }
    }

    // 更新指标
    for (const [exchange, stats] of exchangeStats) {
      if (stats.totalRequests > 0) {
        const successRate = stats.successfulRequests / stats.totalRequests;
        const avgLatency = stats.successfulRequests > 0 ? stats.totalLatency / stats.successfulRequests : 0;
        const avgQuality = stats.successfulRequests > 0 ? stats.totalQuality / stats.successfulRequests : 0;

        // 更新性能指标
        if (avgLatency > 0) {
          this.updatePerformanceMetrics(exchange, {
            latency: avgLatency,
            errorRate: 1 - successRate,
            availability: successRate
          });
        }

        // 更新质量指标
        if (avgQuality > 0) {
          this.updateQualityMetrics(exchange, {
            dataQuality: avgQuality
          });
        }
      }
    }
  }

  /**
   * 优化交易所权重
   */
  private optimizeExchangeWeights(): void {
    // 基于历史表现调整交易所权重
    const recentHistory = this.routingHistory.filter(
      record => Date.now() - record.timestamp.getTime() < 3600000 // 最近1小时
    );

    if (recentHistory.length < 10) return; // 样本不足

    const exchangePerformance = new Map<string, number>();

    for (const record of recentHistory) {
      const exchange = record.decision.primaryExchange;

      if (!exchangePerformance.has(exchange)) {
        exchangePerformance.set(exchange, 0);
      }

      // 基于成功率和预期vs实际性能计算表现分数
      let performanceScore = record.success ? 1 : 0;

      if (record.actualLatency && record.decision.expectedLatency) {
        const latencyRatio = record.decision.expectedLatency / record.actualLatency;
        performanceScore *= Math.min(latencyRatio, 2); // 最多2倍奖励
      }

      if (record.actualQuality && record.decision.expectedQuality) {
        const qualityRatio = record.actualQuality / record.decision.expectedQuality;
        performanceScore *= Math.min(qualityRatio, 1.5); // 最多1.5倍奖励
      }

      const currentScore = exchangePerformance.get(exchange)!;
      exchangePerformance.set(exchange, currentScore + performanceScore);
    }

    // 标准化权重
    const totalPerformance = Array.from(exchangePerformance.values()).reduce((a, b) => a + b, 0);

    if (totalPerformance > 0) {
      for (const [exchange, performance] of exchangePerformance) {
        const normalizedWeight = performance / totalPerformance * this.supportedExchanges.length;
        this.exchangeWeights.set(exchange, Math.max(0.1, Math.min(2.0, normalizedWeight)));
      }

      this.logger.debug('优化交易所权重', {
        weights: Object.fromEntries(this.exchangeWeights),
        sampleSize: recentHistory.length
      });
    }
  }

  /**
   * 获取路由统计
   */
  getRoutingStatistics(): any {
    const recentHistory = this.routingHistory.filter(
      record => Date.now() - record.timestamp.getTime() < 3600000 // 最近1小时
    );

    const exchangeStats = new Map<string, any>();

    for (const record of recentHistory) {
      const exchange = record.decision.primaryExchange;

      if (!exchangeStats.has(exchange)) {
        exchangeStats.set(exchange, {
          requests: 0,
          successes: 0,
          totalConfidence: 0
        });
      }

      const stats = exchangeStats.get(exchange);
      stats.requests++;

      if (record.success) {
        stats.successes++;
      }

      stats.totalConfidence += record.decision.confidence;
    }

    const statistics: any = {
      totalRequests: recentHistory.length,
      exchangeDistribution: {},
      averageConfidence: 0,
      overallSuccessRate: 0
    };

    let totalSuccesses = 0;
    let totalConfidence = 0;

    for (const [exchange, stats] of exchangeStats) {
      statistics.exchangeDistribution[exchange] = {
        requests: stats.requests,
        successRate: stats.requests > 0 ? stats.successes / stats.requests : 0,
        averageConfidence: stats.requests > 0 ? stats.totalConfidence / stats.requests : 0
      };

      totalSuccesses += stats.successes;
      totalConfidence += stats.totalConfidence;
    }

    if (recentHistory.length > 0) {
      statistics.overallSuccessRate = totalSuccesses / recentHistory.length;
      statistics.averageConfidence = totalConfidence / recentHistory.length;
    }

    return statistics;
  }

  /**
   * 停止路由器
   */
  stop(): void {
    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
      this.metricsUpdateInterval = null;
    }

    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }

    this.logger.info('智能交易所路由器已停止');
  }

  /**
   * 获取所有交易所的故障状态
   */
  getExchangeFailureStates(): Map<string, ExchangeFailureState> {
    return new Map(this.failureStates);
  }

  /**
   * 获取特定交易所的故障状态
   */
  getExchangeFailureState(exchange: string): ExchangeFailureState | null {
    return this.failureStates.get(exchange) || null;
  }

  /**
   * 获取健康的交易所列表
   */
  getHealthyExchanges(): string[] {
    return Array.from(this.failureStates.entries())
      .filter(([_, state]) => state.isHealthy)
      .map(([exchange, _]) => exchange);
  }

  /**
   * 获取不健康的交易所列表
   */
  getUnhealthyExchanges(): string[] {
    return Array.from(this.failureStates.entries())
      .filter(([_, state]) => !state.isHealthy)
      .map(([exchange, _]) => exchange);
  }

  /**
   * 手动标记交易所为健康/不健康
   */
  setExchangeHealth(exchange: string, isHealthy: boolean): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) {
      this.basicLogger.warn('尝试设置未知交易所的健康状态', { exchange });
      return;
    }

    if (isHealthy) {
      this.recoverExchange(exchange);
    } else {
      this.markExchangeUnhealthy(exchange);
    }

    this.basicLogger.info('手动设置交易所健康状态', { exchange, isHealthy });
  }

  /**
   * 重置交易所故障状态
   */
  resetExchangeFailureState(exchange: string): void {
    const failureState = this.failureStates.get(exchange);
    if (!failureState) {
      this.basicLogger.warn('尝试重置未知交易所的故障状态', { exchange });
      return;
    }

    failureState.isHealthy = true;
    failureState.consecutiveFailures = 0;
    failureState.consecutiveSuccesses = 0;
    failureState.lastFailureTime = null;
    failureState.lastSuccessTime = null;
    failureState.circuitBreakerState = 'CLOSED';
    failureState.failureReasons = [];

    this.basicLogger.info('重置交易所故障状态', { exchange });
  }

  /**
   * 获取故障恢复统计信息
   */
  getFailureRecoveryStats(): {
    totalExchanges: number;
    healthyExchanges: number;
    unhealthyExchanges: number;
    circuitBreakersOpen: number;
    averageFailureRate: number;
  } {
    const states = Array.from(this.failureStates.values());
    const totalExchanges = states.length;
    const healthyExchanges = states.filter(s => s.isHealthy).length;
    const unhealthyExchanges = totalExchanges - healthyExchanges;
    const circuitBreakersOpen = states.filter(s => s.circuitBreakerState === 'OPEN').length;

    const totalFailures = states.reduce((sum, s) => sum + s.consecutiveFailures, 0);
    const averageFailureRate = totalExchanges > 0 ? totalFailures / totalExchanges : 0;

    return {
      totalExchanges,
      healthyExchanges,
      unhealthyExchanges,
      circuitBreakersOpen,
      averageFailureRate
    };
  }

  /**
   * 初始化交易所适配器
   * 🔥 修复虚假实现：使用真实的交易所适配器
   */
  private initializeExchangeAdapters(): void {
    try {
      // 初始化Binance适配器
      this.exchangeAdapters.set('binance', new BinanceAdapter());

      // 初始化OKX适配器
      this.exchangeAdapters.set('okx', new OKXAdapter());

      // 初始化Coinbase适配器
      this.exchangeAdapters.set('coinbase', new CoinbaseAdapter());

      this.logger.info('交易所适配器初始化完成', {
        adapters: Array.from(this.exchangeAdapters.keys())
      });
    } catch (error) {
      this.logger.error('交易所适配器初始化失败', { error });
      throw error;
    }
  }

  /**
   * 获取交易所适配器
   */
  private getExchangeAdapter(exchange: string): IExchangeAdapter | null {
    return this.exchangeAdapters.get(exchange.toLowerCase()) || null;
  }

  /**
   * 获取价格数据
   */
  private async fetchPriceData(adapter: IExchangeAdapter, request: DataRequest): Promise<any> {
    const symbol = TradingSymbol.fromString(request.symbol);
    const priceData = await adapter.getRealTimePrice(symbol);

    return {
      symbol: request.symbol,
      price: priceData.price.value,
      volume: priceData.volume24h.value,
      timestamp: priceData.timestamp,
      exchange: adapter.exchangeName
    };
  }

  /**
   * 获取K线数据
   */
  private async fetchKlineData(adapter: IExchangeAdapter, request: DataRequest): Promise<any> {
    const symbol = TradingSymbol.fromString(request.symbol);
    const timeframe = Timeframe.create('1h'); // 默认1小时

    const klineData = await adapter.getHistoricalKlines({
      symbol,
      timeframe,
      limit: 100
    });

    return {
      symbol: request.symbol,
      data: klineData,
      timestamp: new Date(),
      exchange: adapter.exchangeName
    };
  }

  /**
   * 获取交易数据（暂时返回价格数据）
   */
  private async fetchTradeData(adapter: IExchangeAdapter, request: DataRequest): Promise<any> {
    // 暂时使用价格数据代替交易数据
    return await this.fetchPriceData(adapter, request);
  }

  /**
   * 获取订单簿数据（暂时返回价格数据）
   */
  private async fetchOrderBookData(adapter: IExchangeAdapter, request: DataRequest): Promise<any> {
    // 暂时使用价格数据代替订单簿数据
    return await this.fetchPriceData(adapter, request);
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    this.stopHealthMonitoring();
    this.failureStates.clear();
    this.exchangeAdapters.clear();
    this.basicLogger.info('ExchangeRouter 资源已清理');
  }
}
