import { injectable } from 'inversify';
import {
  IExchangeAdapter,
  RealTimePriceData,
  KlineData,
  ExchangeSymbolInfo,
  HistoricalDataQuery,
  ExchangeError,
  ExchangeErrorType,
  ExchangeConfig
} from '../../domain/services/exchange-adapter';
import { Price } from '../../domain/value-objects/price';
import { Volume } from '../../domain/value-objects/volume';
import { TradingSymbol } from '../../domain/value-objects/trading-symbol';
import { Timeframe } from '../../domain/value-objects/timeframe';
import { BaseHttpClient } from '../../../../shared/infrastructure/http/base-http-client';
import { HttpClientFactory, HttpClientType } from '../../../../shared/infrastructure/http/http-client-factory';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { getLogger } from '../../../../shared/infrastructure/logging/logger-factory';

// Infrastructure层定义自己的ServiceHealthStatus类型，避免依赖Application层
export interface ServiceHealthStatus {
  isHealthy: boolean;
  lastCheck: Date;
  errorMessage?: string;
}
/**
 * Binance API响应类型
 */
interface BinancePriceResponse {
  symbol: string;
  lastPrice: string;
  priceChange: string;
  priceChangePercent: string;
  volume: string;
  highPrice: string;
  lowPrice: string;
  openTime: number;
  closeTime: number;
  count: number;
}

interface BinanceKlineResponse {
  [index: number]: string | number;
  0: number; // 开盘时间
  1: string; // 开盘价
  2: string; // 最高价
  3: string; // 最低价
  4: string; // 收盘价
  5: string; // 成交量
  6: number; // 收盘时间
  7: string; // 成交额
  8: number; // 成交笔数
  9: string; // 主动买入成交量
  10: string; // 主动买入成交额
  11: string; // 请忽略该参数
}

interface BinanceSymbolResponse {
  symbol: string;
  status: string;
  baseAsset: string;
  quoteAsset: string;
  filters: Array<{
    filterType: string;
    minPrice?: string;
    maxPrice?: string;
    tickSize?: string;
    minQty?: string;
    maxQty?: string;
    stepSize?: string;
    minNotional?: string;
  }>;
}

/**
 * Binance交易所适配器
 * 重构后继承ExternalDataAdapterBase，消除重复实现
 */
@injectable()
export class BinanceAdapter implements IExchangeAdapter {
  readonly adapterName = 'binance';
  readonly sourceName = 'binance';
  public readonly serviceName = 'Binance';
  public readonly exchangeName = 'binance';

  // HTTP客户端和日志记录器
  protected httpClient: BaseHttpClient;
  protected logger: IBasicLogger;

  // Binance时间框架映射
  private readonly timeframeMap: Record<string, string> = {
    '1m': '1m',
    '3m': '3m',
    '5m': '5m',
    '15m': '15m',
    '30m': '30m',
    '1h': '1h',
    '2h': '2h',
    '4h': '4h',
    '6h': '6h',
    '8h': '8h',
    '12h': '12h',
    '1d': '1d',
    '3d': '3d',
    '1w': '1w',
    '1M': '1M',
  };

  constructor(config?: Partial<ExchangeConfig>) {
    // 初始化日志记录器
    this.logger = getLogger('BinanceAdapter');

    // 初始化HTTP客户端
    const httpClientFactory = new HttpClientFactory(this.logger);
    this.httpClient = httpClientFactory.createClient(
      'binance',
      'https://api.binance.com',
      HttpClientType.EXCHANGE,
      {
        timeout: 15000,
        headers: {
          'User-Agent': 'crypto-monitor/1.0.0',
        },
        retryConfig: {
          maxRetries: 3,
          baseDelay: 1000,
          maxDelay: 10000
        },
        rateLimitConfig: {
          requestsPerSecond: 20,
          burstSize: 50
        }
      }
    );
  }

  /**
   * 获取实时价格数据
   * 重构后使用统一的数据处理管道
   */
  async getRealTimePrice(symbol: TradingSymbol): Promise<RealTimePriceData> {
    const binanceSymbol = symbol.toBinanceFormat();

    const response = await this.httpClient.get<BinancePriceResponse>(
      '/api/v3/ticker/24hr',
      {
        params: { symbol: binanceSymbol }
      }
    );
    const rawData = response.data;

    return this.transformPriceData(rawData, symbol);
  }

  /**
   * 批量获取实时价格数据
   * 重构后使用统一的数据处理管道
   */
  async getRealTimePrices(symbols: TradingSymbol[]): Promise<RealTimePriceData[]> {
    const response = await this.httpClient.get<BinancePriceResponse[]>(
      '/api/v3/ticker/24hr'
    );
    const allPricesData = response.data;

    const symbolMap = new Map(symbols.map(s => [s.toBinanceFormat(), s]));

    return allPricesData
      .filter(item => symbolMap.has(item.symbol))
      .map(item => this.transformPriceData(item, symbolMap.get(item.symbol)!));
  }

  /**
   * 获取历史K线数据
   * 重构后使用统一的数据处理管道
   */
  async getHistoricalKlines(query: HistoricalDataQuery): Promise<KlineData[]> {
    const binanceSymbol = query.symbol.toBinanceFormat();
    const interval = this.timeframeMap[query.timeframe.value];

    if (!interval) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_TIMEFRAME,
        this.exchangeName,
        `不支持的时间框架: ${query.timeframe.value}`
      );
    }

    const params: any = {
      symbol: binanceSymbol,
      interval,
      limit: query.limit || 500,
    };

    if (query.startTime) {
      params.startTime = query.startTime.getTime();
    }
    if (query.endTime) {
      params.endTime = query.endTime.getTime();
    }

    // 生成缓存键
    const cacheKey = `klines:${binanceSymbol}:${interval}:${params.startTime || 'latest'}:${params.endTime || 'latest'}:${params.limit}`;

    const response = await this.httpClient.get<BinanceKlineResponse[]>(
      '/api/v3/klines',
      { params }
    );
    const rawData = response.data;

    return rawData.map(item => this.transformKlineData(item, query.symbol, query.timeframe));
  }

  /**
   * 获取交易对信息
   * 重构后使用统一的数据处理管道
   */
  async getSymbolInfo(symbol: TradingSymbol): Promise<ExchangeSymbolInfo> {
    const binanceSymbol = symbol.toBinanceFormat();

    const response = await this.httpClient.get<{ symbols: BinanceSymbolResponse[] }>(
      '/api/v3/exchangeInfo'
    );
    const exchangeInfo = response.data;

    const symbolData = exchangeInfo.symbols.find(
      (s: BinanceSymbolResponse) => s.symbol === binanceSymbol
    );

    if (!symbolData) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_SYMBOL,
        this.exchangeName,
        `交易对不存在: ${symbol.symbol}`
      );
    }

    return this.transformSymbolInfo(symbolData, symbol);
  }

  /**
   * 获取所有交易对信息
   */
  async getAllSymbols(): Promise<ExchangeSymbolInfo[]> {
    try {
      const response = await this.httpClient.get('/api/v3/exchangeInfo');

      // 只返回BTC相关的USDT交易对
      const results: ExchangeSymbolInfo[] = [];

      for (const symbolData of response.data.symbols) {
        // 过滤条件：只要BTC相关的USDT交易对
        if (!symbolData.symbol.includes('BTC') || !symbolData.symbol.endsWith('USDT') || symbolData.status !== 'TRADING') {
          continue;
        }

        try {
          const symbol = TradingSymbol.fromBinanceFormat(symbolData.symbol);
          const symbolInfo = this.transformSymbolInfo(symbolData, symbol);
          results.push(symbolInfo);
        } catch (error) {
          this.logger.warn('跳过无效交易对', {
            symbol: symbolData.symbol,
            error: error instanceof Error ? error.message : '未知错误'
          });
          continue;
        }
      }

      return results;
    } catch (error) {
      throw this.handleError(error, '/api/v3/klines');
    }
  }

  /**
   * 获取服务器时间
   */
  async getServerTime(): Promise<Date> {
    try {
      const response = await this.httpClient.get('/api/v3/time');
      return new Date(response.data.serverTime);
    } catch (error) {
      throw this.handleError(error, '/api/v3/time');
    }
  }

  /**
   * 检查服务是否可用
   */
  async isAvailable(): Promise<boolean> {
    try {
      await this.httpClient.get('/api/v3/ping');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    return this.isAvailable();
  }

  /**
   * 获取健康状态 - 实现IExchangeAdapter接口
   */
  async getHealthStatus(): Promise<import('../../../../shared/application/interfaces/external-service').ServiceHealthStatus> {
    try {
      const isAvailable = await this.isAvailable();
      const startTime = Date.now();

      // 测试一个简单的API调用来获取延迟
      await this.httpClient.get('/api/v3/time');
      const latency = Date.now() - startTime;

      return {
        isHealthy: isAvailable,
        lastCheck: new Date(),
        responseTime: latency,
        errorMessage: isAvailable ? undefined : 'Service unavailable'
      };
    } catch (error) {
      return {
        isHealthy: false,
        lastCheck: new Date(),
        responseTime: 0,
        errorMessage: error instanceof Error ? error.message : String(error)
      };
    }
  }



  /**
   * 转换价格数据
   */
  private transformPriceData(data: BinancePriceResponse, symbol: TradingSymbol): RealTimePriceData {
    try {
      // 调试日志：检查原始数据
      this.logger.debug('Binance原始价格数据', {
        symbol: symbol.symbol,
        rawData: data
      });

      // 解析并验证每个字段 - 使用正确的字段名
      const price = parseFloat(data.lastPrice);
      const priceChange = parseFloat(data.priceChange);
      const priceChangePercent = parseFloat(data.priceChangePercent);
      const volume = parseFloat(data.volume);
      const highPrice = parseFloat(data.highPrice);
      const lowPrice = parseFloat(data.lowPrice);

      // 验证解析结果
      this.logger.debug('Binance解析后数据', {
        symbol: symbol.symbol,
        price,
        priceChange,
        priceChangePercent,
        volume,
        highPrice,
        lowPrice,
        isFinitePrice: isFinite(price),
        isFinitePriceChange: isFinite(priceChange),
        isFiniteVolume: isFinite(volume)
      });

      // 检查关键字段
      if (!isFinite(price)) {
        throw new Error(`价格解析失败: ${data.lastPrice} -> ${price}`);
      }
      if (!isFinite(priceChange)) {
        throw new Error(`价格变化解析失败: ${data.priceChange} -> ${priceChange}`);
      }
      if (!isFinite(volume)) {
        throw new Error(`成交量解析失败: ${data.volume} -> ${volume}`);
      }

      return {
        symbol,
        price: new Price(price),
        change24h: priceChange, // 价格变化保持原始值（可以为负数）
        changePercent24h: priceChangePercent,
        volume24h: new Volume(volume),
        high24h: new Price(highPrice),
        low24h: new Price(lowPrice),
        timestamp: new Date(data.closeTime),
      };
    } catch (error) {
      this.logger.error('Binance价格数据转换失败', {
        symbol: symbol.symbol,
        error: error instanceof Error ? error.message : String(error),
        rawData: data
      });
      throw error;
    }
  }

  /**
   * 转换K线数据
   */
  private transformKlineData(
    data: BinanceKlineResponse,
    symbol: TradingSymbol,
    timeframe: Timeframe
  ): KlineData {
    return {
      symbol,
      timeframe,
      openTime: new Date(data[0] as number),
      closeTime: new Date(data[6] as number),
      open: new Price(parseFloat(data[1] as string)),
      high: new Price(parseFloat(data[2] as string)),
      low: new Price(parseFloat(data[3] as string)),
      close: new Price(parseFloat(data[4] as string)),
      volume: new Volume(parseFloat(data[5] as string)),
      trades: data[8] as number,
      quoteVolume: new Volume(parseFloat(data[7] as string)),
    };
  }

  /**
   * 转换交易对信息
   */
  private transformSymbolInfo(data: BinanceSymbolResponse, symbol: TradingSymbol): ExchangeSymbolInfo {
    const info: ExchangeSymbolInfo = {
      symbol,
      status: data.status,
      baseAsset: data.baseAsset,
      quoteAsset: data.quoteAsset,
    };

    // 解析过滤器
    for (const filter of data.filters) {
      switch (filter.filterType) {
        case 'PRICE_FILTER':
          info.minPrice = filter.minPrice ? parseFloat(filter.minPrice) : undefined;
          info.maxPrice = filter.maxPrice ? parseFloat(filter.maxPrice) : undefined;
          info.tickSize = filter.tickSize ? parseFloat(filter.tickSize) : undefined;
          break;
        case 'LOT_SIZE':
          info.minQty = filter.minQty ? parseFloat(filter.minQty) : undefined;
          info.maxQty = filter.maxQty ? parseFloat(filter.maxQty) : undefined;
          info.stepSize = filter.stepSize ? parseFloat(filter.stepSize) : undefined;
          break;
        case 'MIN_NOTIONAL':
          info.minNotional = filter.minNotional ? parseFloat(filter.minNotional) : undefined;
          break;
      }
    }

    return info;
  }

  /**
   * 简化的错误处理方法
   */
  protected handleError(error: any, endpoint: string): Error {
    let message = `Binance API请求失败: ${endpoint}`;

    if (error.response?.status === 429) {
      message = 'Binance API请求频率超限';
    } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      message = 'Binance API请求超时';
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      message = 'Binance API网络连接失败';
    } else if (error.response?.status >= 400 && error.response?.status < 500) {
      message = error.response?.data?.msg || error.message || 'Binance API错误';
    }

    return new Error(message);
  }
}
