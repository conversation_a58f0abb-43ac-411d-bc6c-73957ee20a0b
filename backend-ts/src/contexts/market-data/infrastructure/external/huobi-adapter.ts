import { injectable } from 'inversify';
import {
  IExchangeAdapter,
  RealTimePriceData,
  KlineData,
  ExchangeSymbolInfo,
  HistoricalDataQuery,
  ExchangeError,
  ExchangeErrorType,
  ExchangeConfig
} from '../../domain/services/exchange-adapter';
import { Price } from '../../domain/value-objects/price';
import { Volume } from '../../domain/value-objects/volume';
import { TradingSymbol } from '../../domain/value-objects/trading-symbol';
import { Timeframe } from '../../domain/value-objects/timeframe';
import { ServiceHealthStatus } from '../../../../shared/application/interfaces/external-service';
import { ExternalDataAdapterBase, AdapterConfig, AdapterHealthStatus } from '../../../../shared/infrastructure/data-processing';
import { getLogger } from '../../../../config/logging';

/**
 * Huobi API响应类型定义
 */
interface HuobiBaseResponse<T> {
  status: string;
  data: T;
  'err-code'?: string;
  'err-msg'?: string;
  ts: number;
}

interface HuobiTickerData {
  open: number;
  high: number;
  low: number;
  close: number;
  amount: number;
  vol: number;
  count: number;
  bid: number;
  bidSize: number;
  ask: number;
  askSize: number;
  ts: number;
}

interface HuobiKlineData {
  id: number;
  open: number;
  close: number;
  low: number;
  high: number;
  amount: number;
  vol: number;
  count: number;
}

interface HuobiSymbolData {
  symbol: string;
  'base-currency': string;
  'quote-currency': string;
  'price-precision': number;
  'amount-precision': number;
  'symbol-partition': string;
  state: string;
  'min-order-amt': number;
  'max-order-amt': number;
  'min-order-value': number;
  'limit-order-min-order-amt': number;
  'limit-order-max-order-amt': number;
  'sell-market-min-order-amt': number;
  'sell-market-max-order-amt': number;
  'buy-market-max-order-value': number;
}

/**
 * Huobi交易所适配器
 * 重构后继承ExternalDataAdapterBase，消除重复实现
 */
@injectable()
export class HuobiAdapter extends ExternalDataAdapterBase implements IExchangeAdapter {
  readonly adapterName = 'huobi';
  readonly sourceName = 'huobi';
  public readonly serviceName = 'Huobi';
  public readonly exchangeName = 'huobi';

  // Huobi时间框架映射
  private readonly timeframeMap: Record<string, string> = {
    '1m': '1min',
    '5m': '5min',
    '15m': '15min',
    '30m': '30min',
    '1h': '60min',
    '4h': '4hour',
    '1d': '1day',
    '1w': '1week',
    '1M': '1mon',
  };

  constructor(
    config?: AdapterConfig,
    pipeline?: any,
    logger?: any,
    errorHandler?: any
  ) {
    // 如果没有提供配置，使用默认配置
    const adapterConfig: AdapterConfig = config || {
      name: 'huobi',
      baseUrl: 'https://api.huobi.pro',
      timeout: 10000,
      maxRetries: 3,
      rateLimitConfig: {
        requestsPerSecond: 10, // Huobi限制相对宽松
        burstSize: 20
      },
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'crypto-monitor/1.0.0',
      },
      enableCache: true,
      cacheValidityMinutes: 1 // 实时数据缓存1分钟
    };

    // 调用基类构造函数，自动集成数据处理管道
    super(adapterConfig, pipeline, logger, errorHandler);
  }

  /**
   * 获取实时价格数据
   * 重构后使用统一的数据处理管道
   */
  async getRealTimePrice(symbol: TradingSymbol): Promise<RealTimePriceData> {
    const huobiSymbol = symbol.toHuobiFormat();

    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<HuobiBaseResponse<HuobiTickerData>>(
      '/market/detail/merged',
      {
        params: { symbol: huobiSymbol },
        useCache: true,
        cacheKey: `ticker:${huobiSymbol}`
      },
      {
        dataType: 'ticker',
        businessSystem: 'market-data',
        metadata: {
          symbol: symbol.symbol,
          exchange: 'huobi'
        }
      }
    );

    // 处理Huobi特有的错误格式
    if (rawData.status !== 'ok') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `Huobi API错误: ${rawData['err-msg'] || '未知错误'}`
      );
    }

    return this.transformPriceData(rawData.data, symbol);
  }

  /**
   * 批量获取实时价格数据
   * 重构后使用统一的数据处理管道
   */
  async getRealTimePrices(symbols: TradingSymbol[]): Promise<RealTimePriceData[]> {
    // Huobi不支持批量获取特定符号，需要获取所有符号然后过滤
    const rawData = await this.fetchAndProcess<HuobiBaseResponse<HuobiTickerData[]>>(
      '/market/tickers',
      {
        useCache: true,
        cacheKey: 'all-tickers',
        timeout: 15000 // 批量请求超时时间更长
      },
      {
        dataType: 'batch-ticker',
        businessSystem: 'market-data',
        metadata: {
          symbols: symbols.map(s => s.symbol),
          exchange: 'huobi',
          requestType: 'batch'
        }
      }
    );

    // 处理Huobi特有的错误格式
    if (rawData.status !== 'ok') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `Huobi API错误: ${rawData['err-msg'] || '未知错误'}`
      );
    }

    const results: RealTimePriceData[] = [];
    const symbolMap = new Map(symbols.map(s => [s.toHuobiFormat(), s]));

    // 注意：Huobi的tickers接口返回的数据结构可能不同，需要适配
    for (const tickerData of rawData.data) {
      // 这里需要根据实际API响应调整
      const huobiSymbol = (tickerData as any).symbol;
      const symbol = symbolMap.get(huobiSymbol);
      if (symbol) {
        results.push(this.transformPriceData(tickerData, symbol));
      }
    }

    return results;
  }

  /**
   * 获取历史K线数据
   * 重构后使用统一的数据处理管道
   */
  async getHistoricalKlines(query: HistoricalDataQuery): Promise<KlineData[]> {
    const huobiSymbol = query.symbol.toHuobiFormat();
    const period = this.timeframeMap[query.timeframe.value];

    if (!period) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_TIMEFRAME,
        this.exchangeName,
        `不支持的时间框架: ${query.timeframe.value}`
      );
    }

    const params: any = {
      symbol: huobiSymbol,
      period,
      size: query.limit || 200,
    };

    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<HuobiBaseResponse<HuobiKlineData[]>>(
      '/market/history/kline',
      {
        params,
        useCache: false, // 历史数据不缓存
        timeout: 20000 // 历史数据请求超时时间更长
      },
      {
        dataType: 'kline',
        businessSystem: 'market-data',
        metadata: {
          symbol: query.symbol.symbol,
          timeframe: query.timeframe.value,
          exchange: 'huobi',
          limit: query.limit
        }
      }
    );

    // 处理Huobi特有的错误格式
    if (rawData.status !== 'ok') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `Huobi API错误: ${rawData['err-msg'] || '未知错误'}`
      );
    }

    // Huobi返回的数据是按时间倒序的，需要反转
    const sortedData = rawData.data.reverse();

    return sortedData.map(item => this.transformKlineData(item, query.symbol as TradingSymbol, query.timeframe));
  }

  /**
   * 获取交易对信息
   * 重构后使用统一的数据处理管道
   */
  async getSymbolInfo(symbol: TradingSymbol): Promise<ExchangeSymbolInfo> {
    const huobiSymbol = symbol.toHuobiFormat();

    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<HuobiBaseResponse<HuobiSymbolData[]>>(
      '/v1/common/symbols',
      {
        useCache: true,
        cacheKey: 'all-symbols', // 复用所有交易对的缓存
        timeout: 10000
      },
      {
        dataType: 'symbol-info',
        businessSystem: 'market-data',
        metadata: {
          symbol: symbol.symbol,
          exchange: 'huobi'
        }
      }
    );

    // 处理Huobi特有的错误格式
    if (rawData.status !== 'ok') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `Huobi API错误: ${rawData['err-msg'] || '未知错误'}`
      );
    }

    const symbolData = rawData.data.find(s => s.symbol === huobiSymbol);
    if (!symbolData) {
      throw new ExchangeError(
        ExchangeErrorType.INVALID_SYMBOL,
        this.exchangeName,
        `交易对不存在: ${symbol.symbol}`
      );
    }

    return this.transformSymbolInfo(symbolData, symbol);
  }

  /**
   * 获取所有交易对信息
   * 重构后使用统一的数据处理管道
   */
  async getAllSymbols(): Promise<ExchangeSymbolInfo[]> {
    // 使用统一的数据处理管道
    const rawData = await this.fetchAndProcess<HuobiBaseResponse<HuobiSymbolData[]>>(
      '/v1/common/symbols',
      {
        useCache: true,
        cacheKey: 'all-symbols',
        timeout: 15000 // 获取所有交易对可能需要更长时间
      },
      {
        dataType: 'all-symbols',
        businessSystem: 'market-data',
        metadata: {
          exchange: 'huobi',
          requestType: 'bulk-symbols'
        }
      }
    );

    // 处理Huobi特有的错误格式
    if (rawData.status !== 'ok') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `Huobi API错误: ${rawData['err-msg'] || '未知错误'}`
      );
    }

    const results: ExchangeSymbolInfo[] = [];

    for (const symbolData of rawData.data) {
      // 只返回USDT交易对
      if (!symbolData.symbol.endsWith('usdt') || symbolData.state !== 'online') {
        continue;
      }

      try {
        const symbol = TradingSymbol.fromHuobiFormat(symbolData.symbol);
        const symbolInfo = this.transformSymbolInfo(symbolData, symbol);
        results.push(symbolInfo);
      } catch (error) {
        this.logger.warn('跳过无效交易对', {
          symbol: symbolData.symbol,
          error: error instanceof Error ? error.message : '未知错误'
        });
        continue;
      }
    }

    return results;
  }

  /**
   * 检查交易所服务是否可用
   * 重构后使用基类的健康检查机制
   */
  async isAvailable(): Promise<boolean> {
    try {
      const healthStatus = await super.healthCheck();
      return healthStatus.isHealthy;
    } catch {
      return false;
    }
  }



  /**
   * 获取服务器时间
   * 重构后使用统一的数据处理管道
   */
  async getServerTime(): Promise<Date> {
    const rawData = await this.fetchAndProcess<HuobiBaseResponse<number>>(
      '/v1/common/timestamp',
      {
        useCache: false, // 服务器时间不缓存
        timeout: 5000
      },
      {
        dataType: 'server-time',
        businessSystem: 'market-data',
        metadata: {
          exchange: 'huobi',
          requestType: 'server-time'
        }
      }
    );

    // 处理Huobi特有的错误格式
    if (rawData.status !== 'ok') {
      throw new ExchangeError(
        ExchangeErrorType.API_ERROR,
        this.exchangeName,
        `Huobi API错误: ${rawData['err-msg'] || '未知错误'}`
      );
    }

    return new Date(rawData.data);
  }

  /**
   * 测试连接
   * 重构后使用基类的健康检查机制
   */
  async testConnection(): Promise<boolean> {
    try {
      const healthStatus = await (this as any).getHealthStatus();
      return healthStatus.isHealthy || healthStatus.status === 'healthy';
    } catch {
      return false;
    }
  }

  // 健康检查逻辑已由基类ExternalDataAdapterBase提供
  // 基类的getHealthStatus()方法会自动调用testConnection()并处理错误

  /**
   * 转换价格数据
   */
  private transformPriceData(ticker: HuobiTickerData, symbol: TradingSymbol): RealTimePriceData {
    const currentPrice = ticker.close;
    const openPrice = ticker.open;
    const change24h = currentPrice - openPrice;
    const changePercent24h = openPrice === 0 ? 0 : (change24h / openPrice) * 100;

    return {
      symbol,
      price: new Price(currentPrice),
      change24h, // 价格变化保持原始值（可以为负数）
      changePercent24h,
      volume24h: new Volume(ticker.vol),
      high24h: new Price(ticker.high),
      low24h: new Price(ticker.low),
      timestamp: new Date(ticker.ts),
    };
  }

  /**
   * 转换K线数据
   */
  private transformKlineData(
    data: HuobiKlineData,
    symbol: TradingSymbol,
    timeframe: Timeframe
  ): KlineData {
    const openTime = new Date(data.id * 1000);
    const closeTime = new Date(openTime.getTime() + timeframe.milliseconds - 1);

    return {
      symbol,
      timeframe,
      openTime,
      closeTime,
      open: new Price(data.open),
      high: new Price(data.high),
      low: new Price(data.low),
      close: new Price(data.close),
      volume: new Volume(data.vol),
      trades: data.count,
    };
  }

  /**
   * 转换交易对信息
   */
  private transformSymbolInfo(symbolData: HuobiSymbolData, symbol: TradingSymbol): ExchangeSymbolInfo {
    return {
      symbol,
      baseAsset: symbolData['base-currency'].toUpperCase(),
      quoteAsset: symbolData['quote-currency'].toUpperCase(),
      status: symbolData.state === 'online' ? 'TRADING' : 'INACTIVE',
      minOrderSize: symbolData['min-order-amt'],
      maxOrderSize: symbolData['max-order-amt'],
      tickSize: Math.pow(10, -symbolData['price-precision']),
      stepSize: Math.pow(10, -symbolData['amount-precision']),
      pricePrecision: symbolData['price-precision'],
      quantityPrecision: symbolData['amount-precision'],
      filters: [],
    };
  }

  // 错误处理逻辑已由基类ExternalDataAdapterBase和数据处理管道提供
  // 基类会自动处理HTTP错误、速率限制、网络错误等常见情况
  // 数据处理管道会统一处理所有错误并记录日志
}
