/**
 * 配置热更新服务
 * 监听用户配置变更并实时生效，无需重启服务
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { IEventBus, IDomainEventHandler, DomainEvent } from '../../../../shared/domain/events/domain-event';
import { getRedis } from '../../../../shared/infrastructure/messaging/redis';
import { UnifiedEnvironmentManager } from '../../../../shared/infrastructure/config/environment/unified-environment-manager';
import { TIME_CONSTANTS } from '../../../../shared/infrastructure/config/unified-default-config';

/**
 * 用户配置变更事件
 */
export class UserConfigChangedEvent extends DomainEvent {
  readonly occurredOn: Date;

  constructor(
    public readonly userId: string,
    public readonly configType: 'llmConfig' | 'modelPreference',
    public readonly providerName?: string,
    public readonly scenario?: string,
    public readonly changeDetails?: Record<string, any>
  ) {
    super(userId, {
      configType,
      providerName,
      scenario,
      changeDetails
    });
    this.occurredOn = new Date();
  }
}

/**
 * 配置缓存清理事件
 */
export class ConfigCacheClearEvent extends DomainEvent {
  readonly occurredOn: Date;

  constructor(
    public readonly userId: string,
    public readonly cacheKeys: string[]
  ) {
    super(userId, { cacheKeys });
    this.occurredOn = new Date();
  }
}

/**
 * 配置回滚事件
 */
export class ConfigRollbackEvent extends DomainEvent {
  readonly occurredOn: Date;

  constructor(
    public readonly userId: string,
    public readonly configType: string,
    public readonly reason: string,
    public readonly previousConfig?: any
  ) {
    super(userId, { configType, reason, previousConfig });
    this.occurredOn = new Date();
  }
}

/**
 * 配置验证结果
 */
interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 配置备份项
 */
interface ConfigBackup {
  userId: string;
  configType: string;
  config: any;
  timestamp: Date;
  version: string;
}

/**
 * 配置热更新服务
 */
@injectable()
export class ConfigHotReloadService implements IDomainEventHandler<UserConfigChangedEvent> {
  private readonly configCache = new Map<string, any>();
  private readonly providerInstances = new Map<string, any>();
  private readonly lastUpdateTimes = new Map<string, number>();
  private readonly configBackups = new Map<string, ConfigBackup[]>();
  private readonly reloadLocks = new Map<string, Promise<void>>();
  
  // 缓存键前缀
  private readonly CACHE_PREFIX = {
    USER_CONFIG: 'userConfig:',
    MODEL_PREFERENCE: 'modelPref:',
    PROVIDER_INSTANCE: 'provider:',
    SELECTION_CACHE: 'selection:'
  };

  // 配置更新冷却期（毫秒）
  private readonly UPDATE_COOLDOWN = TIME_CONSTANTS.ONE_SECOND;

  // 配置备份保留数量
  private readonly MAX_BACKUP_COUNT = 5;

  // 配置验证超时时间
  private readonly VALIDATION_TIMEOUT = TIME_CONSTANTS.FIVE_SECONDS;

  constructor(
    @inject(TYPES.Shared.EventBus)
    private readonly eventBus: IEventBus,
    @inject(TYPES.UserConfig.UserConfigApplicationService)
    private readonly userConfigService: any, // 使用any避免直接依赖Application层类型
    @inject(TYPES.UserConfig.ModelSelectionService)
    private readonly modelSelectionService: any, // 使用any避免直接依赖Application层类型
    @inject(TYPES.Logger)
    private readonly logger: Logger
  ) {
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听用户配置变更事件
    this.eventBus.subscribe('UserConfigChangedEvent', this);
    
    // 监听缓存清理事件
    this.eventBus.subscribe('ConfigCacheClearEvent', new ConfigCacheClearHandler(this.logger));

    this.logger.info('✅ 配置热更新服务事件监听器已注册');
  }

  /**
   * 处理用户配置变更事件
   */
  async handle(event: UserConfigChangedEvent): Promise<void> {
    const lockKey = `${event.userId}:${event.configType}`;

    // 检查是否已有正在进行的重载操作
    if (this.reloadLocks.has(lockKey)) {
      this.logger.debug('配置重载已在进行中，等待完成', { userId: event.userId, configType: event.configType });
      await this.reloadLocks.get(lockKey);
      return;
    }

    // 创建重载锁
    const reloadPromise = this.performConfigReload(event);
    this.reloadLocks.set(lockKey, reloadPromise);

    try {
      await reloadPromise;
    } finally {
      // 清除重载锁
      this.reloadLocks.delete(lockKey);
    }
  }

  /**
   * 执行配置重载
   */
  private async performConfigReload(event: UserConfigChangedEvent): Promise<void> {
    let backupCreated = false;

    try {
      this.logger.info('开始处理用户配置变更事件', {
        userId: event.userId,
        configType: event.configType,
        providerName: event.providerName,
        scenario: event.scenario
      });

      // 检查冷却期
      if (this.isInCooldown(event.userId)) {
        this.logger.debug('配置更新在冷却期内，跳过处理', { userId: event.userId });
        return;
      }

      // 1. 创建配置备份
      await this.createConfigBackup(event.userId, event.configType);
      backupCreated = true;

      // 2. 验证新配置
      const validationResult = await this.validateConfig(event);
      if (!validationResult.isValid) {
        throw new Error(`配置验证失败: ${validationResult.errors.join(', ')}`);
      }

      // 记录警告
      if (validationResult.warnings.length > 0) {
        this.logger.warn('配置验证警告', {
          userId: event.userId,
          warnings: validationResult.warnings
        });
      }

      // 3. 更新最后处理时间
      this.lastUpdateTimes.set(event.userId, Date.now());

      // 4. 根据配置类型执行不同的热更新逻辑
      switch (event.configType) {
        case 'llmConfig':
          await this.handleLLMConfigChange(event);
          break;
        case 'modelPreference':
          await this.handleModelPreferenceChange(event);
          break;
        default:
          throw new Error(`未知的配置类型: ${event.configType}`);
      }

      // 5. 验证重载后的配置是否正常工作
      await this.validateReloadedConfig(event);

      this.logger.info('用户配置热更新完成', { userId: event.userId });

    } catch (error) {
      this.logger.error('处理用户配置变更失败', {
        userId: event.userId,
        error: error instanceof Error ? error.message : String(error)
      });

      // 如果创建了备份，尝试回滚
      if (backupCreated) {
        try {
          await this.rollbackConfig(event.userId, event.configType, error instanceof Error ? error.message : String(error));
        } catch (rollbackError) {
          this.logger.error('配置回滚失败', {
            userId: event.userId,
            rollbackError: rollbackError instanceof Error ? rollbackError.message : String(rollbackError)
          });
        }
      }

      throw error;
    }
  }

  /**
   * 处理LLM配置变更
   */
  private async handleLLMConfigChange(event: UserConfigChangedEvent): Promise<void> {
    const { userId, providerName } = event;

    // 1. 清除用户配置缓存
    await this.clearUserConfigCache(userId);

    // 2. 清除提供者实例缓存
    if (providerName) {
      await this.clearProviderInstanceCache(userId, providerName);
    }

    // 3. 重新初始化提供者
    await this.reinitializeProviders(userId);

    // 4. 清除模型选择缓存
    await this.clearModelSelectionCache(userId);

    // 5. 发布缓存清理事件
    const cacheKeys = [
      `${this.CACHE_PREFIX.USER_CONFIG}${userId}`,
      `${this.CACHE_PREFIX.PROVIDER_INSTANCE}${userId}:${providerName}`,
      `${this.CACHE_PREFIX.SELECTION_CACHE}${userId}:*`
    ];

    await this.eventBus.publish(new ConfigCacheClearEvent(userId, cacheKeys));
  }

  /**
   * 处理模型偏好变更
   */
  private async handleModelPreferenceChange(event: UserConfigChangedEvent): Promise<void> {
    const { userId, scenario } = event;

    // 1. 清除模型偏好缓存
    await this.clearModelPreferenceCache(userId, scenario);

    // 2. 清除模型选择缓存
    await this.clearModelSelectionCache(userId, scenario);

    // 3. 预热新的偏好配置
    await this.preheatModelPreferences(userId, scenario);

    // 4. 发布缓存清理事件
    const cacheKeys = [
      `${this.CACHE_PREFIX.MODEL_PREFERENCE}${userId}:${scenario}`,
      `${this.CACHE_PREFIX.SELECTION_CACHE}${userId}:${scenario}`
    ];

    await this.eventBus.publish(new ConfigCacheClearEvent(userId, cacheKeys));
  }

  /**
   * 清除用户配置缓存
   */
  private async clearUserConfigCache(userId: string): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX.USER_CONFIG}${userId}`;
      
      // 清除内存缓存
      this.configCache.delete(cacheKey);

      // 清除Redis缓存
      const redis = getRedis();
      if (redis) {
        await redis.del(cacheKey);
      }

      this.logger.debug('用户配置缓存已清除', { userId, cacheKey });
    } catch (error) {
      this.logger.warn('清除用户配置缓存失败', { userId, error });
    }
  }

  /**
   * 清除提供者实例缓存
   */
  private async clearProviderInstanceCache(userId: string, providerName: string): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX.PROVIDER_INSTANCE}${userId}:${providerName}`;
      
      // 清除内存缓存
      this.providerInstances.delete(cacheKey);

      // 清除Redis缓存
      const redis = getRedis();
      if (redis) {
        await redis.del(cacheKey);
      }

      this.logger.debug('提供者实例缓存已清除', { userId, providerName, cacheKey });
    } catch (error) {
      this.logger.warn('清除提供者实例缓存失败', { userId, providerName, error });
    }
  }

  /**
   * 清除模型偏好缓存
   */
  private async clearModelPreferenceCache(userId: string, scenario?: string): Promise<void> {
    try {
      const pattern = scenario 
        ? `${this.CACHE_PREFIX.MODEL_PREFERENCE}${userId}:${scenario}`
        : `${this.CACHE_PREFIX.MODEL_PREFERENCE}${userId}:*`;

      // 清除内存缓存
      for (const key of this.configCache.keys()) {
        if (key.startsWith(`${this.CACHE_PREFIX.MODEL_PREFERENCE}${userId}`)) {
          if (!scenario || key.includes(scenario)) {
            this.configCache.delete(key);
          }
        }
      }

      // 清除Redis缓存
      const redis = getRedis();
      if (redis) {
        if (scenario) {
          await redis.del(pattern);
        } else {
          // 使用SCAN命令查找并删除匹配的键
          const keys = await redis.keys(pattern);
          if (keys.length > 0) {
            await redis.del(keys);
          }
        }
      }

      this.logger.debug('模型偏好缓存已清除', { userId, scenario, pattern });
    } catch (error) {
      this.logger.warn('清除模型偏好缓存失败', { userId, scenario, error });
    }
  }

  /**
   * 清除模型选择缓存
   */
  private async clearModelSelectionCache(userId: string, scenario?: string): Promise<void> {
    try {
      const pattern = scenario 
        ? `${this.CACHE_PREFIX.SELECTION_CACHE}${userId}:${scenario}`
        : `${this.CACHE_PREFIX.SELECTION_CACHE}${userId}:*`;

      // 清除Redis缓存
      const redis = getRedis();
      if (redis) {
        if (scenario) {
          await redis.del(pattern);
        } else {
          const keys = await redis.keys(pattern);
          if (keys.length > 0) {
            await redis.del(keys);
          }
        }
      }

      this.logger.debug('模型选择缓存已清除', { userId, scenario, pattern });
    } catch (error) {
      this.logger.warn('清除模型选择缓存失败', { userId, scenario, error });
    }
  }

  /**
   * 重新初始化提供者
   */
  private async reinitializeProviders(userId: string): Promise<void> {
    try {
      // 获取用户的最新配置
      const userConfigs = await this.userConfigService.getUserConfigs(userId);
      
      // 为每个配置的提供者重新初始化实例
      for (const config of userConfigs) {
        if (config.isActive) {
          // 这里可以预创建提供者实例，但不缓存
          // 实际的提供者实例会在下次使用时动态创建
          this.logger.debug('标记提供者需要重新初始化', {
            userId,
            provider: config.providerName
          });
        }
      }

      this.logger.debug('提供者重新初始化完成', { userId });
    } catch (error) {
      this.logger.warn('重新初始化提供者失败', { userId, error });
    }
  }

  /**
   * 预热模型偏好配置
   */
  private async preheatModelPreferences(userId: string, scenario?: string): Promise<void> {
    try {
      if (scenario) {
        // 预热特定场景的模型选择
        await this.modelSelectionService.getRecommendedModelsForScenario(scenario, userId);
      } else {
        // 预热所有场景的模型选择
        const scenarios = this.modelSelectionService.getAvailableScenarios();
        for (const scenarioDefinition of scenarios) {
          await this.modelSelectionService.getRecommendedModelsForScenario(
            scenarioDefinition.id, 
            userId
          );
        }
      }

      this.logger.debug('模型偏好预热完成', { userId, scenario });
    } catch (error) {
      this.logger.warn('预热模型偏好失败', { userId, scenario, error });
    }
  }

  /**
   * 检查是否在冷却期内
   */
  private isInCooldown(userId: string): boolean {
    const lastUpdate = this.lastUpdateTimes.get(userId);
    if (!lastUpdate) {
      return false;
    }

    return (Date.now() - lastUpdate) < this.UPDATE_COOLDOWN;
  }

  /**
   * 发布配置变更事件
   */
  async publishConfigChangeEvent(
    userId: string,
    configType: 'llmConfig' | 'modelPreference',
    providerName?: string,
    scenario?: string,
    changeDetails?: Record<string, any>
  ): Promise<void> {
    const event = new UserConfigChangedEvent(
      userId,
      configType,
      providerName,
      scenario,
      changeDetails
    );

    await this.eventBus.publish(event);
    this.logger.debug('配置变更事件已发布', { userId, configType });
  }

  /**
   * 创建配置备份
   */
  private async createConfigBackup(userId: string, configType: string): Promise<void> {
    try {
      let currentConfig: any;

      // 获取当前配置
      switch (configType) {
        case 'llmConfig':
          currentConfig = await this.userConfigService.getUserConfigs(userId);
          break;
        case 'modelPreference':
          // 这里需要获取模型偏好配置，暂时使用空对象
          currentConfig = {};
          break;
        default:
          throw new Error(`不支持的配置类型: ${configType}`);
      }

      // 创建备份
      const backup: ConfigBackup = {
        userId,
        configType,
        config: JSON.parse(JSON.stringify(currentConfig)), // 深拷贝
        timestamp: new Date(),
        version: `${Date.now()}`
      };

      // 存储备份
      const userBackups = this.configBackups.get(userId) || [];
      userBackups.unshift(backup); // 添加到开头

      // 保持最大备份数量
      if (userBackups.length > this.MAX_BACKUP_COUNT) {
        userBackups.splice(this.MAX_BACKUP_COUNT);
      }

      this.configBackups.set(userId, userBackups);

      this.logger.debug('配置备份已创建', {
        userId,
        configType,
        version: backup.version,
        backupCount: userBackups.length
      });

    } catch (error) {
      this.logger.error('创建配置备份失败', {
        userId,
        configType,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证配置
   */
  private async validateConfig(event: UserConfigChangedEvent): Promise<ConfigValidationResult> {
    const result: ConfigValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 设置验证超时
      const validationPromise = this.performConfigValidation(event);
      const timeoutPromise = new Promise<ConfigValidationResult>((_, reject) => {
        setTimeout(() => reject(new Error('配置验证超时')), this.VALIDATION_TIMEOUT);
      });

      return await Promise.race([validationPromise, timeoutPromise]);

    } catch (error) {
      result.isValid = false;
      result.errors.push(error instanceof Error ? error.message : String(error));
      return result;
    }
  }

  /**
   * 执行配置验证
   */
  private async performConfigValidation(event: UserConfigChangedEvent): Promise<ConfigValidationResult> {
    const result: ConfigValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    const { userId, configType, providerName, changeDetails } = event;

    switch (configType) {
      case 'llmConfig':
        if (!providerName) {
          result.errors.push('LLM配置变更必须指定提供者名称');
          result.isValid = false;
        }

        // 验证提供者配置
        if (changeDetails) {
          if (changeDetails.apiKey && typeof changeDetails.apiKey !== 'string') {
            result.errors.push('API密钥必须是字符串类型');
            result.isValid = false;
          }

          if (changeDetails.baseUrl && !this.isValidUrl(changeDetails.baseUrl)) {
            result.errors.push('基础URL格式无效');
            result.isValid = false;
          }

          if (changeDetails.maxTokens && (typeof changeDetails.maxTokens !== 'number' || changeDetails.maxTokens <= 0)) {
            result.errors.push('最大令牌数必须是正整数');
            result.isValid = false;
          }
        }
        break;

      case 'modelPreference':
        if (changeDetails) {
          if (changeDetails.preferredModels && !Array.isArray(changeDetails.preferredModels)) {
            result.errors.push('首选模型必须是数组类型');
            result.isValid = false;
          }

          if (changeDetails.priority && (typeof changeDetails.priority !== 'number' || changeDetails.priority < 0)) {
            result.errors.push('优先级必须是非负数');
            result.isValid = false;
          }
        }
        break;

      default:
        result.errors.push(`未知的配置类型: ${configType}`);
        result.isValid = false;
    }

    // 添加通用警告
    if (changeDetails && Object.keys(changeDetails).length === 0) {
      result.warnings.push('配置变更详情为空，可能不会产生实际效果');
    }

    return result;
  }

  /**
   * 验证重载后的配置
   */
  private async validateReloadedConfig(event: UserConfigChangedEvent): Promise<void> {
    try {
      const { userId, configType, providerName } = event;

      switch (configType) {
        case 'llmConfig':
          if (providerName) {
            // 尝试获取用户配置以验证重载是否成功
            const configs = await this.userConfigService.getUserConfigs(userId);
            const providerConfig = configs.find(c => c.providerName === providerName);

            if (!providerConfig) {
              throw new Error(`重载后未找到提供者配置: ${providerName}`);
            }

            if (!providerConfig.isActive) {
              this.logger.warn('重载后的提供者配置未激活', { userId, providerName });
            }
          }
          break;

        case 'modelPreference':
          // 验证模型偏好是否正确重载
          // 这里可以添加具体的验证逻辑
          break;
      }

      this.logger.debug('重载后配置验证通过', { userId, configType });

    } catch (error) {
      this.logger.error('重载后配置验证失败', {
        userId: event.userId,
        configType: event.configType,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 回滚配置
   */
  private async rollbackConfig(userId: string, configType: string, reason: string): Promise<void> {
    try {
      const userBackups = this.configBackups.get(userId);
      if (!userBackups || userBackups.length === 0) {
        throw new Error('没有可用的配置备份');
      }

      // 找到最新的相同类型的备份
      const latestBackup = userBackups.find(backup => backup.configType === configType);
      if (!latestBackup) {
        throw new Error(`没有找到类型为 ${configType} 的配置备份`);
      }

      this.logger.info('开始回滚配置', {
        userId,
        configType,
        backupVersion: latestBackup.version,
        reason
      });

      // 执行回滚操作
      switch (configType) {
        case 'llmConfig':
          // 这里需要实现具体的LLM配置回滚逻辑
          // 由于涉及数据库操作，这里只记录日志
          this.logger.warn('LLM配置回滚需要手动处理', {
            userId,
            backupConfig: latestBackup.config
          });
          break;

        case 'modelPreference':
          // 这里需要实现具体的模型偏好回滚逻辑
          this.logger.warn('模型偏好回滚需要手动处理', {
            userId,
            backupConfig: latestBackup.config
          });
          break;
      }

      // 发布回滚事件
      const rollbackEvent = new ConfigRollbackEvent(
        userId,
        configType,
        reason,
        latestBackup.config
      );
      await this.eventBus.publish(rollbackEvent);

      this.logger.info('配置回滚完成', { userId, configType });

    } catch (error) {
      this.logger.error('配置回滚失败', {
        userId,
        configType,
        reason,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): Record<string, any> {
    return {
      configCacheSize: this.configCache.size,
      providerInstancesSize: this.providerInstances.size,
      lastUpdateTimesSize: this.lastUpdateTimes.size,
      updateCooldown: this.UPDATE_COOLDOWN,
      totalBackups: Array.from(this.configBackups.values()).reduce((sum, backups) => sum + backups.length, 0),
      activeReloadLocks: this.reloadLocks.size
    };
  }
}

/**
 * 缓存清理事件处理器
 */
class ConfigCacheClearHandler implements IDomainEventHandler<ConfigCacheClearEvent> {
  constructor(private readonly logger: Logger) {}

  async handle(event: ConfigCacheClearEvent): Promise<void> {
    this.logger.info('处理缓存清理事件', {
      userId: event.userId,
      cacheKeys: event.cacheKeys
    });

    // 这里可以添加额外的缓存清理逻辑
    // 比如通知其他服务实例清理缓存等
  }
}
