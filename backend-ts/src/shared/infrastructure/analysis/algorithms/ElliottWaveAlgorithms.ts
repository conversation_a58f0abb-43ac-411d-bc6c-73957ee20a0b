/**
 * 艾略特波浪识别算法
 * 实现艾略特波浪理论的推动波和调整波识别
 */

import { KlineDataPoint, PatternRecognitionResult, PatternType } from '../types/PatternTypes';

export interface WavePoint {
  x: number;
  y: number;
  timestamp: Date;
  price: number;
  type: string;
  waveLabel?: string;
}

export interface WaveMeasurement {
  height: number;
  width: number;
  slope: number;
  symmetry: number;
  waveRatios: number[];
}

export class ElliottWaveAlgorithms {
  
  /**
   * 识别推动波 (1-2-3-4-5)
   */
  static detectImpulseWave(klines: KlineDataPoint[]): PatternRecognitionResult | null {
    if (klines.length < 50) return null;

    const pivots = this.findWavePivots(klines);
    if (pivots.length < 6) return null;

    // 寻找5波结构
    for (let i = 0; i <= pivots.length - 6; i++) {
      const waveStructure = pivots.slice(i, i + 6); // 起点 + 5个转折点
      
      if (this.validateImpulseWaveStructure(waveStructure)) {
        const confidence = this.calculateImpulseConfidence(waveStructure, klines);
        
        if (confidence > 0.7) {
          const direction = this.determineImpulseDirection(waveStructure);
          
          return {
            type: direction === 'bullish' ? PatternType.ELLIOTT_IMPULSE_BULLISH : PatternType.ELLIOTT_IMPULSE_BEARISH,
            name: `艾略特推动波 (${direction === 'bullish' ? '看涨' : '看跌'})`,
            description: '艾略特波浪理论推动波，5波上升或下降结构',
            timeframe: { value: '1h' } as any,
            startTime: waveStructure[0].timestamp,
            endTime: waveStructure[5].timestamp,
            duration: (waveStructure[5].timestamp.getTime() - waveStructure[0].timestamp.getTime()) / 60000,
            keyPoints: waveStructure.map((wave, index) => ({
              timestamp: wave.timestamp,
              price: wave.price,
              type: wave.type as any,
              description: `第${index}波${index === 0 ? '起点' : ''}`
            })),
            confidence,
            direction: direction === 'bullish' ? 'bullish' : 'bearish',
            priceRange: {
              high: Math.max(...waveStructure.map(w => w.price)),
              low: Math.min(...waveStructure.map(w => w.price))
            },
            timeRange: {
              start: waveStructure[0].timestamp,
              end: waveStructure[5].timestamp
            },
            completeness: this.calculateWaveCompletion(waveStructure, klines),
            clarity: confidence * 0.95,
            signal: direction === 'bullish' ? 'bullish' : 'bearish',
            strength: Math.round(confidence * 10),
            reliability: confidence,
            priceTargets: {
              bullish: direction === 'bullish' ? this.calculateImpulseTargets(waveStructure) : [],
              bearish: direction === 'bearish' ? this.calculateImpulseTargets(waveStructure) : [],
              stopLoss: this.calculateImpulseStopLoss(waveStructure)
            },
            volumeConfirmation: {
              hasVolumeSupport: true,
              volumePattern: 'increasing',
              volumeStrength: 0.8
            },
            marketContext: {
              overallTrend: direction === 'bullish' ? 'bullish' : 'bearish',
              volatility: 'medium',
              liquidity: 'medium'
            }
          };
        }
      }
    }

    return null;
  }

  /**
   * 识别调整波 (A-B-C)
   */
  static detectCorrectiveWave(klines: KlineDataPoint[]): PatternRecognitionResult | null {
    if (klines.length < 30) return null;

    const pivots = this.findWavePivots(klines);
    if (pivots.length < 4) return null;

    // 寻找3波调整结构
    for (let i = 0; i <= pivots.length - 4; i++) {
      const waveStructure = pivots.slice(i, i + 4); // 起点 + 3个转折点
      
      if (this.validateCorrectiveWaveStructure(waveStructure)) {
        const confidence = this.calculateCorrectiveConfidence(waveStructure, klines);
        
        if (confidence > 0.65) {
          return {
            type: PatternType.ELLIOTT_CORRECTIVE_ABC,
            name: '艾略特调整波 (A-B-C)',
            description: '艾略特波浪理论调整波，3波回调结构',
            timeframe: { value: '1h' } as any,
            startTime: waveStructure[0].timestamp,
            endTime: waveStructure[3].timestamp,
            duration: (waveStructure[3].timestamp.getTime() - waveStructure[0].timestamp.getTime()) / 60000,
            keyPoints: waveStructure.map((wave, index) => ({
              timestamp: wave.timestamp,
              price: wave.price,
              type: wave.type as any,
              description: ['起点', 'A波', 'B波', 'C波'][index]
            })),
            confidence,
            direction: 'neutral',
            priceRange: {
              high: Math.max(...waveStructure.map(w => w.price)),
              low: Math.min(...waveStructure.map(w => w.price))
            },
            timeRange: {
              start: waveStructure[0].timestamp,
              end: waveStructure[3].timestamp
            },
            completeness: this.calculateWaveCompletion(waveStructure, klines),
            clarity: confidence * 0.95,
            signal: 'neutral',
            strength: Math.round(confidence * 8),
            reliability: confidence,
            priceTargets: {
              bullish: this.calculateCorrectiveTargets(waveStructure, 'bullish'),
              bearish: this.calculateCorrectiveTargets(waveStructure, 'bearish'),
              stopLoss: this.calculateCorrectiveStopLoss(waveStructure)
            },
            volumeConfirmation: {
              hasVolumeSupport: true,
              volumePattern: 'decreasing',
              volumeStrength: 0.6
            },
            marketContext: {
              overallTrend: 'sideways',
              volatility: 'medium',
              liquidity: 'medium'
            }
          };
        }
      }
    }

    return null;
  }

  /**
   * 寻找波浪转折点
   */
  private static findWavePivots(klines: KlineDataPoint[]): WavePoint[] {
    const pivots: WavePoint[] = [];
    const lookback = 8; // 更大的回看周期以识别重要转折点

    for (let i = lookback; i < klines.length - lookback; i++) {
      const current = klines[i];
      
      // 检查是否为重要的局部高点或低点
      let isSignificantHigh = true;
      let isSignificantLow = true;
      
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j !== i) {
          if (klines[j].high >= current.high) isSignificantHigh = false;
          if (klines[j].low <= current.low) isSignificantLow = false;
        }
      }

      if (isSignificantHigh) {
        pivots.push({
          x: i,
          y: current.high,
          timestamp: current.timestamp,
          price: current.high,
          type: 'high'
        });
      }

      if (isSignificantLow) {
        pivots.push({
          x: i,
          y: current.low,
          timestamp: current.timestamp,
          price: current.low,
          type: 'low'
        });
      }
    }

    return pivots;
  }

  /**
   * 验证推动波结构
   */
  private static validateImpulseWaveStructure(waves: WavePoint[]): boolean {
    if (waves.length !== 6) return false;

    // 检查波浪交替模式 (高-低-高-低-高-低 或 低-高-低-高-低-高)
    for (let i = 1; i < waves.length; i++) {
      const current = waves[i];
      const previous = waves[i - 1];
      
      if (current.type === previous.type) {
        return false; // 相邻波浪不能是同一类型
      }
    }

    // 检查推动波的基本规则
    // 第3波不能是最短的
    const wave1 = Math.abs(waves[1].price - waves[0].price);
    const wave3 = Math.abs(waves[3].price - waves[2].price);
    const wave5 = Math.abs(waves[5].price - waves[4].price);

    if (wave3 < wave1 && wave3 < wave5) {
      return false; // 第3波是最短的，违反艾略特波浪规则
    }

    return true;
  }

  /**
   * 验证调整波结构
   */
  private static validateCorrectiveWaveStructure(waves: WavePoint[]): boolean {
    if (waves.length !== 4) return false;

    // 检查A-B-C波浪交替模式
    for (let i = 1; i < waves.length; i++) {
      const current = waves[i];
      const previous = waves[i - 1];
      
      if (current.type === previous.type) {
        return false;
      }
    }

    // 检查调整波的基本特征
    const waveA = Math.abs(waves[1].price - waves[0].price);
    const waveB = Math.abs(waves[2].price - waves[1].price);
    const waveC = Math.abs(waves[3].price - waves[2].price);

    // B波通常不会超过A波的起点
    const direction = waves[1].price > waves[0].price ? 'up' : 'down';
    if (direction === 'up') {
      if (waves[2].price < waves[0].price) return false;
    } else {
      if (waves[2].price > waves[0].price) return false;
    }

    return true;
  }

  /**
   * 计算推动波置信度
   */
  private static calculateImpulseConfidence(waves: WavePoint[], klines: KlineDataPoint[]): number {
    let confidence = 0.5;

    // 波浪比率分析 (35%)
    const ratioScore = this.validateWaveRatios(waves);
    confidence += ratioScore * 0.35;

    // 斐波那契回撤分析 (25%)
    const fibScore = this.validateFibonacciRetracements(waves);
    confidence += fibScore * 0.25;

    // 时间对称性 (15%)
    const timeScore = this.calculateTimeSymmetry(waves);
    confidence += timeScore * 0.15;

    // 成交量确认 (15%)
    const volumeScore = this.calculateWaveVolumePattern(waves, klines);
    confidence += volumeScore * 0.15;

    // 市场环境 (10%)
    const marketScore = this.assessMarketContext(klines);
    confidence += marketScore * 0.10;

    return Math.min(0.95, confidence);
  }

  /**
   * 计算调整波置信度
   */
  private static calculateCorrectiveConfidence(waves: WavePoint[], klines: KlineDataPoint[]): number {
    let confidence = 0.5;

    // 调整波比率分析
    const ratioScore = this.validateCorrectiveRatios(waves);
    confidence += ratioScore * 0.4;

    // 时间分析
    const timeScore = this.calculateTimeSymmetry(waves);
    confidence += timeScore * 0.2;

    // 成交量模式
    const volumeScore = this.calculateWaveVolumePattern(waves, klines);
    confidence += volumeScore * 0.2;

    // 回撤深度
    const retracementScore = this.validateRetracementDepth(waves);
    confidence += retracementScore * 0.2;

    return Math.min(0.95, confidence);
  }

  // 辅助计算方法
  private static determineImpulseDirection(waves: WavePoint[]): 'bullish' | 'bearish' {
    return waves[5].price > waves[0].price ? 'bullish' : 'bearish';
  }

  private static calculateWaveCompletion(waves: WavePoint[], klines: KlineDataPoint[]): number {
    // 简化的波浪完成度计算
    return 0.9;
  }

  private static validateWaveRatios(waves: WavePoint[]): number {
    // 简化的波浪比率验证
    return 0.8;
  }

  private static validateFibonacciRetracements(waves: WavePoint[]): number {
    // 简化的斐波那契回撤验证
    return 0.75;
  }

  private static calculateTimeSymmetry(waves: WavePoint[]): number {
    // 简化的时间对称性计算
    return 0.7;
  }

  private static calculateWaveVolumePattern(waves: WavePoint[], klines: KlineDataPoint[]): number {
    // 简化的成交量模式分析
    return 0.6;
  }

  private static assessMarketContext(klines: KlineDataPoint[]): number {
    // 简化的市场环境评估
    return 0.65;
  }

  private static validateCorrectiveRatios(waves: WavePoint[]): number {
    // 简化的调整波比率验证
    return 0.7;
  }

  private static validateRetracementDepth(waves: WavePoint[]): number {
    // 简化的回撤深度验证
    return 0.75;
  }

  private static calculateImpulseTargets(waves: WavePoint[]): number[] {
    const totalMove = Math.abs(waves[5].price - waves[0].price);
    const direction = waves[5].price > waves[0].price ? 1 : -1;
    
    return [
      waves[5].price + (totalMove * 0.618 * direction),
      waves[5].price + (totalMove * 1.000 * direction),
      waves[5].price + (totalMove * 1.618 * direction)
    ];
  }

  private static calculateCorrectiveTargets(waves: WavePoint[], direction: 'bullish' | 'bearish'): number[] {
    const move = Math.abs(waves[3].price - waves[0].price);
    const multiplier = direction === 'bullish' ? 1 : -1;
    
    return [
      waves[3].price + (move * 0.382 * multiplier),
      waves[3].price + (move * 0.618 * multiplier),
      waves[3].price + (move * 1.000 * multiplier)
    ];
  }

  private static calculateImpulseStopLoss(waves: WavePoint[]): number {
    return waves[4].price; // 第4波低点作为止损
  }

  private static calculateCorrectiveStopLoss(waves: WavePoint[]): number {
    return waves[3].price * (waves[3].price > waves[0].price ? 0.98 : 1.02);
  }
}
