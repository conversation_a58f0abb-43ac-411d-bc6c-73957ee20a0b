/**
 * 江恩理论形态识别算法
 * 实现江恩角度线、江恩扇形线、江恩时间周期等识别算法
 */

import { KlineDataPoint, PatternRecognitionResult, PatternType } from '../types/PatternTypes';

export interface GannPoint {
  x: number;
  y: number;
  timestamp: Date;
  price: number;
  type: string;
}

export interface GannAngle {
  name: string;
  ratio: number;
  degrees: number;
  significance: number;
}

export class GannPatternAlgorithms {
  
  /**
   * 识别江恩1x1角度线
   */
  static detectGann1x1Angle(klines: KlineDataPoint[]): PatternRecognitionResult | null {
    if (klines.length < 30) return null;

    const significantPoints = this.findSignificantPoints(klines);
    if (significantPoints.length < 2) return null;

    // 江恩1x1角度线（45度）
    const gann1x1Angle: GannAngle = {
      name: '1x1',
      ratio: 1,
      degrees: 45,
      significance: 1.0
    };

    for (let i = 0; i < significantPoints.length - 1; i++) {
      const startPoint = significantPoints[i];
      const confidence = this.calculateGannAngleConfidence(startPoint, gann1x1Angle, klines);
      
      if (confidence > 0.7) {
        return {
          type: PatternType.GANN_ANGLE_1X1,
          name: '江恩1x1角度线',
          description: '江恩1x1角度线，价格与时间的平衡点',
          timeframe: { value: '1h' } as any,
          startTime: startPoint.timestamp,
          endTime: klines[klines.length - 1].timestamp,
          duration: (klines[klines.length - 1].timestamp.getTime() - startPoint.timestamp.getTime()) / 60000,
          keyPoints: [
            {
              timestamp: startPoint.timestamp,
              price: startPoint.price,
              type: 'support',
              description: '江恩角度线起点'
            }
          ],
          confidence,
          completeness: confidence * 0.9,
          clarity: confidence * 0.95,
          signal: startPoint.type === 'low' ? 'bullish' : 'bearish',
          direction: startPoint.type === 'low' ? 'bullish' : 'bearish',
          strength: Math.round(confidence * 10),
          reliability: confidence,
          priceRange: {
            high: Math.max(startPoint.price, klines[klines.length - 1].close),
            low: Math.min(startPoint.price, klines[klines.length - 1].close)
          },
          timeRange: {
            start: startPoint.timestamp,
            end: klines[klines.length - 1].timestamp
          },
          priceTargets: {
            bullish: this.calculateGannTargets(startPoint, gann1x1Angle, 'bullish'),
            bearish: this.calculateGannTargets(startPoint, gann1x1Angle, 'bearish'),
            stopLoss: startPoint.price * (startPoint.type === 'low' ? 0.98 : 1.02)
          },
          volumeConfirmation: {
            hasVolumeSupport: true,
            volumePattern: 'stable',
            volumeStrength: 0.7
          },
          marketContext: {
            overallTrend: 'sideways',
            volatility: 'medium',
            liquidity: 'medium'
          }
        };
      }
    }

    return null;
  }

  /**
   * 识别江恩扇形线
   */
  static detectGannFan(klines: KlineDataPoint[]): PatternRecognitionResult | null {
    if (klines.length < 50) return null;

    const significantPoints = this.findSignificantPoints(klines);
    if (significantPoints.length < 1) return null;

    const gannAngles: GannAngle[] = [
      { name: '1x8', ratio: 1/8, degrees: 7.125, significance: 0.5 },
      { name: '1x4', ratio: 1/4, degrees: 14.25, significance: 0.6 },
      { name: '1x3', ratio: 1/3, degrees: 18.43, significance: 0.7 },
      { name: '1x2', ratio: 1/2, degrees: 26.57, significance: 0.8 },
      { name: '1x1', ratio: 1, degrees: 45, significance: 1.0 },
      { name: '2x1', ratio: 2, degrees: 63.43, significance: 0.8 },
      { name: '3x1', ratio: 3, degrees: 71.57, significance: 0.7 },
      { name: '4x1', ratio: 4, degrees: 75.96, significance: 0.6 },
      { name: '8x1', ratio: 8, degrees: 82.875, significance: 0.5 }
    ];

    for (const point of significantPoints) {
      let effectiveAngles = 0;
      let totalConfidence = 0;

      for (const angle of gannAngles) {
        const confidence = this.calculateGannAngleConfidence(point, angle, klines);
        if (confidence > 0.6) {
          effectiveAngles++;
          totalConfidence += confidence * angle.significance;
        }
      }

      const fanEffectiveness = effectiveAngles / gannAngles.length;
      const avgConfidence = totalConfidence / effectiveAngles;

      if (fanEffectiveness > 0.6 && avgConfidence > 0.7) {
        return {
          type: PatternType.GANN_FAN,
          name: '江恩扇形线',
          description: '江恩扇形线，多条角度线形成的支撑阻力扇形',
          timeframe: { value: '1h' } as any,
          startTime: point.timestamp,
          endTime: klines[klines.length - 1].timestamp,
          duration: (klines[klines.length - 1].timestamp.getTime() - point.timestamp.getTime()) / 60000,
          keyPoints: [
            {
              timestamp: point.timestamp,
              price: point.price,
              type: point.type === 'low' ? 'support' : 'resistance',
              description: '江恩扇形线中心点'
            }
          ],
          confidence: avgConfidence,
          completeness: fanEffectiveness,
          clarity: avgConfidence * 0.95,
          signal: point.type === 'low' ? 'bullish' : 'bearish',
          strength: Math.round(avgConfidence * 10),
          reliability: avgConfidence,
          priceTargets: {
            bullish: this.calculateFanTargets(point, gannAngles, 'bullish'),
            bearish: this.calculateFanTargets(point, gannAngles, 'bearish'),
            stopLoss: point.price * (point.type === 'low' ? 0.97 : 1.03)
          },
          volumeConfirmation: {
            hasVolumeSupport: true,
            volumePattern: 'stable',
            volumeStrength: 0.8
          },
          marketContext: {
            overallTrend: point.type === 'low' ? 'bullish' : 'bearish',
            volatility: 'medium',
            liquidity: 'medium'
          }
        };
      }
    }

    return null;
  }

  /**
   * 寻找重要的转折点
   */
  private static findSignificantPoints(klines: KlineDataPoint[]): GannPoint[] {
    const points: GannPoint[] = [];
    const lookback = 5;

    for (let i = lookback; i < klines.length - lookback; i++) {
      const current = klines[i];
      
      // 检查是否为局部高点
      let isHigh = true;
      let isLow = true;
      
      for (let j = i - lookback; j <= i + lookback; j++) {
        if (j !== i) {
          if (klines[j].high >= current.high) isHigh = false;
          if (klines[j].low <= current.low) isLow = false;
        }
      }

      if (isHigh) {
        points.push({
          x: i,
          y: current.high,
          timestamp: current.timestamp,
          price: current.high,
          type: 'high'
        });
      }

      if (isLow) {
        points.push({
          x: i,
          y: current.low,
          timestamp: current.timestamp,
          price: current.low,
          type: 'low'
        });
      }
    }

    return points;
  }

  /**
   * 计算江恩角度线置信度
   */
  private static calculateGannAngleConfidence(
    point: GannPoint, 
    angle: GannAngle, 
    klines: KlineDataPoint[]
  ): number {
    const testPeriod = Math.min(20, klines.length - point.x - 1);
    if (testPeriod <= 0) return 0.5;

    let touchCount = 0;
    const startIndex = point.x + 1;

    for (let i = 0; i < testPeriod; i++) {
      const currentIndex = startIndex + i;
      if (currentIndex >= klines.length) break;

      const expectedPrice = this.calculateGannAnglePrice(point, angle.ratio, i + 1);
      const candle = klines[currentIndex];
      const tolerance = expectedPrice * 0.02; // 2%容差

      // 检查是否触及角度线
      if (candle.low <= expectedPrice + tolerance && candle.high >= expectedPrice - tolerance) {
        touchCount++;
      }
    }

    const touchRate = touchCount / testPeriod;
    return Math.min(0.95, touchRate * angle.significance);
  }

  /**
   * 计算江恩角度线价格
   */
  private static calculateGannAnglePrice(point: GannPoint, ratio: number, periods: number): number {
    // 简化的江恩角度线价格计算
    const priceChange = periods * ratio * (point.price * 0.01); // 假设每个周期1%的价格变化
    return point.type === 'low' ? point.price + priceChange : point.price - priceChange;
  }

  /**
   * 计算江恩目标价位
   */
  private static calculateGannTargets(point: GannPoint, angle: GannAngle, direction: 'bullish' | 'bearish'): number[] {
    const baseMove = point.price * 0.1; // 10%基础移动
    const multiplier = direction === 'bullish' ? 1 : -1;
    
    return [
      point.price + (baseMove * angle.ratio * multiplier),
      point.price + (baseMove * angle.ratio * 1.618 * multiplier),
      point.price + (baseMove * angle.ratio * 2.618 * multiplier)
    ];
  }

  /**
   * 计算扇形线目标价位
   */
  private static calculateFanTargets(point: GannPoint, angles: GannAngle[], direction: 'bullish' | 'bearish'): number[] {
    const targets: number[] = [];
    const multiplier = direction === 'bullish' ? 1 : -1;
    
    for (const angle of angles.slice(0, 3)) { // 取前3个角度
      const baseMove = point.price * 0.08; // 8%基础移动
      targets.push(point.price + (baseMove * angle.ratio * multiplier));
    }
    
    return targets;
  }
}
