import { Server as SocketIOServer } from 'socket.io';
import { injectable, inject } from 'inversify';
import { Logger, getLogger } from '../logging/logger-factory';
import { TYPES } from '../di/types/index';
import { UnifiedEnvironmentManager } from '../config/environment/unified-environment-manager';
import { getEnvironmentManager } from '../config/environment/environment-extensions';

/**
 * WebSocket服务器管理器
 * 重构后集成统一的WebSocket基础设施
 */
@injectable()
export class WebSocketServer {
  private io: SocketIOServer | null = null;
  private readonly logger: Logger;
  private readonly env = getEnvironmentManager();
  private readonly connectedClients = new Map<string, any>();
  private readonly subscriptions = new Map<string, Set<string>>();

  constructor() {
    this.logger = getLogger('WebSocketServer');
  }

  /**
   * 初始化WebSocket服务器
   */
  initialize(server: any): void {
    // 支持Express HTTP服务器
    const httpServer = server.server ?? server;

    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: this.env.get('WS_CORS_ORIGINS')?.split(',') || ['http://localhost:3000'],
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 30000,
    });

    this.setupEventHandlers();
    this.logger.info('WebSocket服务器初始化完成');
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      const clientId = socket.id;
      this.logger.info('客户端连接', { clientId, remoteAddress: socket.handshake.address });

      // 存储客户端信息
      this.connectedClients.set(clientId, {
        socket,
        connectedAt: new Date(),
        subscriptions: new Set<string>(),
      });

      // 发送连接确认
      socket.emit('connected', {
        clientId,
        timestamp: new Date().toISOString(),
        message: '连接成功',
      });

      // 处理订阅请求
      socket.on('subscribe', (data) => {
        this.handleSubscription(clientId, data);
      });

      // 处理取消订阅请求
      socket.on('unsubscribe', (data) => {
        this.handleUnsubscription(clientId, data);
      });

      // 处理ping请求
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date().toISOString() });
      });

      // 处理断开连接
      socket.on('disconnect', (reason) => {
        this.handleDisconnection(clientId, reason);
      });

      // 处理错误
      socket.on('error', (error) => {
        this.logger.error('WebSocket错误', { clientId, error: error instanceof Error ? error.message : String(error) });
      });
    });
  }

  /**
   * 处理订阅请求
   */
  private handleSubscription(clientId: string, data: any): void {
    try {
      const { type, symbols } = data;
      const client = this.connectedClients.get(clientId);
      
      if (!client) {
        this.logger.warn('客户端不存在', { clientId });
        return;
      }

      switch (type) {
        case 'price':
          this.subscribeToPrice(clientId, symbols);
          break;
        case 'kline':
          this.subscribeToKline(clientId, data);
          break;
        case 'marketOverview':
          this.subscribeToMarketOverview(clientId);
          break;
        default:
          this.logger.warn('未知的订阅类型', { clientId, type });
          client.socket.emit('error', {
            type: 'subscriptionError',
            message: `未知的订阅类型: ${type}`,
          });
      }
    } catch (error) {
      this.logger.error('处理订阅请求失败', { 
        clientId, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
    }
  }

  /**
   * 订阅价格数据
   */
  private subscribeToPrice(clientId: string, symbols: string[]): void {
    const client = this.connectedClients.get(clientId);
    if (!client) return;

    symbols.forEach(symbol => {
      const channel = `price:${symbol}`;
      client.subscriptions.add(channel);
      
      // 添加到全局订阅映射
      if (!this.subscriptions.has(channel)) {
        this.subscriptions.set(channel, new Set());
      }
      this.subscriptions.get(channel)!.add(clientId);
    });

    client.socket.emit('subscriptionConfirmed', {
      type: 'price',
      symbols,
      timestamp: new Date().toISOString(),
    });

    this.logger.debug('价格订阅成功', { clientId, symbols });
  }

  /**
   * 订阅K线数据
   */
  private subscribeToKline(clientId: string, data: any): void {
    const client = this.connectedClients.get(clientId);
    if (!client) return;

    const { symbols, intervals } = data;
    
    symbols.forEach((symbol: string) => {
      intervals.forEach((interval: string) => {
        const channel = `kline:${symbol}:${interval}`;
        client.subscriptions.add(channel);
        
        // 添加到全局订阅映射
        if (!this.subscriptions.has(channel)) {
          this.subscriptions.set(channel, new Set());
        }
        this.subscriptions.get(channel)!.add(clientId);
      });
    });

    client.socket.emit('subscriptionConfirmed', {
      type: 'kline',
      symbols,
      intervals,
      timestamp: new Date().toISOString(),
    });

    this.logger.debug('K线订阅成功', { clientId, symbols, intervals });
  }

  /**
   * 订阅市场概览
   */
  private subscribeToMarketOverview(clientId: string): void {
    const client = this.connectedClients.get(clientId);
    if (!client) return;

    const channel = 'marketOverview';
    client.subscriptions.add(channel);
    
    // 添加到全局订阅映射
    if (!this.subscriptions.has(channel)) {
      this.subscriptions.set(channel, new Set());
    }
    this.subscriptions.get(channel)!.add(clientId);

    client.socket.emit('subscriptionConfirmed', {
      type: 'marketOverview',
      timestamp: new Date().toISOString(),
    });

    this.logger.debug('市场概览订阅成功', { clientId });
  }

  /**
   * 处理取消订阅请求
   */
  private handleUnsubscription(clientId: string, data: any): void {
    try {
      const { type, symbols, channels } = data;
      const client = this.connectedClients.get(clientId);
      
      if (!client) {
        this.logger.warn('客户端不存在', { clientId });
        return;
      }

      // 根据类型处理取消订阅
      if (channels && Array.isArray(channels)) {
        channels.forEach(channel => {
          this.unsubscribeFromChannel(clientId, channel);
        });
      } else if (type && symbols) {
        this.unsubscribeByType(clientId, type, symbols);
      }

      client.socket.emit('unsubscriptionConfirmed', {
        type,
        symbols,
        channels,
        timestamp: new Date().toISOString(),
      });

      this.logger.debug('取消订阅成功', { clientId, type, symbols, channels });
    } catch (error) {
      this.logger.error('处理取消订阅请求失败', { 
        clientId, 
        error: error instanceof Error ? error.message : '未知错误' 
      });
    }
  }

  /**
   * 从指定频道取消订阅
   */
  private unsubscribeFromChannel(clientId: string, channel: string): void {
    const client = this.connectedClients.get(clientId);
    if (!client) return;

    client.subscriptions.delete(channel);
    
    // 从全局订阅映射中移除
    const subscribers = this.subscriptions.get(channel);
    if (subscribers) {
      subscribers.delete(clientId);
      if (subscribers.size === 0) {
        this.subscriptions.delete(channel);
      }
    }
  }

  /**
   * 根据类型取消订阅
   */
  private unsubscribeByType(clientId: string, type: string, symbols: string[]): void {
    symbols.forEach(symbol => {
      let channel: string;
      switch (type) {
        case 'price':
          channel = `price:${symbol}`;
          break;
        case 'kline':
          // 需要处理所有时间间隔
          const client = this.connectedClients.get(clientId);
          if (client) {
            Array.from(client.subscriptions).forEach((sub) => {
              if (typeof sub === 'string' && sub.startsWith(`kline:${symbol}:`)) {
                this.unsubscribeFromChannel(clientId, sub as string);
              }
            });
          }
          return;
        default:
          return;
      }
      this.unsubscribeFromChannel(clientId, channel);
    });
  }

  /**
   * 处理客户端断开连接
   */
  private handleDisconnection(clientId: string, reason: string): void {
    this.logger.info('客户端断开连接', { clientId, reason });

    const client = this.connectedClients.get(clientId);
    if (client) {
      // 清理所有订阅
      client.subscriptions.forEach((channel: string) => {
        const subscribers = this.subscriptions.get(channel);
        if (subscribers) {
          subscribers.delete(clientId);
          if (subscribers.size === 0) {
            this.subscriptions.delete(channel);
          }
        }
      });

      // 移除客户端
      this.connectedClients.delete(clientId);
    }
  }

  /**
   * 获取WebSocket服务器实例
   */
  getServer(): SocketIOServer | null {
    return this.io;
  }

  /**
   * 获取连接的客户端数量
   */
  getConnectedClientsCount(): number {
    return this.connectedClients.size;
  }

  /**
   * 获取订阅统计信息
   */
  getSubscriptionStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    this.subscriptions.forEach((subscribers, channel) => {
      stats[channel] = subscribers.size;
    });
    return stats;
  }

  /**
   * 广播价格更新
   */
  broadcastPriceUpdate(symbol: string, priceData: any): void {
    const channel = `price:${symbol}`;
    const subscribers = this.subscriptions.get(channel);

    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const message = {
      type: 'priceUpdate',
      symbol,
      data: priceData,
      timestamp: new Date().toISOString(),
    };

    subscribers.forEach(clientId => {
      const client = this.connectedClients.get(clientId);
      if (client?.socket.connected) {
        client.socket.emit('priceUpdate', message);
      }
    });

    this.logger.debug('价格更新已广播', { symbol, subscriberCount: subscribers.size });
  }

  /**
   * 广播K线更新
   */
  broadcastKlineUpdate(symbol: string, interval: string, klineData: any): void {
    const channel = `kline:${symbol}:${interval}`;
    const subscribers = this.subscriptions.get(channel);

    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const message = {
      type: 'klineUpdate',
      symbol,
      interval,
      data: klineData,
      timestamp: new Date().toISOString(),
    };

    subscribers.forEach(clientId => {
      const client = this.connectedClients.get(clientId);
      if (client?.socket.connected) {
        client.socket.emit('klineUpdate', message);
      }
    });

    this.logger.debug('K线更新已广播', { symbol, interval, subscriberCount: subscribers.size });
  }

  /**
   * 广播市场概览更新
   */
  broadcastMarketOverview(marketData: any): void {
    const channel = 'marketOverview';
    const subscribers = this.subscriptions.get(channel);

    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const message = {
      type: 'marketOverview',
      data: marketData,
      timestamp: new Date().toISOString(),
    };

    subscribers.forEach(clientId => {
      const client = this.connectedClients.get(clientId);
      if (client?.socket.connected) {
        client.socket.emit('marketOverview', message);
      }
    });

    this.logger.debug('市场概览已广播', { subscriberCount: subscribers.size });
  }

  /**
   * 向特定客户端发送消息
   */
  sendToClient(clientId: string, event: string, data: any): boolean {
    const client = this.connectedClients.get(clientId);
    if (client?.socket.connected) {
      client.socket.emit(event, data);
      return true;
    }
    return false;
  }

  /**
   * 向所有客户端广播消息
   */
  broadcast(event: string, data: any): void {
    if (!this.io) return;

    this.io.emit(event, data);
    this.logger.debug('消息已广播', { event, clientCount: this.connectedClients.size });
  }

  /**
   * 清理断开的连接
   */
  cleanupDisconnectedClients(): void {
    const disconnectedClients: string[] = [];

    this.connectedClients.forEach((client, clientId) => {
      if (!client.socket.connected) {
        disconnectedClients.push(clientId);
      }
    });

    disconnectedClients.forEach(clientId => {
      this.handleDisconnection(clientId, 'cleanup');
    });

    if (disconnectedClients.length > 0) {
      this.logger.info('清理断开的连接', { count: disconnectedClients.length });
    }
  }

  /**
   * 关闭WebSocket服务器
   */
  async close(): Promise<void> {
    if (this.io) {
      this.logger.info('正在关闭WebSocket服务器...');

      // 通知所有客户端服务器即将关闭
      this.broadcast('serverShutdown', {
        message: '服务器即将关闭',
        timestamp: new Date().toISOString(),
      });

      // 等待一段时间让客户端处理消息
      await new Promise<void>(resolve => setTimeout(resolve, 1000));

      // 关闭所有连接
      this.io.close();
      this.io = null;

      // 清理数据
      this.connectedClients.clear();
      this.subscriptions.clear();

      this.logger.info('WebSocket服务器已关闭');
    }
  }
}

// 全局WebSocket服务器实例
let globalWebSocketServer: WebSocketServer | null = null;

/**
 * 设置WebSocket服务器
 */
export async function setupWebSocket(server: any): Promise<void> {
  try {
    globalWebSocketServer = new WebSocketServer();
    globalWebSocketServer.initialize(server);
    const logger = getLogger('WebSocketSetup');
    logger.info('✅ WebSocket服务器初始化完成');
  } catch (error) {
    const logger = getLogger('WebSocketSetup');
    logger.error('❌ WebSocket服务器初始化失败', { error: error instanceof Error ? error.message : String(error) });
    throw error;
  }
}

/**
 * 获取WebSocket服务器实例
 */
export function getWebSocketServer(): WebSocketServer {
  if (!globalWebSocketServer) {
    throw new Error('WebSocket服务器未初始化，请先调用 setupWebSocket()');
  }
  return globalWebSocketServer;
}
