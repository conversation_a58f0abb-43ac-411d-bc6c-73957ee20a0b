/**
 * 风险计算阶段 - V3.0策略模式实现
 * 使用核心分析服务进行风险指标计算，避免重复实现
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../../../di/types/index';
import { IProcessingStage, ProcessingContext } from '../../interfaces/IDataProcessingPipeline';
import { ILogger } from '../../../logging/interfaces';
import { RiskDataAggregation } from './risk-data-fetching-stage';
import { RiskMetrics } from '../../../../../contexts/risk-management/domain/value-objects/risk-metrics';
import { RiskFactor } from '../../../../../contexts/risk-management/domain/value-objects/risk-factor';
import { IDynamicWeightingService } from '../../../analysis/interfaces/IDynamicWeightingService';
import { IMultiTimeframeService } from '../../../analysis/interfaces/IMultiTimeframeService';

import { IRiskMetricsCalculatorService } from '../../../../../contexts/risk-management/infrastructure/services/risk-metrics-calculator-service.interface';
import { IFinancialMetricsService } from '../../../analysis/interfaces/IFinancialMetricsService';

/**
 * 风险计算结果接口
 */
export interface RiskCalculationResult {
  // 核心风险指标
  riskMetrics: RiskMetrics;
  
  // 风险因子
  riskFactors: RiskFactor[];
  
  // 组合风险
  portfolioRisk: {
    totalRisk: number;
    concentrationRisk: number;
    diversificationBenefit: number;
    correlationRisk: number;
  };
  
  // 压力测试结果
  stressTestResults: {
    scenarios: Array<{
      name: string;
      potentialLoss: number;
      probability: number;
      recoveryTime: number;
    }>;
    worstCaseScenario: {
      loss: number;
      probability: number;
    };
  };
  
  // 动态权重分配结果
  weightingResult?: {
    timeframeWeights: Record<string, number>;
    strategy: string;
    confidence: number;
  };
  
  // 多时间框架分析结果
  multiTimeframeAnalysis?: {
    alignment: {
      overallConsistency: number;
      dominantTrend: {
        direction: string;
        strength: number;
      };
      conflicts: Array<{
        timeframes: string[];
        description: string;
        severity: number;
      }>;
    };
  };
  
  // 元数据
  timestamp: Date;
  calculationDuration: number;
  dataQuality: number;
  confidence: number;
}

/**
 * 风险计算阶段实现
 */
@injectable()
export class RiskCalculationStage implements IProcessingStage {
  readonly stageName = 'risk-calculation';

  constructor(
    @inject(TYPES.Logger)
    private readonly logger: ILogger,

    // 重构后的风险指标计算服务
    @inject(TYPES.RiskManagement.RiskMetricsCalculatorService)
    private readonly riskMetricsCalculatorService: IRiskMetricsCalculatorService,

    // 通用金融指标服务
    @inject(TYPES.Shared.FinancialMetricsService)
    private readonly financialMetricsService: IFinancialMetricsService,

    // 核心分析服务
    @inject(TYPES.Shared.DynamicWeightingService)
    private readonly dynamicWeightingService: IDynamicWeightingService,

    @inject(TYPES.Shared.MultiTimeframeService)
    private readonly multiTimeframeService: IMultiTimeframeService
  ) {}

  /**
   * 执行风险计算处理
   */
  async process(
    context: ProcessingContext, 
    previousStageOutput: RiskDataAggregation
  ): Promise<RiskCalculationResult> {
    const startTime = performance.now();

    this.logger.info('开始风险计算阶段', {
      symbol: previousStageOutput.symbol.symbol,
      dataQuality: previousStageOutput.dataQuality.overallQuality,
      availableSources: previousStageOutput.metadata.sources
    });

    try {
      // 1. 基础风险指标计算
      const riskMetrics = await this.calculateBasicRiskMetrics(previousStageOutput);
      
      // 2. 使用核心分析服务进行高级分析
      const [weightingResult, multiTimeframeAnalysis] = await Promise.allSettled([
        this.performDynamicWeighting(previousStageOutput),
        this.performMultiTimeframeAnalysis(previousStageOutput)
      ]);

      // 3. 计算组合风险
      const portfolioRisk = await this.calculatePortfolioRisk(previousStageOutput, riskMetrics);

      // 4. 执行压力测试
      const stressTestResults = await this.performStressTest(previousStageOutput, riskMetrics);

      // 5. 识别风险因子
      const riskFactors = await this.identifyRiskFactors(riskMetrics, previousStageOutput);

      // 6. 计算整体置信度
      const confidence = this.calculateOverallConfidence(
        previousStageOutput.dataQuality.overallQuality,
        weightingResult.status === 'fulfilled' ? weightingResult.value.confidence : 0.5,
        riskMetrics
      );

      const calculationDuration = performance.now() - startTime;

      const result: RiskCalculationResult = {
        riskMetrics,
        riskFactors,
        portfolioRisk,
        stressTestResults,
        weightingResult: weightingResult.status === 'fulfilled' ? weightingResult.value : undefined,
        multiTimeframeAnalysis: multiTimeframeAnalysis.status === 'fulfilled' ? multiTimeframeAnalysis.value : undefined,
        timestamp: new Date(),
        calculationDuration,
        dataQuality: previousStageOutput.dataQuality.overallQuality,
        confidence
      };

      this.logger.info('风险计算阶段完成', {
        symbol: previousStageOutput.symbol.symbol,
        duration: calculationDuration.toFixed(3),
        confidence: confidence.toFixed(3),
        riskFactorsCount: riskFactors.length,
        overallRisk: riskMetrics.getCompositeRiskScore()
      });

      return result;

    } catch (error) {
      const calculationDuration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      this.logger.error('风险计算阶段失败', {
        symbol: previousStageOutput.symbol.symbol,
        error: errorMessage,
        duration: calculationDuration.toFixed(3)
      });

      throw new Error(`风险计算失败: ${errorMessage}`);
    }
  }

  /**
   * 验证输入数据
   */
  async validateInput(context: ProcessingContext, input: RiskDataAggregation): Promise<boolean> {
    try {
      if (!input || !input.marketData || !input.positionData || !input.accountData) {
        this.logger.warn('风险计算阶段输入验证失败：缺少必要的数据');
        return false;
      }

      if (input.dataQuality.overallQuality < 0.6) {
        this.logger.warn('风险计算阶段输入验证失败：数据质量过低', {
          quality: input.dataQuality.overallQuality
        });
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('风险计算阶段输入验证异常', { error });
      return false;
    }
  }

  /**
   * 获取阶段配置
   */
  getStageConfig(): Record<string, any> {
    return {
      stageName: this.stageName,
      description: '使用核心分析服务和专用风险计算服务计算多维度风险指标',
      requiredServices: ['RiskMetricsCalculatorService', 'FinancialMetricsService', 'DynamicWeightingService', 'MultiTimeframeService'],
      timeout: 30000,
      retryCount: 1,
      dataQualityThreshold: 0.6
    };
  }

  /**
   * 计算基础风险指标
   * 重构后的流程：先获取通用金融指标，再计算风险专属指标
   */
  private async calculateBasicRiskMetrics(data: RiskDataAggregation): Promise<RiskMetrics> {
    try {
      // 1. 从价格历史中提取价格序列
      const prices = data.marketData.priceHistory.map(item => item.price);
      
      // 2. 并行调用通用金融指标服务获取基础指标
      const [volatility, returns, sharpeRatio] = await Promise.all([
        this.financialMetricsService.calculateVolatility(prices),
        this.financialMetricsService.calculateReturns(prices),
        this.calculateSharpeRatioFromService(prices)
      ]);

      // 3. 使用重构后的风险指标计算服务计算风险专属指标
      const [var95, cvar95, maxDrawdown, liquidityRisk] = await Promise.all([
        this.riskMetricsCalculatorService.calculateVaR(
          data.positionData.positions[0] || {} as any,
          data.marketData.priceHistory,
          [0.95, 0.99],
          [1, 7, 30],
          'HISTORICAL_SIMULATION' as any
        ),
        this.riskMetricsCalculatorService.calculateCVaR(
          data.positionData.positions[0] || {} as any,
          data.marketData.priceHistory,
          [0.95, 0.99],
          [1, 7, 30]
        ),
        this.riskMetricsCalculatorService.calculateMaxDrawdown(
          data.positionData.positions,
          data.marketData.priceHistory
        ),
        this.riskMetricsCalculatorService.calculateLiquidityRisk(
          data.positionData.positions,
          data.marketData.priceHistory
        )
      ]);

      // 4. 构建完整的风险指标对象
      const riskMetrics = RiskMetrics.create({
        volatility,
        var95,
        cvar95,
        maxDrawdown,
        sharpeRatio,
        liquidityRisk,
        overallRiskScore: this.calculateOverallRiskScore({
          volatility,
          var95,
          cvar95,
          maxDrawdown,
          liquidityRisk
        })
      });

      this.logger.debug('基础风险指标计算完成', {
        volatility: riskMetrics.volatility,
        var95: riskMetrics.var95,
        maxDrawdown: riskMetrics.maxDrawdown,
        sharpeRatio: riskMetrics.sharpeRatio
      });

      return riskMetrics;
    } catch (error) {
      this.logger.error('基础风险指标计算失败', { error });
      throw error;
    }
  }

  /**
   * 执行动态权重分配
   */
  private async performDynamicWeighting(data: RiskDataAggregation): Promise<any> {
    try {
      if (!data.trendAnalysis?.trends) {
        throw new Error('缺少趋势分析数据，无法执行动态权重分配');
      }

      // 转换趋势数据格式
      const trends = data.trendAnalysis.trends.map(trend => ({
        timeframe: trend.timeframe,
        direction: trend.direction,
        strength: trend.strength,
        confidence: trend.confidence,
        volume: 1.0, // 默认值
        momentum: trend.strength * trend.confidence,
        timestamp: new Date()
      }));

      // 构建市场状况
      const prices = data.marketData.priceHistory.map(item => item.price);
      const volatilityResult = prices.length > 1 ? await this.financialMetricsService.calculateVolatility(prices) : { annualized: 0.02 };
      
      const marketCondition = {
        volatility: typeof volatilityResult === 'number' ? volatilityResult : volatilityResult.annualized || volatilityResult,
        volume: data.marketData.priceHistory.reduce((sum, item) => sum + item.volume, 0) / data.marketData.priceHistory.length,
        trend: trends.length > 0 ? trends[0].direction : 'neutral',
        timestamp: new Date()
      };

      const result = await this.dynamicWeightingService.allocate(trends, marketCondition);

      this.logger.debug('动态权重分配完成', {
        strategy: result.strategy,
        confidence: result.confidence,
        weights: Object.keys(result.weights).length
      });

      return {
        timeframeWeights: result.weights,
        strategy: result.strategy,
        confidence: result.confidence
      };
    } catch (error) {
      this.logger.warn('动态权重分配失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 执行多时间框架分析
   */
  private async performMultiTimeframeAnalysis(data: RiskDataAggregation): Promise<any> {
    try {
      if (!data.marketData.priceHistory || data.marketData.priceHistory.length === 0) {
        throw new Error('缺少价格历史数据，无法执行多时间框架分析');
      }

      // 构建多时间框架数据查询
      const query = {
        symbol: data.symbol.symbol,
        timeframes: ['1h', '4h', '1d'],
        startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
        endTime: new Date(),
        limit: 168 // 7天的小时数据
      };

      // 收集多时间框架数据
      const multiTimeframeData = await this.multiTimeframeService.collectData(query);
      
      // 分析趋势一致性
      const alignment = await this.multiTimeframeService.getAlignment(multiTimeframeData);

      this.logger.debug('多时间框架分析完成', {
        consistency: alignment.overallConsistency,
        dominantTrend: alignment.dominantTrend.direction,
        conflicts: alignment.conflicts.length
      });

      return { alignment };
    } catch (error) {
      this.logger.warn('多时间框架分析失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 计算组合风险
   */
  private async calculatePortfolioRisk(data: RiskDataAggregation, riskMetrics: RiskMetrics): Promise<any> {
    try {
      // 使用重构后的风险指标计算服务的组合风险计算功能
      const portfolioRisk = await this.riskMetricsCalculatorService.calculatePortfolioRisk(
        data.positionData.positions,
        data.marketData.priceHistory
      );

      return {
        totalRisk: portfolioRisk.totalRisk || 0,
        concentrationRisk: portfolioRisk.concentrationRisk || 0,
        diversificationBenefit: portfolioRisk.diversificationBenefit || 0,
        correlationRisk: 0 // portfolioRisk对象暂时不包含correlationRisk属性
      };
    } catch (error) {
      this.logger.warn('组合风险计算失败，使用默认值', { error: error.message });
      return {
        totalRisk: Math.abs(riskMetrics.var95) || 0,
        concentrationRisk: 0,
        diversificationBenefit: 0,
        correlationRisk: 0
      };
    }
  }

  /**
   * 执行压力测试
   */
  private async performStressTest(data: RiskDataAggregation, riskMetrics: RiskMetrics): Promise<any> {
    try {
      // 使用重构后的风险指标计算服务的压力测试功能
      const stressTestResult = await this.riskMetricsCalculatorService.performStressTest(
        data.positionData.positions,
        data.marketData.priceHistory
      );

      return {
        scenarios: stressTestResult.scenarios || [],
        worstCaseScenario: stressTestResult.worstCaseScenario || {
          loss: riskMetrics.var95 * 2, // 使用VaR的2倍作为最坏情况
          probability: 0.01
        }
      };
    } catch (error) {
      this.logger.warn('压力测试失败，使用默认值', { error: error.message });
      return {
        scenarios: [],
        worstCaseScenario: {
          loss: riskMetrics.var95 * 2,
          probability: 0.01
        }
      };
    }
  }

  /**
   * 识别风险因子
   */
  private async identifyRiskFactors(riskMetrics: RiskMetrics, data: RiskDataAggregation): Promise<RiskFactor[]> {
    try {
      // 使用重构后的风险指标计算服务的风险因子识别功能
      const riskFactors = await this.riskMetricsCalculatorService.identifyRiskFactors(
        riskMetrics,
        data.positionData.positions,
        data.marketData.priceHistory
      );

      return riskFactors || [];
    } catch (error) {
      this.logger.warn('风险因子识别失败', { error: error.message });
      return [];
    }
  }

  /**
   * 计算整体置信度
   */
  private calculateOverallConfidence(
    dataQuality: number,
    weightingConfidence: number,
    riskMetrics: RiskMetrics
  ): number {
    // 数据质量权重50%，权重分配置信度权重30%，风险指标完整性权重20%
    const riskMetricsCompleteness = this.calculateRiskMetricsCompleteness(riskMetrics);
    
    return dataQuality * 0.5 + weightingConfidence * 0.3 + riskMetricsCompleteness * 0.2;
  }

  /**
   * 计算风险指标完整性
   */
  private calculateRiskMetricsCompleteness(riskMetrics: RiskMetrics): number {
    const requiredFields = ['volatility', 'var95', 'cvar95', 'maxDrawdown', 'sharpeRatio'];
    const availableFields = requiredFields.filter(field => 
      riskMetrics[field] !== undefined && riskMetrics[field] !== null
    );
    
    return availableFields.length / requiredFields.length;
  }

  /**
   * 使用通用金融指标服务计算夏普比率
   */
  private async calculateSharpeRatioFromService(prices: number[]): Promise<number> {
    try {
      const returns = await this.financialMetricsService.calculateReturns(prices);
      const riskFreeRate = 0.02; // 默认无风险利率2%
      return await this.financialMetricsService.calculateSharpeRatio(returns, riskFreeRate);
    } catch (error) {
      this.logger.warn('夏普比率计算失败，使用默认值', { error: error.message });
      return 0;
    }
  }

  /**
   * 计算整体风险评分
   */
  private calculateOverallRiskScore(metrics: {
    volatility: number;
    var95: number;
    cvar95: number;
    maxDrawdown: number;
    liquidityRisk: number;
  }): number {
    // 使用加权平均计算整体风险评分
    const weights = {
      volatility: 0.25,
      var95: 0.25,
      cvar95: 0.20,
      maxDrawdown: 0.20,
      liquidityRisk: 0.10
    };

    // 将各指标标准化到0-1范围
    const normalizedVolatility = Math.min(metrics.volatility / 0.5, 1); // 假设50%为极高波动率
    const normalizedVar = Math.min(Math.abs(metrics.var95) / 0.2, 1); // 假设20%为极高VaR
    const normalizedCVar = Math.min(Math.abs(metrics.cvar95) / 0.25, 1); // 假设25%为极高CVaR
    const normalizedMaxDrawdown = Math.min(Math.abs(metrics.maxDrawdown) / 0.3, 1); // 假设30%为极高回撤
    const normalizedLiquidityRisk = Math.min(metrics.liquidityRisk, 1); // 假设已经是0-1范围

    return (
      normalizedVolatility * weights.volatility +
      normalizedVar * weights.var95 +
      normalizedCVar * weights.cvar95 +
      normalizedMaxDrawdown * weights.maxDrawdown +
      normalizedLiquidityRisk * weights.liquidityRisk
    );
  }


}