/**
 * 外部数据适配器接口定义
 * 为所有外部API适配器提供统一的接口规范
 */

import { ProcessingContext, ProcessingResult } from './IDataProcessingPipeline';

/**
 * 适配器配置接口
 */
export interface AdapterConfig {
  name: string;
  baseUrl: string;
  timeout?: number;
  maxRetries?: number;
  rateLimitConfig?: {
    requestsPerSecond: number;
    burstSize: number;
  };
  headers?: Record<string, string>;
  enableCache?: boolean;
  cacheValidityMinutes?: number;
}

/**
 * 适配器健康状态接口
 */
export interface AdapterHealthStatus {
  isHealthy: boolean;
  latency?: number;
  lastCheck: Date;
  error?: string;
  requestCount?: number;
  successRate?: number;
}

/**
 * 适配器统计信息接口
 */
export interface AdapterStatistics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageLatency: number;
  lastRequestTime?: Date;
  cacheHitRate?: number;
}

/**
 * 数据获取选项接口
 */
export interface FetchOptions {
  timeout?: number;
  retries?: number;
  useCache?: boolean;
  cacheKey?: string;
  headers?: Record<string, string>;
  params?: Record<string, any>;
}

/**
 * 外部数据适配器主接口
 */
export interface IExternalDataAdapter {
  /**
   * 适配器名称
   */
  readonly adapterName: string;

  /**
   * 数据源名称
   */
  readonly sourceName: string;

  /**
   * 获取并处理数据（核心方法）
   */
  fetchAndProcess<T>(
    endpoint: string,
    options: FetchOptions,
    context: Partial<ProcessingContext>
  ): Promise<T>;

  /**
   * 批量获取并处理数据
   */
  fetchAndProcessBatch<T>(
    requests: Array<{
      endpoint: string;
      options: FetchOptions;
      context: Partial<ProcessingContext>;
    }>
  ): Promise<T[]>;

  /**
   * 健康检查
   */
  healthCheck(): Promise<AdapterHealthStatus>;

  /**
   * 获取统计信息
   */
  getStatistics(): Promise<AdapterStatistics>;

  /**
   * 测试连接
   */
  testConnection(): Promise<boolean>;

  /**
   * 清除缓存
   */
  clearCache(): void;
}

/**
 * 可配置适配器接口
 */
export interface IConfigurableAdapter extends IExternalDataAdapter {
  /**
   * 更新配置
   */
  updateConfig(config: Partial<AdapterConfig>): void;

  /**
   * 获取当前配置
   */
  getConfig(): AdapterConfig;
}

/**
 * 缓存适配器接口
 */
export interface ICacheableAdapter extends IExternalDataAdapter {
  /**
   * 设置缓存
   */
  setCache(key: string, data: any, ttlMinutes?: number): void;

  /**
   * 获取缓存
   */
  getCache<T>(key: string): T | null;

  /**
   * 检查缓存是否存在
   */
  hasCache(key: string): boolean;

  /**
   * 删除特定缓存
   */
  deleteCache(key: string): void;
}

/**
 * 监控适配器接口
 */
export interface IMonitorableAdapter extends IExternalDataAdapter {
  /**
   * 记录请求指标
   */
  recordRequestMetrics(success: boolean, latency: number, endpoint: string): void;

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): {
    averageLatency: number;
    successRate: number;
    requestsPerMinute: number;
    errorRate: number;
  };

  /**
   * 重置指标
   */
  resetMetrics(): void;
}

/**
 * 适配器错误类型
 */
export enum AdapterErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PROCESSING_ERROR = 'PROCESSING_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR'
}

/**
 * 适配器错误类
 */
export class AdapterError extends Error {
  constructor(
    public readonly type: AdapterErrorType,
    public readonly adapterName: string,
    message: string,
    public readonly originalError?: Error,
    public readonly context?: any
  ) {
    super(message);
    this.name = 'AdapterError';
  }
}

/**
 * 适配器工厂接口
 */
export interface IAdapterFactory {
  /**
   * 创建适配器实例
   */
  createAdapter(adapterName: string, config: AdapterConfig): IExternalDataAdapter;

  /**
   * 注册适配器类
   */
  registerAdapter(name: string, adapterClass: new (config: AdapterConfig, ...args: any[]) => IExternalDataAdapter): void;

  /**
   * 获取支持的适配器列表
   */
  getSupportedAdapters(): string[];
}

/**
 * 适配器注册信息
 */
export interface AdapterRegistration {
  name: string;
  description: string;
  supportedDataTypes: string[];
  requiredConfig: string[];
  optionalConfig: string[];
  adapterClass: new (config: AdapterConfig, ...args: any[]) => IExternalDataAdapter;
}
