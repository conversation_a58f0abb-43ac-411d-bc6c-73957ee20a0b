/**
 * 上下文特定类型定义
 * 拆分自types.ts，减少模块耦合度
 */

/**
 * 用户管理上下文类型
 */
export const USER_MANAGEMENT_TYPES = {
  // 应用服务
  UserManagementApplicationService: Symbol.for('UserManagement.UserManagementApplicationService'),
  AuthenticationService: Symbol.for('UserManagement.AuthenticationService'),
  AuthorizationService: Symbol.for('UserManagement.AuthorizationService'),
  ApiKeyService: Symbol.for('UserManagement.ApiKeyService'),
  InvitationService: Symbol.for('UserManagement.InvitationService'),
  AuditService: Symbol.for('UserManagement.AuditService'),
  
  // 基础设施服务
  PasswordService: Symbol.for('UserManagement.PasswordService'),
  JwtService: Symbol.for('UserManagement.JwtService'),
  
  // 控制器
  UserController: Symbol.for('UserManagement.UserController'),
  AuthController: Symbol.for('UserManagement.AuthController'),
  ApiKeyController: Symbol.for('UserManagement.ApiKeyController'),
} as const;

/**
 * 市场数据上下文类型
 */
export const MARKET_DATA_TYPES = {
  // 应用服务
  MarketDataApplicationService: Symbol.for('MarketData.MarketDataApplicationService'),
  DataIngestionService: Symbol.for('MarketData.DataIngestionService'),
  DataValidationService: Symbol.for('MarketData.DataValidationService'),
  MarketDataSyncService: Symbol.for('MarketData.MarketDataSyncService'),
  RealTimeDataStreamService: Symbol.for('MarketData.RealTimeDataStreamService'),
  MultiExchangeWebSocketManager: Symbol.for('MarketData.MultiExchangeWebSocketManager'),
  MultiExchangeDataService: Symbol.for('MarketData.MultiExchangeDataService'),
  DataBackfillService: Symbol.for('MarketData.DataBackfillService'),

  // 数据提供者
  AlphaVantageProvider: Symbol.for('MarketData.AlphaVantageProvider'),
  YahooFinanceProvider: Symbol.for('MarketData.YahooFinanceProvider'),
  BinanceProvider: Symbol.for('MarketData.BinanceProvider'),

  // 基础设施服务
  RealMarketDataService: Symbol.for('MarketData.RealMarketDataService'),
  RealDataIntegrationService: Symbol.for('MarketData.RealDataIntegrationService'),

  // 控制器
  MarketDataController: Symbol.for('MarketData.MarketDataController'),

  // 适配器
  SentiCryptAdapter: Symbol.for('MarketData.SentiCryptAdapter'),
  FearGreedAdapter: Symbol.for('MarketData.FearGreedAdapter'),
  CoinMetricsAdapter: Symbol.for('MarketData.CoinMetricsAdapter'),
  MempoolAdapter: Symbol.for('MarketData.MempoolAdapter'),

  // 交易所适配器
  BinanceAdapter: Symbol.for('MarketData.BinanceAdapter'),
  BinanceFuturesAdapter: Symbol.for('MarketData.BinanceFuturesAdapter'),
  KrakenAdapter: Symbol.for('MarketData.KrakenAdapter'),
  OKXAdapter: Symbol.for('MarketData.OKXAdapter'),
  CoinbaseAdapter: Symbol.for('MarketData.CoinbaseAdapter'),
  TokenMetricsAdapter: Symbol.for('MarketData.TokenMetricsAdapter'),

  ExchangeAdapterFactory: Symbol.for('MarketData.ExchangeAdapterFactory'),

  // 仓储
  MarketSymbolRepository: Symbol.for('MarketData.MarketSymbolRepository'),
  SymbolRepository: Symbol.for('MarketData.SymbolRepository'), // 向后兼容
  PriceDataRepository: Symbol.for('MarketData.PriceDataRepository'),
  HistoricalDataRepository: Symbol.for('MarketData.HistoricalDataRepository'),

  // WebSocket服务
  RealWebSocketManager: Symbol.for('MarketData.RealWebSocketManager'),
  WebSocketService: Symbol.for('MarketData.WebSocketService'),

  // 数据服务
  DataUpdateService: Symbol.for('MarketData.DataUpdateService'),
  TimestampConflictDetector: Symbol.for('MarketData.TimestampConflictDetector'),
  StreamDataCleaningEngine: Symbol.for('MarketData.StreamDataCleaningEngine'),
  APIHealthMonitor: Symbol.for('MarketData.APIHealthMonitor'),
  RealTimeDataQualityMonitor: Symbol.for('MarketData.RealTimeDataQualityMonitor'),
  DataAnomalyDetectionEngine: Symbol.for('MarketData.DataAnomalyDetectionEngine'),
  ExchangeRouter: Symbol.for('MarketData.ExchangeRouter'),
  IntelligentExchangeRouter: Symbol.for('MarketData.IntelligentExchangeRouter'),
  HighPerformanceDataDistributionNetwork: Symbol.for('MarketData.HighPerformanceDataDistributionNetwork'),
  IntelligentMultiLayerCacheSystem: Symbol.for('MarketData.IntelligentMultiLayerCacheSystem'),
} as const;

/**
 * 风险管理上下文类型
 */
export const RISK_MANAGEMENT_TYPES = {
  // 应用服务
  RiskAssessmentApplicationService: Symbol.for('RiskManagement.RiskAssessmentApplicationService'),
  
  // 领域服务
  RiskMetricsCalculatorService: Symbol.for('RiskManagement.RiskMetricsCalculatorService'),
  FinancialMetricsService: Symbol.for('RiskManagement.FinancialMetricsService'),
  
  // 控制器
  RiskAssessmentController: Symbol.for('RiskManagement.RiskAssessmentController'),
} as const;

/**
 * 趋势分析上下文类型
 */
export const TREND_ANALYSIS_TYPES = {
  // 应用服务
  TrendAnalysisApplicationService: Symbol.for('TrendAnalysis.TrendAnalysisApplicationService'),
  
  // 领域服务
  TrendDetectionService: Symbol.for('TrendAnalysis.TrendDetectionService'),
  TechnicalAnalysisService: Symbol.for('TrendAnalysis.TechnicalAnalysisService'),
  
  // 控制器
  TrendAnalysisController: Symbol.for('TrendAnalysis.TrendAnalysisController'),
} as const;

/**
 * 用户配置上下文类型
 */
export const USER_CONFIG_TYPES = {
  // 应用服务
  UserConfigApplicationService: Symbol.for('UserConfig.UserConfigApplicationService'),
  UserProfileApplicationService: Symbol.for('UserConfig.UserProfileApplicationService'),
  UserPreferencesApplicationService: Symbol.for('UserConfig.UserPreferencesApplicationService'),
  ModelSelectionService: Symbol.for('UserConfig.ModelSelectionService'),
  UserConfigService: Symbol.for('UserConfig.UserConfigService'),
  ConfigHotReloadService: Symbol.for('UserConfig.ConfigHotReloadService'),
  ConfigChangeListener: Symbol.for('UserConfig.ConfigChangeListener'),
  EnhancedLLMRouter: Symbol.for('UserConfig.EnhancedLLMRouter'),
  UserConfigValidator: Symbol.for('UserConfig.UserConfigValidator'),
  UserConfigSystemAdapter: Symbol.for('UserConfig.UserConfigSystemAdapter'),
  UserConfigQualityAdapter: Symbol.for('UserConfig.UserConfigQualityAdapter'),

  // 基础设施服务
  EncryptionService: Symbol.for('UserConfig.EncryptionService'),

  // 控制器
  UserConfigController: Symbol.for('UserConfig.UserConfigController'),
  ModelPreferenceController: Symbol.for('UserConfig.ModelPreferenceController'),
  StatisticsController: Symbol.for('UserConfig.StatisticsController'),
  UserProfileController: Symbol.for('UserConfig.UserProfileController'),
  UserPreferencesController: Symbol.for('UserConfig.UserPreferencesController'),
} as const;

/**
 * AI推理上下文类型
 */
export const AI_REASONING_TYPES = {
  // 应用服务
  AIReasoningApplicationService: Symbol.for('AIReasoning.AIReasoningApplicationService'),

  // 控制器
  DualLayerReasoningController: Symbol.for('AIReasoning.DualLayerReasoningController'),

  // LLM提供者
  OpenAIProvider: Symbol.for('AIReasoning.OpenAIProvider'),
  AnthropicProvider: Symbol.for('AIReasoning.AnthropicProvider'),
  GeminiProvider: Symbol.for('AIReasoning.GeminiProvider'),
  LLMRouter: Symbol.for('AIReasoning.LLMRouter'),

  // 推理引擎
  LLMService: Symbol.for('AIReasoning.LLMService'),
  ReasoningChain: Symbol.for('AIReasoning.ReasoningChain'),
  UnifiedDecisionEngine: Symbol.for('AIReasoning.UnifiedDecisionEngine'),
  ContinuousLearningEngine: Symbol.for('AIReasoning.ContinuousLearningEngine'),

  // 纯净AI分析引擎
  PureAITradingSignalGenerator: Symbol.for('AIReasoning.PureAITradingSignalGenerator'),
  PureAIRiskAnalysisEngine: Symbol.for('AIReasoning.PureAIRiskAnalysisEngine'),
  PureAITrendAnalysisEngine: Symbol.for('AIReasoning.PureAITrendAnalysisEngine'),
  PureAIAnalyzer: Symbol.for('AIReasoning.PureAIAnalyzer'),
  LearningAnalysisEngine: Symbol.for('AIReasoning.LearningAnalysisEngine'),
  FinancialKnowledgeGraph: Symbol.for('AIReasoning.FinancialKnowledgeGraph'),
  ParameterConfigCenter: Symbol.for('AIReasoning.ParameterConfigCenter'),
  LearningKnowledgeBase: Symbol.for('AIReasoning.LearningKnowledgeBase'),
  TimeframeCoordinator: Symbol.for('AIReasoning.TimeframeCoordinator'),
  MultiTimeframeLearningCoordinator: Symbol.for('AIReasoning.MultiTimeframeLearningCoordinator'),

  // 统一学习系统
  UnifiedLearningSystemStarter: Symbol.for('AIReasoning.UnifiedLearningSystemStarter'),
  UnifiedPredictionEngine: Symbol.for('AIReasoning.UnifiedPredictionEngine'),
  UnifiedLearningEngine: Symbol.for('AIReasoning.UnifiedLearningEngine'),
  MacroPredictionService: Symbol.for('AIReasoning.MacroPredictionService'),

  // 推理可追溯性服务
  ReasoningProcessRecorder: Symbol.for('AIReasoning.ReasoningProcessRecorder'),
  DecisionPathTracker: Symbol.for('AIReasoning.DecisionPathTracker'),
  ReasoningVisualizationService: Symbol.for('AIReasoning.ReasoningVisualizationService'),
} as const;

/**
 * 交易分析上下文类型（向后兼容）
 */
export const TRADING_ANALYSIS_TYPES = {
  // 技术分析服务（已迁移到trend-analysis系统）
  TechnicalIndicatorCalculator: Symbol.for('TradingAnalysis.TechnicalIndicatorCalculator'),
  SignalFusionCoordinator: Symbol.for('TradingAnalysis.SignalFusionCoordinator'),
  EnhancedSignalFusionCoordinator: Symbol.for('TradingAnalysis.EnhancedSignalFusionCoordinator'),
  RealSignalGenerationService: Symbol.for('TradingAnalysis.RealSignalGenerationService'),
  ProductionSignalService: Symbol.for('TradingAnalysis.ProductionSignalService'),
} as const;

/**
 * 交易信号上下文类型
 */
export const TRADING_SIGNALS_TYPES = {
  // 应用服务
  SignalGenerationApplicationService: Symbol.for('TradingSignals.SignalGenerationApplicationService'),
  TradingSignalApplicationService: Symbol.for('TradingSignals.TradingSignalApplicationService'),
  DegradationManagerService: Symbol.for('TradingSignals.DegradationManagerService'),
  
  // 领域服务
  StrategySelector: Symbol.for('TradingSignals.StrategySelector'),
  PositionSizeCalculator: Symbol.for('TradingSignals.PositionSizeCalculator'),
  
  // 策略工厂和策略实现
  StrategyFactory: Symbol.for('TradingSignals.StrategyFactory'),
  TrendFollowingStrategy: Symbol.for('TradingSignals.TrendFollowingStrategy'),
  MeanReversionStrategy: Symbol.for('TradingSignals.MeanReversionStrategy'),
} as const;
