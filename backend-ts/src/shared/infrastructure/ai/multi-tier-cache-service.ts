/**
 * 多层次缓存架构服务
 * 实现L1内存缓存、L2 Redis缓存、L3向量数据库的多层次缓存
 */

import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../di/types/index';
import { VectorDatabaseService, VectorStoreItem } from './vector-database-service';
import { EnhancedVectorService } from './vector-service';
import Redis from 'ioredis';
import { UnifiedEnvironmentManager } from '../config/environment/unified-environment-manager';
import { REDIS_DEFAULTS } from '../config/unified-default-config';
import { CacheItem, CacheStats, CacheMetrics } from '../types';

/**
 * 多层缓存项接口（扩展统一缓存项）
 */
export interface MultiTierCacheItem<T = any> extends CacheItem<T> {
  metadata: {
    timestamp: Date;
    ttl: number;
    accessCount: number;
    lastAccess: Date;
    quality: number;
    size: number;
    tags: string[];
  };
}

/**
 * 缓存层级枚举
 */
export enum CacheTier {
  L1_MEMORY = 'L1_MEMORY',
  L2_REDIS = 'L2_REDIS',
  L3_VECTOR = 'L3_VECTOR'
}

/**
 * 缓存策略配置
 */
export interface CacheStrategyConfig {
  l1: {
    maxSize: number;
    ttl: number;
    evictionPolicy: 'LRU' | 'LFU' | 'FIFO';
  };
  l2: {
    ttl: number;
    compressionEnabled: boolean;
    keyPrefix: string;
  };
  l3: {
    similarityThreshold: number;
    maxResults: number;
    qualityThreshold: number;
  };
}

/**
 * 多层缓存统计信息（扩展统一缓存统计）
 */
export interface MultiTierCacheStats extends CacheStats {
  l1: {
    hits: number;
    misses: number;
    size: number;
    hitRate: number;
  };
  l2: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  l3: {
    hits: number;
    misses: number;
    hitRate: number;
  };
  overall: {
    totalHits: number;
    totalMisses: number;
    overallHitRate: number;
  };
}

/**
 * L1内存缓存层
 */
export class L1MemoryCache<T = any> {
  private readonly cache = new Map<string, CacheItem<T>>();
  private readonly accessOrder = new Map<string, number>();
  private accessCounter = 0;

  constructor(
    private readonly maxSize: number,
    private readonly ttl: number,
    private readonly evictionPolicy: 'LRU' | 'LFU' | 'FIFO' = 'LRU'
  ) {}

  get(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查TTL
    const timestampMs = typeof item.timestamp === 'number' ? item.timestamp : item.timestamp.getTime();
    if (Date.now() - timestampMs > item.ttl) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      return null;
    }

    // 更新访问信息
     item.accessCount++;
    item.lastAccessed = new Date();
    this.accessOrder.set(key, ++this.accessCounter);

    return item.value;
  }

  set(key: string, value: T, ttl?: number): void {
    // 检查是否需要驱逐
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evict();
    }

    const item: CacheItem<T> = {
      key,
      value,
      timestamp: new Date(),
       ttl: ttl || this.ttl,
       accessCount: 1,
       lastAccessed: new Date(),
       size: this.calculateSize(value),
       metadata: {
         quality: 1.0,
         tags: []
       }
    };

    this.cache.set(key, item);
    this.accessOrder.set(key, ++this.accessCounter);
  }

  delete(key: string): boolean {
    this.accessOrder.delete(key);
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;
  }

  size(): number {
    return this.cache.size;
  }

  private evict(): void {
    let keyToEvict: string | null = null;

    switch (this.evictionPolicy) {
      case 'LRU':
        keyToEvict = this.findLRUKey();
        break;
      case 'LFU':
        keyToEvict = this.findLFUKey();
        break;
      case 'FIFO':
        keyToEvict = this.findFIFOKey();
        break;
    }

    if (keyToEvict) {
      this.delete(keyToEvict);
    }
  }

  private findLRUKey(): string | null {
    let oldestKey: string | null = null;
    let oldestAccess = Infinity;

    for (const [key, accessTime] of this.accessOrder) {
      if (accessTime < oldestAccess) {
        oldestAccess = accessTime;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFUKey(): string | null {
    let leastUsedKey: string | null = null;
    let leastAccessCount = Infinity;

    for (const [key, item] of this.cache) {
      if (item.accessCount < leastAccessCount) {
        leastAccessCount = item.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findFIFOKey(): string | null {
    let oldestKey: string | null = null;
    let oldestTimestamp = Date.now();

    for (const [key, item] of this.cache) {
      const itemTimestamp = typeof item.timestamp === 'number' ? item.timestamp : item.timestamp.getTime();
      if (itemTimestamp < oldestTimestamp) {
        oldestTimestamp = itemTimestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private calculateSize(value: any): number {
    // 简单的大小估算
    return JSON.stringify(value).length;
  }

  getStats(): { size: number; maxSize: number; items: CacheItem<T>[] } {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      items: Array.from(this.cache.values())
    };
  }
}

/**
 * L2 Redis缓存层
 */
@injectable()
export class L2RedisCache {
  private redis: Redis;
  private readonly keyPrefix: string;
  private readonly compressionEnabled: boolean;

  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.Shared.UnifiedEnvironmentManager) private readonly envManager: UnifiedEnvironmentManager,
    keyPrefix: string = 'ai-cache:',
    compressionEnabled: boolean = true
  ) {
    this.keyPrefix = keyPrefix;
    this.compressionEnabled = compressionEnabled;
    this.initializeRedis();
  }

  private initializeRedis(): void {
    const redisConfig = this.envManager.getRedisConfig();
    this.redis = new Redis({
      host: redisConfig.host,
      port: redisConfig.port,
      password: redisConfig.password,
      db: redisConfig.db,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.redis.on('error', (error) => {
      this.logger.error('Redis连接错误', { error });
    });

    this.redis.on('connect', () => {
      this.logger.info('Redis连接成功');
    });
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const fullKey = this.keyPrefix + key;
      const value = await this.redis.get(fullKey);
      
      if (!value) return null;

      const parsed = JSON.parse(value);
      
      // 检查TTL
      if (parsed.metadata && parsed.metadata.ttl) {
        const elapsed = Date.now() - new Date(parsed.metadata.timestamp).getTime();
        if (elapsed > parsed.metadata.ttl) {
          await this.delete(key);
          return null;
        }
      }

      return parsed.value;
    } catch (error) {
      this.logger.error('Redis获取失败', { error, key });
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      const fullKey = this.keyPrefix + key;
      const item: CacheItem<T> = {
        key,
        value,
        timestamp: new Date(),
        ttl: ttl || 3600000, // 默认1小时
        accessCount: 1,
        lastAccessed: new Date(),
        size: JSON.stringify(value).length,
        metadata: {
          quality: 1.0,
          tags: []
        }
      };

      const serialized = JSON.stringify(item);
      
      if (ttl) {
        await this.redis.setex(fullKey, Math.floor(ttl / 1000), serialized);
      } else {
        await this.redis.set(fullKey, serialized);
      }
    } catch (error) {
      this.logger.error('Redis设置失败', { error, key });
      throw error;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const fullKey = this.keyPrefix + key;
      const result = await this.redis.del(fullKey);
      return result > 0;
    } catch (error) {
      this.logger.error('Redis删除失败', { error, key });
      return false;
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = await this.redis.keys(this.keyPrefix + '*');
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      this.logger.error('Redis清理失败', { error });
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const fullKey = this.keyPrefix + key;
      const result = await this.redis.exists(fullKey);
      return result > 0;
    } catch (error) {
      this.logger.error('Redis存在性检查失败', { error, key });
      return false;
    }
  }

  async getStats(): Promise<{ keyCount: number; memoryUsage: string }> {
    try {
      const keys = await this.redis.keys(this.keyPrefix + '*');
      const info = await this.redis.info('memory');
      const memoryMatch = info.match(/usedMemoryHuman:(.+)/);
      
      return {
        keyCount: keys.length,
        memoryUsage: memoryMatch ? memoryMatch[1].trim() : 'unknown'
      };
    } catch (error) {
      this.logger.error('获取Redis统计信息失败', { error });
      return { keyCount: 0, memoryUsage: 'unknown' };
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.redis.ping();
      return true;
    } catch (error) {
      return false;
    }
  }
}

// 增强功能接口
interface EnhancedCacheOptions {
  quality?: number;
  tags?: string[];
  priority?: number;
  storeInVector?: boolean;
  semanticSearch?: boolean;
}

// 使用统一的CacheMetrics接口，不再重复定义

interface CallTypeMetrics extends CacheMetrics {
  callType: string;
}

interface HourlyStats {
  hour: string;
  requests: number;
  hits: number;
  misses: number;
  avgResponseTime: number;
  costSaved: number;
  costSpent: number;
}

interface PredictedCall {
  callType: string;
  marketContext: any;
  probability: number;
  priority: number;
}

/**
 * 增强的多层次缓存服务
 * 集成了语义缓存、性能监控、智能预热等功能
 */
@injectable()
export class MultiTierCacheService {
  private readonly l1Cache: L1MemoryCache;
  private readonly l2Cache: L2RedisCache;
  private readonly stats: MultiTierCacheStats;
  private readonly config: CacheStrategyConfig;
  private readonly memoryVectorStore: Map<string, { vector: number[], metadata: any }> = new Map();
  
  // 增强功能：性能监控
  private performanceMetrics: {
    byCallType: Map<string, CallTypeMetrics>;
    hourlyStats: Map<string, HourlyStats>;
    monitoringStartTime: Date;
  } = {
    byCallType: new Map(),
    hourlyStats: new Map(),
    monitoringStartTime: new Date()
  };
  
  // 增强功能：预热统计
  private warmupStats = {
    totalWarmups: 0,
    successfulWarmups: 0,
    failedWarmups: 0,
    lastWarmupTime: null as Date | null
  };

  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.Shared.EnhancedVectorService) private readonly vectorService: EnhancedVectorService,
    @inject(TYPES.Shared.UnifiedEnvironmentManager) private readonly envManager: UnifiedEnvironmentManager,
    @inject(TYPES.Shared.VectorDatabaseService) private readonly vectorDatabaseService: VectorDatabaseService,
    private readonly aiCallLogService?: any
  ) {
    // 内存向量存储，用于替代mock向量数据库
    this.memoryVectorStore = new Map();
    this.config = this.getDefaultConfig();
    this.l1Cache = new L1MemoryCache(
      this.config.l1.maxSize,
      this.config.l1.ttl,
      this.config.l1.evictionPolicy
    );
    this.l2Cache = new L2RedisCache(this.logger, this.envManager, this.config.l2.keyPrefix, this.config.l2.compressionEnabled);
    this.stats = this.initializeStats();
    
    // 启动性能监控定时器
    this.startPerformanceMonitoring();
  }

  private getDefaultConfig(): CacheStrategyConfig {
    // 从统一配置管理器获取配置，如果不可用则使用默认值
    try {
      // 暂时移除容器依赖，直接使用默认配置
      const configManager = null;
      if (configManager) {
        return {
          l1: {
            maxSize: configManager.get('cache.l1.maxSize', 1000),
            ttl: configManager.get('cache.l1.ttl', 300000),
            evictionPolicy: configManager.get('cache.l1.evictionPolicy', 'LRU')
          },
          l2: {
            ttl: configManager.get('cache.l2.ttl', 3600000),
            compressionEnabled: configManager.get('cache.l2.compressionEnabled', true),
            keyPrefix: configManager.get('cache.l2.keyPrefix', 'ai-cache:')
          },
          l3: {
            similarityThreshold: configManager.get('cache.l3.similarityThreshold', 0.85),
            maxResults: configManager.get('cache.l3.maxResults', 10),
            qualityThreshold: configManager.get('cache.l3.qualityThreshold', 0.7)
          }
        };
      }
    } catch (error) {
      this.logger.warn('无法从统一配置管理器获取缓存配置，使用默认值', { error });
    }

    // 默认配置
    return {
      l1: {
        maxSize: 1000,
        ttl: 300000, // 5分钟
        evictionPolicy: 'LRU'
      },
      l2: {
        ttl: 3600000, // 1小时
        compressionEnabled: true,
        keyPrefix: 'ai-cache:'
      },
      l3: {
        similarityThreshold: 0.85,
        maxResults: 10,
        qualityThreshold: 0.7
      }
    };
  }

  private initializeStats(): MultiTierCacheStats {
    return {
      // CacheStats 基础属性
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      size: 0,
      memoryUsage: 0,
      evictionCount: 0,
      totalRequests: 0,
      totalKeys: 0,
      missRate: 0,
      diskUsage: 0,
      // MultiTierCacheStats 扩展属性
      l1: { hits: 0, misses: 0, size: 0, hitRate: 0 },
      l2: { hits: 0, misses: 0, hitRate: 0 },
      l3: { hits: 0, misses: 0, hitRate: 0 },
      overall: { totalHits: 0, totalMisses: 0, overallHitRate: 0 }
    };
  }

  /**
   * 增强的智能缓存获取
   */
  async get<T>(key: string, options?: {
    callType?: string;
    marketContext?: any;
    semanticSearch?: boolean;
  }): Promise<T | null> {
    const startTime = Date.now();
    const callType = options?.callType || 'unknown';
    
    try {
      // L1: 内存缓存
      const l1Result = this.l1Cache.get(key);
      if (l1Result !== null) {
        this.stats.l1.hits++;
        this.recordCacheHit(callType, Date.now() - startTime, 0.01);
        this.updateOverallStats();
        this.logger.debug('L1缓存命中', { key, callType });
        return l1Result;
      }
      this.stats.l1.misses++;

      // L2: Redis缓存
      const l2Result = await this.l2Cache.get<T>(key);
      if (l2Result !== null) {
        this.stats.l2.hits++;
        // 回写到L1缓存
        this.l1Cache.set(key, l2Result);
        this.recordCacheHit(callType, Date.now() - startTime, 0.005);
        this.updateOverallStats();
        this.logger.debug('L2缓存命中', { key, callType });
        return l2Result;
      }
      this.stats.l2.misses++;

      // L3: 向量数据库语义搜索（如果启用）
      if (options?.semanticSearch !== false) {
        const l3Result = await this.semanticSearch<T>(key);
        if (l3Result !== null) {
          this.stats.l3.hits++;
          // 回写到L1和L2缓存
          this.l1Cache.set(key, l3Result);
          await this.l2Cache.set(key, l3Result);
          this.recordCacheHit(callType, Date.now() - startTime, 0.002);
          this.updateOverallStats();
          this.logger.debug('L3语义缓存命中', { key, callType });
          return l3Result;
        }
      }
      this.stats.l3.misses++;

      this.recordCacheMiss(callType, Date.now() - startTime);
      this.updateOverallStats();
      return null;
    } catch (error) {
      this.recordCacheMiss(callType, Date.now() - startTime);
      this.logger.error('多层缓存获取失败', { error, key, callType });
      return null;
    }
  }

  /**
   * 增强的智能缓存设置
   */
  async set<T>(key: string, value: T, options?: EnhancedCacheOptions & {
    ttl?: number;
    callType?: string;
  }): Promise<void> {
    try {
      const ttl = options?.ttl || this.config.l1.ttl;
      const quality = options?.quality || 1.0;
      const storeInVector = options?.storeInVector ?? true;
      const callType = options?.callType || 'unknown';

      // 存储到L1缓存
      this.l1Cache.set(key, value, ttl);
      // 存储到L2缓存
      await this.l2Cache.set(key, value, ttl);

      // 存储到L3向量数据库（如果启用）
      if (storeInVector && typeof value === 'string') {
        await this.storeInVectorDatabase(key, value as string, quality, options?.tags);
      }

      this.logger.debug('多层缓存设置成功', { key, quality, storeInVector, callType });
    } catch (error) {
      this.logger.error('多层缓存设置失败', { error, key });
      throw error;
    }
  }

  /**
   * 语义搜索 - 使用向量数据库实现
   */
  private async semanticSearch<T>(query: string): Promise<T | null> {
    try {
      // 生成查询向量
      const queryEmbedding = await this.vectorService.generateIntelligentEmbedding(query);

      // 使用向量数据库搜索相似的缓存项
      const searchResults = await this.vectorDatabaseService.performVectorSearch(
        queryEmbedding.vector,
        {
          topK: this.config.l3?.maxResults || 10,
          similarityThreshold: this.config.l3?.similarityThreshold || 0.8
        }
      );

      // 如果向量数据库搜索失败，回退到内存向量搜索
      if (searchResults.length === 0) {
        this.logger.debug('向量数据库搜索未找到匹配，尝试内存向量搜索', { query });
        
        // 在内存中搜索相似的缓存项作为备份
        const memoryResults = this.searchInMemoryVectors(queryEmbedding.vector, {
          topK: this.config.l3?.maxResults || 10,
          similarityThreshold: this.config.l3?.similarityThreshold || 0.8
        });
        
        if (memoryResults.length > 0) {
          const bestMemoryMatch = memoryResults[0];
          this.logger.debug('内存向量搜索找到匹配', {
            query,
            similarity: bestMemoryMatch.score,
            cacheKey: bestMemoryMatch.metadata?.cacheKey
          });
          
          // 尝试从Redis获取完整数据
          if (bestMemoryMatch.metadata?.cacheKey) {
            const cachedData = await this.l2Cache.get(bestMemoryMatch.metadata.cacheKey);
            if (cachedData) {
              return cachedData as T;
            }
          }
        }
        
        return null;
      }

      // 处理向量数据库搜索结果
      const bestMatch = searchResults[0];
      this.logger.debug('向量数据库搜索找到匹配', {
        query,
        similarity: bestMatch.score,
        cacheKey: bestMatch.metadata?.cacheKey
      });

      // 尝试从Redis获取完整数据
      if (bestMatch.metadata?.cacheKey) {
        const cachedData = await this.l2Cache.get(bestMatch.metadata.cacheKey);
        if (cachedData) {
          return cachedData as T;
        }
      }

      return null;
    } catch (error) {
      this.logger.error('语义搜索失败', { error, query });
      
      // 出错时尝试使用内存向量搜索作为备份
      try {
        const queryEmbedding = await this.vectorService.generateIntelligentEmbedding(query);
        const memoryResults = this.searchInMemoryVectors(queryEmbedding.vector, {
          topK: this.config.l3?.maxResults || 10,
          similarityThreshold: this.config.l3?.similarityThreshold || 0.8
        });
        
        if (memoryResults.length > 0 && memoryResults[0].metadata?.cacheKey) {
          const cachedData = await this.l2Cache.get(memoryResults[0].metadata.cacheKey);
          if (cachedData) {
            this.logger.debug('向量数据库搜索失败，使用内存向量搜索备份成功', { query });
            return cachedData as T;
          }
        }
      } catch (backupError) {
        this.logger.error('备份内存向量搜索也失败', { error: backupError, query });
      }
      
      return null;
    }
  }

  /**
   * 存储到向量数据库
   */
  private async storeInVectorDatabase(
    key: string,
    text: string,
    quality: number,
    tags?: string[]
  ): Promise<void> {
    try {
      const embedding = await this.vectorService.generateIntelligentEmbedding(text);

      // 存储到内存向量存储中（作为备份）
      this.memoryVectorStore.set(`cache-${key}`, {
        vector: embedding.vector,
        metadata: {
          text,
          hash: embedding.hash,
          domain: embedding.metadata.domain,
          timestamp: new Date(),
          quality,
          cacheKey: key,
          tags: tags || [],
          complexity: embedding.metadata.complexity
        }
      });

      // 存储到向量数据库
      await this.vectorDatabaseService.storeVectors([{
        id: `cache-${key}`,
        vector: embedding.vector,
        metadata: {
          text,
          hash: embedding.hash,
          domain: embedding.metadata.domain,
          timestamp: new Date(),
          quality,
          cacheKey: key,
          tags: tags || [],
          complexity: embedding.metadata.complexity
        }
      }]);

      this.logger.debug('向量已存储到向量数据库', { key, vectorDimension: embedding.vector.length });
    } catch (error) {
      this.logger.error('向量存储失败', { error, key });
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 删除缓存项
   */
  async delete(key: string): Promise<void> {
    try {
      // 从所有层级删除
      this.l1Cache.delete(key);
      await this.l2Cache.delete(key);

      // 从向量数据库删除
      try {
        await this.vectorDatabaseService.deleteVectors([`cache-${key}`]);
        this.logger.debug('向量已从向量数据库删除', { key });
      } catch (vectorError) {
        this.logger.error('从向量数据库删除失败', { error: vectorError, key });
      }
      
      // 从内存向量存储中删除（备份）
      this.memoryVectorStore.delete(`cache-${key}`);

      this.logger.debug('多层缓存删除成功', { key });
    } catch (error) {
      this.logger.error('多层缓存删除失败', { error, key });
      throw error;
    }
  }

  /**
   * 清理所有缓存
   */
  async clear(): Promise<void> {
    try {
      this.l1Cache.clear();
      await this.l2Cache.clear();

      this.logger.info('多层缓存清理完成');
    } catch (error) {
      this.logger.error('多层缓存清理失败', { error });
      throw error;
    }
  }

  /**
   * 预热缓存
   */
  async warmup(items: Array<{ key: string; value: any; quality?: number }>): Promise<void> {
    try {
      this.logger.info('开始缓存预热', { count: items.length });

      for (const item of items) {
        await this.set(item.key, item.value, {
          quality: item.quality || 1.0,
          storeInVector: true
        });
      }

      this.logger.info('缓存预热完成', { count: items.length });
    } catch (error) {
      this.logger.error('缓存预热失败', { error });
      throw error;
    }
  }

  /**
   * 智能预加载
   */
  async intelligentPreload(patterns: string[]): Promise<void> {
    try {
      this.logger.info('开始智能预加载', { patterns });
      let preloadedCount = 0;

      for (const pattern of patterns) {
        try {
          // 生成模式的向量嵌入
          const embedding = await this.vectorService.generateIntelligentEmbedding(pattern);
          
          // 使用向量数据库搜索相似模式
          const searchResults = await this.vectorDatabaseService.performVectorSearch(
            embedding.vector,
            {
              topK: 10, // 获取前10个最相似的结果
              similarityThreshold: 0.75, // 设置相似度阈值
              filter: {
                path: ['metadata', 'domain'],
                operator: 'equals',
                value: embedding.metadata.domain
              }
            }
          );
          
          if (searchResults.length > 0) {
            // 预加载找到的相似缓存项
            for (const result of searchResults) {
              const cacheKey = result.metadata?.cacheKey;
              if (cacheKey) {
                // 从L2缓存获取值并加载到L1缓存
                const value = await this.l2Cache.get(cacheKey);
                if (value !== null) {
                  this.l1Cache.set(cacheKey, value, this.config.l1.ttl);
                  preloadedCount++;
                }
              }
            }
            this.logger.debug('智能预加载模式完成', { pattern, resultsFound: searchResults.length });
          } else {
            this.logger.debug('未找到相似模式进行预加载', { pattern });
          }
        } catch (patternError) {
          this.logger.error('处理预加载模式失败', { error: patternError, pattern });
        }
      }

      this.logger.info('智能预加载完成', { preloadedCount });
    } catch (error) {
      this.logger.error('智能预加载失败', { error });
    }
  }

  /**
   * 自适应失效策略
   */
  async adaptiveEviction(): Promise<void> {
    try {
      const l1Stats = this.l1Cache.getStats();

      // 基于访问模式调整缓存策略
      if (this.stats.l1.hitRate < 0.5) {
        // 命中率低，增加缓存大小或调整TTL
        this.logger.info('L1缓存命中率低，考虑调整策略', { hitRate: this.stats.l1.hitRate });
      }

      // 清理低质量的缓存项
      for (const item of l1Stats.items) {
        if (item.metadata.quality < 0.3 && item.accessCount < 2) {
          this.l1Cache.delete(item.key);
          this.logger.debug('清理低质量缓存项', { key: item.key, quality: item.metadata.quality });
        }
      }
    } catch (error) {
      this.logger.error('自适应失效策略执行失败', { error });
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getStats(): Promise<MultiTierCacheStats> {
    try {
      // 更新L1统计
      const l1Stats = this.l1Cache.getStats();
      this.stats.l1.size = l1Stats.size;
      this.stats.l1.hitRate = this.stats.l1.hits / (this.stats.l1.hits + this.stats.l1.misses) || 0;

      // 更新L2统计
      this.stats.l2.hitRate = this.stats.l2.hits / (this.stats.l2.hits + this.stats.l2.misses) || 0;

      // 更新L3统计
      this.stats.l3.hitRate = this.stats.l3.hits / (this.stats.l3.hits + this.stats.l3.misses) || 0;

      // 更新总体统计
      this.updateOverallStats();

      return { ...this.stats };
    } catch (error) {
      this.logger.error('获取缓存统计失败', { error });
      return this.stats;
    }
  }

  private updateOverallStats(): void {
    this.stats.overall.totalHits = this.stats.l1.hits + this.stats.l2.hits + this.stats.l3.hits;
    this.stats.overall.totalMisses = this.stats.l1.misses + this.stats.l2.misses + this.stats.l3.misses;
    this.stats.overall.overallHitRate =
      this.stats.overall.totalHits / (this.stats.overall.totalHits + this.stats.overall.totalMisses) || 0;
  }

  /**
   * 统一缓存查找接口 - 兼容IntelligentCacheManager接口
   */
  async tryGetFromCache(
    prompt: string,
    marketContext: any,
    callType: string,
    options: any = {}
  ): Promise<any | null> {
    try {
      // 生成缓存键
      const cacheKey = this.generateCacheKey(prompt, marketContext, callType);

      // 尝试从缓存获取
      const result = await this.get(cacheKey, { callType });

      if (result) {
        this.logger.debug('缓存命中', { callType, cacheKey });
        return {
          result,
          sourceId: cacheKey,
          similarity: 1.0,
          timestamp: new Date()
        };
      }

      return null;
    } catch (error) {
      this.logger.error('缓存查找失败', { error, callType });
      return null;
    }
  }

  /**
   * 记录AI调用并更新缓存 - 兼容IntelligentCacheManager接口
   */
  async recordAICall(request: any, cacheResult?: any): Promise<void> {
    try {
      // 如果不是缓存命中，将结果存储到缓存
      if (!cacheResult && request.prompt && request.marketContext) {
        const cacheKey = this.generateCacheKey(
          request.prompt,
          request.marketContext,
          request.callType
        );

        await this.set(cacheKey, request.responseData, {
          ttl: 300000, // 5分钟TTL
          callType: request.callType
        });

        this.logger.debug('AI调用结果已缓存', {
          callType: request.callType,
          cacheKey
        });
      }

      // 记录AI调用日志（如果有日志服务）
      if (this.aiCallLogService) {
        await this.aiCallLogService.logAICall(request);
      }
    } catch (error) {
      this.logger.error('记录AI调用失败', { error, callType: request.callType });
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(prompt: string, marketContext: any, callType: string): string {
    const contextStr = JSON.stringify({
      symbol: marketContext.symbol,
      timeframe: marketContext.timeframe,
      price: Math.round(marketContext.currentPrice || 0),
      callType
    });

    // 使用简单的哈希生成缓存键
    const hash = this.simpleHash(prompt + contextStr);
    return `ai_call:${callType}:${hash}`;
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    l1: boolean;
    l2: boolean;
    l3: boolean;
    overall: boolean;
  }> {
    const l1Healthy = this.l1Cache.size() >= 0; // L1总是健康的
    const l2Healthy = await this.l2Cache.isAvailable();

    const l3Healthy = false; // L3已禁用

    return {
      l1: l1Healthy,
      l2: l2Healthy,
      l3: l3Healthy,
      overall: l1Healthy && l2Healthy
    };
  }

  // ==================== 增强功能方法 ====================

  /**
   * 记录缓存命中（性能监控功能）
   */
  private recordCacheHit(callType: string, responseTimeMs: number, costSaved: number = 0): void {
    this.updateCallTypeMetrics(callType, true, responseTimeMs, costSaved, 0);
    this.updateHourlyStats(true, responseTimeMs, costSaved, 0);
  }

  /**
   * 记录缓存未命中（性能监控功能）
   */
  private recordCacheMiss(callType: string, responseTimeMs: number, costSpent: number = 0): void {
    this.updateCallTypeMetrics(callType, false, responseTimeMs, 0, costSpent);
    this.updateHourlyStats(false, responseTimeMs, 0, costSpent);
  }

  /**
   * 更新按调用类型的性能指标
   */
  private updateCallTypeMetrics(
    callType: string,
    isHit: boolean,
    responseTimeMs: number,
    costSaved: number,
    costSpent: number
  ): void {
    if (!this.performanceMetrics.byCallType.has(callType)) {
      this.performanceMetrics.byCallType.set(callType, {
        callType,
        totalRequests: 0,
        cacheHits: 0,
        cacheMisses: 0,
        totalResponseTime: 0,
        cacheHitResponseTime: 0,
        cacheMissResponseTime: 0,
        totalCostSaved: 0,
        totalCostSpent: 0,
        hitRate: 0,
        avgResponseTime: 0
      });
    }

    const metrics = this.performanceMetrics.byCallType.get(callType)!;
    metrics.totalRequests++;
    metrics.totalResponseTime += responseTimeMs;

    if (isHit) {
      metrics.cacheHits++;
      metrics.cacheHitResponseTime += responseTimeMs;
      metrics.totalCostSaved += costSaved;
    } else {
      metrics.cacheMisses++;
      metrics.cacheMissResponseTime += responseTimeMs;
      metrics.totalCostSpent += costSpent;
    }

    // 更新计算字段
    metrics.hitRate = metrics.totalRequests > 0 ? metrics.cacheHits / metrics.totalRequests : 0;
    metrics.avgResponseTime = metrics.totalRequests > 0 ? metrics.totalResponseTime / metrics.totalRequests : 0;
  }

  /**
   * 更新按小时的性能统计
   */
  private updateHourlyStats(
    isHit: boolean,
    responseTimeMs: number,
    costSaved: number,
    costSpent: number
  ): void {
    const hour = new Date().toISOString().slice(0, 13); // YYYY-MM-DDTHH

    if (!this.performanceMetrics.hourlyStats.has(hour)) {
      this.performanceMetrics.hourlyStats.set(hour, {
        hour,
        requests: 0,
        hits: 0,
        misses: 0,
        avgResponseTime: 0,
        costSaved: 0,
        costSpent: 0
      });
    }

    const stats = this.performanceMetrics.hourlyStats.get(hour)!;
    const oldRequests = stats.requests;
    stats.requests++;

    // 更新平均响应时间
    stats.avgResponseTime = (stats.avgResponseTime * oldRequests + responseTimeMs) / stats.requests;

    if (isHit) {
      stats.hits++;
      stats.costSaved += costSaved;
    } else {
      stats.misses++;
      stats.costSpent += costSpent;
    }
  }

  /**
   * 启动性能监控定时器
   */
  private startPerformanceMonitoring(): void {
    // 每小时清理旧的统计数据
    setInterval(() => {
      const cutoff = new Date();
      cutoff.setHours(cutoff.getHours() - 24); // 保留24小时数据
      const cutoffStr = cutoff.toISOString().slice(0, 13);
      
      for (const [hour] of this.performanceMetrics.hourlyStats) {
        if (hour < cutoffStr) {
          this.performanceMetrics.hourlyStats.delete(hour);
        }
      }
    }, 3600000); // 每小时执行一次
  }

  /**
   * 获取性能指标
   */
  async getPerformanceMetrics(): Promise<{
    byCallType: Record<string, CallTypeMetrics>;
    hourlyStats: HourlyStats[];
    summary: {
      totalRequests: number;
      totalHits: number;
      totalMisses: number;
      overallHitRate: number;
      totalCostSaved: number;
      totalCostSpent: number;
    };
  }> {
    const byCallType: Record<string, CallTypeMetrics> = {};
    for (const [callType, metrics] of this.performanceMetrics.byCallType) {
      byCallType[callType] = { ...metrics };
    }

    const hourlyStats = Array.from(this.performanceMetrics.hourlyStats.values())
      .sort((a, b) => a.hour.localeCompare(b.hour));

    // 计算总体统计
    let totalRequests = 0;
    let totalHits = 0;
    let totalMisses = 0;
    let totalCostSaved = 0;
    let totalCostSpent = 0;

    for (const metrics of this.performanceMetrics.byCallType.values()) {
      totalRequests += metrics.totalRequests;
      totalHits += metrics.cacheHits;
      totalMisses += metrics.cacheMisses;
      totalCostSaved += metrics.totalCostSaved;
      totalCostSpent += metrics.totalCostSpent;
    }

    return {
      byCallType,
      hourlyStats,
      summary: {
        totalRequests,
        totalHits,
        totalMisses,
        overallHitRate: totalRequests > 0 ? totalHits / totalRequests : 0,
        totalCostSaved,
        totalCostSpent
      }
    };
  }

  /**
   * 智能预测下一个查询（预热功能）
   */
  async predictNextQueries(currentQuery: string, marketContext: any, limit: number = 5): Promise<PredictedCall[]> {
    try {
      // 生成查询的向量嵌入
      const embedding = await this.vectorService.generateIntelligentEmbedding(currentQuery);
      
      // 使用向量数据库搜索相似查询
      const searchResults = await this.vectorDatabaseService.performVectorSearch(
        embedding.vector,
        {
          topK: limit * 2, // 获取更多结果，以便后续过滤
          similarityThreshold: 0.7, // 设置相似度阈值
          filter: {
            path: ['metadata', 'domain'],
            operator: 'equals',
            value: embedding.metadata.domain
          }
        }
      );
      
      if (searchResults.length === 0) {
        this.logger.debug('未找到相似查询', { currentQuery });
        return [];
      }
      
      // 转换为预测结果并过滤掉当前查询
      const predictions: PredictedCall[] = searchResults
        .filter(item => {
          // 确保不返回当前查询本身
          const itemText = item.metadata?.text || '';
          return itemText !== currentQuery;
        })
        .map(item => ({
          callType: item.metadata?.text || '',
          marketContext: marketContext,
          probability: item.score || 0.5,
          priority: item.metadata?.quality || 0
        }))
        .slice(0, limit); // 限制返回数量
      
      this.logger.debug('预测下一个查询成功', { currentQuery, predictionsCount: predictions.length });
      return predictions;
    } catch (error) {
      this.logger.error('预测下一个查询失败', { error, currentQuery });
      return [];
    }
  }

  /**
   * 智能预热缓存
   */
  async intelligentWarmup(patterns: string[], marketContext: any): Promise<void> {
    this.warmupStats.totalWarmups++;
    
    try {
      this.logger.info('开始智能预热缓存', { patterns: patterns.length });
      
      for (const pattern of patterns) {
        try {
          // 预测相关查询
          const predictions = await this.predictNextQueries(pattern, marketContext, 10);
          
          // 预加载高概率查询
          for (const prediction of predictions) {
            if (prediction.probability > 0.7) {
              // 检查是否已缓存
              const cached = await this.get(pattern, { 
                callType: prediction.callType,
                semanticSearch: true 
              });
              
              if (!cached) {
                // 这里可以触发实际的AI调用来预热缓存
                // 但为了避免不必要的成本，我们只记录预热意图
                this.logger.debug('标记预热目标', {
                  pattern,
                  callType: prediction.callType,
                  probability: prediction.probability
                });
              }
            }
          }
          
          this.warmupStats.successfulWarmups++;
        } catch (error) {
          this.warmupStats.failedWarmups++;
          this.logger.warn('预热模式失败', { pattern, error });
        }
      }
      
      this.warmupStats.lastWarmupTime = new Date();
      this.logger.info('智能预热完成', {
        total: this.warmupStats.totalWarmups,
        successful: this.warmupStats.successfulWarmups,
        failed: this.warmupStats.failedWarmups
      });
    } catch (error) {
      this.logger.error('智能预热失败', { error });
      throw error;
    }
  }

  /**
   * 获取预热统计
   */
  getWarmupStats() {
    return { ...this.warmupStats };
  }

  /**
   * 批量获取缓存项
   */
  async getBatch<T>(keys: string[], options?: {
    callType?: string;
    semanticSearch?: boolean;
  }): Promise<Map<string, T>> {
    const results = new Map<string, T>();
    const promises = keys.map(async (key) => {
      const value = await this.get<T>(key, options);
      if (value !== null) {
        results.set(key, value);
      }
    });
    
    await Promise.all(promises);
    return results;
  }

  /**
   * 检查缓存项是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      // 首先检查L1缓存
      const l1Result = this.l1Cache.get(key);
      if (l1Result !== null) {
        return true;
      }

      // 然后检查L2缓存
      const l2Exists = await this.l2Cache.exists(key);
      if (l2Exists) {
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error('检查缓存存在性失败', { error, key });
      return false;
    }
  }

  /**
   * 批量设置缓存项
   */
  async setBatch<T>(items: Array<{ key: string; value: T; options?: EnhancedCacheOptions }>): Promise<void> {
    const promises = items.map(({ key, value, options }) => 
      this.set(key, value, options)
    );
    
    await Promise.all(promises);
  }

  /**
   * 按标签删除缓存项
   */
  async deleteByTags(tags: string[]): Promise<number> {
    let deletedCount = 0;
    
    try {
      // 构建过滤条件，查找包含任一标签的缓存项
      const filter = {
        path: ['metadata', 'tags'],
        operator: 'containsAny',
        value: tags
      };
      
      // 从向量数据库中搜索匹配标签的项
      const searchResults = await this.vectorDatabaseService.performVectorSearch(
        [], // 空向量，仅使用过滤器搜索
        {
          filter,
          topK: 1000, // 设置较大的值以获取所有匹配项
          similarityThreshold: 0 // 不使用相似度过滤
        }
      );
      
      if (searchResults.length > 0) {
        // 提取缓存键并删除
        const cacheKeys = searchResults.map(item => {
          // 从向量ID中提取缓存键（移除'cache-'前缀）
          const cacheKey = item.id.startsWith('cache-') ? item.id.substring(6) : item.id;
          return cacheKey;
        });
        
        // 批量删除缓存项
        const deletePromises = cacheKeys.map(key => this.delete(key));
        await Promise.all(deletePromises);
        
        deletedCount = cacheKeys.length;
        this.logger.debug('按标签删除缓存成功', { tags, deletedCount });
      } else {
        this.logger.debug('未找到匹配标签的缓存项', { tags });
      }
    } catch (error) {
      this.logger.error('按标签删除缓存失败', { error, tags });
    }
    
    return deletedCount;
  }

  /**
   * 获取缓存项的元数据
   */
  async getMetadata(key: string): Promise<any> {
    try {
      // 尝试从L2缓存获取完整项信息
      const fullKey = this.l2Cache['keyPrefix'] + key;
      const redis = this.l2Cache['redis'];
      const value = await redis.get(fullKey);
      
      if (value) {
        const parsed = JSON.parse(value);
        return parsed.metadata;
      }
      
      return null;
    } catch (error) {
      this.logger.error('获取缓存元数据失败', { error, key });
      return null;
    }
  }

  /**
   * 内存向量搜索实现
   */
  private searchInMemoryVectors(queryVector: number[], options: {
    topK: number;
    similarityThreshold: number;
  }): Array<{ score: number; metadata: any }> {
    const results: Array<{ score: number; metadata: any }> = [];

    // 遍历内存向量存储
    for (const [key, item] of this.memoryVectorStore.entries()) {
      const similarity = this.calculateCosineSimilarity(queryVector, item.vector);

      if (similarity >= options.similarityThreshold) {
        results.push({
          score: similarity,
          metadata: { ...item.metadata, cacheKey: key }
        });
      }
    }

    // 按相似度排序并返回topK结果
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, options.topK);
  }

  /**
   * 计算余弦相似度
   */
  private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }
}
