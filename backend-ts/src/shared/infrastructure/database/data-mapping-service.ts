import { Decimal } from '@prisma/client/runtime/library';

/**
 * 数据映射工具类
 * 提供数据库数据与领域对象之间的映射工具函数
 */
export class DataMappingUtils {
  /**
   * 将Decimal转换为number
   */
  static decimalToNumber(decimal: Decimal | null | undefined): number {
    if (!decimal) return 0;
    return decimal.toNumber();
  }

  /**
   * 将number转换为Decimal
   */
  static numberToDecimal(value: number | null | undefined): Decimal {
    if (value === null || value === undefined) return new Decimal(0);
    return new Decimal(value);
  }

  /**
   * 安全的字符串转换
   */
  static safeString(value: any): string {
    if (value === null || value === undefined) return '';
    return String(value);
  }

  /**
   * 安全的数字转换
   */
  static safeNumber(value: any): number {
    if (value === null || value === undefined) return 0;
    const num = Number(value);
    return isNaN(num) ? 0 : num;
  }

  /**
   * 安全的布尔转换
   */
  static safeBoolean(value: any): boolean {
    if (value === null || value === undefined) return false;
    return Boolean(value);
  }

  /**
   * 安全的日期转换
   */
  static safeDate(value: any): Date {
    if (value === null || value === undefined) return new Date();
    if (value instanceof Date) return value;
    const date = new Date(value);
    return isNaN(date.getTime()) ? new Date() : date;
  }

  /**
   * 映射数组数据
   */
  static mapArray<T, R>(
    items: T[] | null | undefined,
    mapper: (item: T) => R
  ): R[] {
    if (!items || !Array.isArray(items)) return [];
    return items.map(mapper);
  }

  /**
   * 过滤空值
   */
  static filterNulls<T>(items: (T | null | undefined)[]): T[] {
    return items.filter((item): item is T => item !== null && item !== undefined);
  }

  /**
   * 深度克隆对象
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as any;
    if (obj instanceof Array) return obj.map(item => this.deepClone(item)) as any;
    
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = this.deepClone(obj[key]);
      }
    }
    return cloned;
  }

  /**
   * 合并对象
   */
  static merge<T extends object>(target: T, ...sources: Partial<T>[]): T {
    const result = { ...target };
    for (const source of sources) {
      Object.assign(result, source);
    }
    return result;
  }

  /**
   * 提取对象属性
   */
  static pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
    const result = {} as Pick<T, K>;
    for (const key of keys) {
      if (key in obj) {
        result[key] = obj[key];
      }
    }
    return result;
  }

  /**
   * 排除对象属性
   */
  static omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
    const result = { ...obj };
    for (const key of keys) {
      delete result[key];
    }
    return result;
  }
}
