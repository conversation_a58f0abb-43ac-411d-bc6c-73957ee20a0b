import { EventEmitter } from 'events';
import { getLogger, ILogger } from '../config/logging';
import { SystemStateData, SystemStateType, SyncMode } from './cross-system-state-sync';
import { SystemType } from './unified-data-sync-coordinator';
import { StateConflictResolver } from './state-sync/StateConflictResolver';
import { StateSyncScheduler, StateSyncConfig } from './state-sync/StateSyncScheduler';
import { StateDataValidator } from './state-sync/StateDataValidator';
import { DataVersionManager, VersionManagerConfig } from './data-version-manager';

/**
 * 重构后的跨系统状态同步管理器
 * 修复单一职责违规：将原来1159行的巨型服务拆分为多个专门的服务，
 * 此服务作为协调器，负责统一管理和协调各个专门服务
 */
export class RefactoredCrossSystemStateSyncManager extends EventEmitter {
  private readonly logger: ILogger;
  private readonly conflictResolver: StateConflictResolver;
  private readonly syncScheduler: StateSyncScheduler;
  private readonly dataValidator: StateDataValidator;
  private readonly versionManager: DataVersionManager;
  
  private readonly stateStore = new Map<string, SystemStateData>();
  private readonly statistics = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    conflictsResolved: 0,
    lastSyncTime: null as Date | null,
    systemStats: new Map<SystemType, { sent: number; received: number }>()
  };

  private isRunning = false;

  constructor() {
    super();
    this.logger = getLogger('RefactoredCrossSystemStateSyncManager');
    
    // 初始化专门的服务
    const versionManagerConfig: VersionManagerConfig = {
      nodeId: `sync-manager-${Date.now()}`,
      maxVersionHistory: 100,
      conflictResolutionStrategy: 'merge',
      cleanupInterval: 300000, // 5分钟
      maxClockSize: 1000,
      enableCompression: false,
      enableChecksumValidation: true,
      retentionPeriod: 86400000 // 24小时
    };
    this.versionManager = new DataVersionManager(versionManagerConfig);
    this.conflictResolver = new StateConflictResolver(this.versionManager);
    this.syncScheduler = new StateSyncScheduler();
    this.dataValidator = new StateDataValidator();
    
    this.setupEventHandlers();
  }

  /**
   * 启动同步管理器
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('跨系统状态同步管理器已在运行');
      return;
    }

    try {
      this.logger.info('🚀 启动重构后的跨系统状态同步管理器...');
      
      // 启动调度器
      this.syncScheduler.start();
      
      // 初始化默认配置
      this.initializeDefaultConfigs();
      
      this.isRunning = true;
      this.emit('started');
      this.logger.info('✅ 跨系统状态同步管理器启动成功');
    } catch (error) {
      this.logger.error('❌ 跨系统状态同步管理器启动失败:', error);
      throw error;
    }
  }

  /**
   * 停止同步管理器
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.logger.info('🛑 停止跨系统状态同步管理器...');
    
    try {
      // 停止调度器
      this.syncScheduler.stop();
      
      // 处理剩余的冲突
      await this.conflictResolver.processConflictQueue();
      
      this.isRunning = false;
      this.emit('stopped');
      this.logger.info('✅ 跨系统状态同步管理器已停止');
    } catch (error) {
      this.logger.error('❌ 停止跨系统状态同步管理器失败:', error);
      throw error;
    }
  }

  /**
   * 同步状态数据
   */
  async syncStateData(stateData: SystemStateData): Promise<SyncResult> {
    const startTime = Date.now();
    
    try {
      // 验证数据
      const validationResult = this.dataValidator.validateStateData(stateData);
      if (!validationResult.isValid) {
        return {
          success: false,
          stateType: stateData.stateType,
          systemType: stateData.systemType,
          error: `数据验证失败: ${validationResult.errors.join(', ')}`,
          duration: Date.now() - startTime
        };
      }

      // 检查是否存在现有状态
      const stateKey = this.generateStateKey(stateData.stateType, stateData.systemType);
      const existingState = this.stateStore.get(stateKey);

      if (existingState) {
        // 解决冲突
        const { resolved } = await this.conflictResolver.resolveConflict(stateData, existingState);
        this.stateStore.set(stateKey, resolved);
        this.statistics.conflictsResolved++;
      } else {
        // 直接存储新状态
        this.stateStore.set(stateKey, stateData);
      }

      // 更新统计信息
      this.updateStatistics([{
        success: true,
        stateType: stateData.stateType,
        systemType: stateData.systemType,
        duration: Date.now() - startTime
      }]);

      // 发送同步事件
      this.emit('stateDataSynced', {
        stateData,
        timestamp: new Date()
      });

      return {
        success: true,
        stateType: stateData.stateType,
        systemType: stateData.systemType,
        duration: Date.now() - startTime
      };

    } catch (error) {
      this.logger.error('状态数据同步失败', {
        stateType: stateData.stateType,
        systemType: stateData.systemType,
        error
      });

      this.updateStatistics([{
        success: false,
        stateType: stateData.stateType,
        systemType: stateData.systemType,
        error: error.message,
        duration: Date.now() - startTime
      }]);

      return {
        success: false,
        stateType: stateData.stateType,
        systemType: stateData.systemType,
        error: error.message,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * 获取状态数据
   */
  getStateData(stateType: SystemStateType, systemType: SystemType): SystemStateData | null {
    const stateKey = this.generateStateKey(stateType, systemType);
    return this.stateStore.get(stateKey) || null;
  }

  /**
   * 添加同步配置
   */
  addSyncConfig(config: StateSyncConfig): void {
    this.syncScheduler.addSyncConfig(config);
  }

  /**
   * 手动触发同步
   */
  async triggerSync(stateType: SystemStateType): Promise<void> {
    await this.syncScheduler.triggerSync(stateType);
  }

  /**
   * 获取同步统计信息
   */
  getStatistics(): typeof this.statistics {
    return { ...this.statistics };
  }

  /**
   * 获取管理器状态
   */
  getStatus(): {
    isRunning: boolean;
    stateCount: number;
    schedulerStatus: any;
    conflictQueueStatus: any;
  } {
    return {
      isRunning: this.isRunning,
      stateCount: this.stateStore.size,
      schedulerStatus: this.syncScheduler.getStatus(),
      conflictQueueStatus: this.conflictResolver.getConflictQueueStatus()
    };
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    // 监听调度器事件
    this.syncScheduler.on?.('scheduledSyncTriggered', (data) => {
      this.emit('scheduledSyncTriggered', data);
    });
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigs(): void {
    const defaultConfigs: StateSyncConfig[] = [
      {
        stateType: SystemStateType.MARKET_DATA_STATE,
        mode: SyncMode.REAL_TIME,
        interval: 1000,
        enabled: true,
        targetSystems: ['trading', 'risk', 'trend'],
        retryAttempts: 3,
        retryDelay: 1000,
        priority: 'high',
        conflictResolution: 'auto'
      },
      {
        stateType: SystemStateType.TRADING_SIGNAL_STATE,
        mode: SyncMode.REAL_TIME,
        interval: 5000,
        enabled: true,
        targetSystems: ['execution', 'risk'],
        retryAttempts: 5,
        retryDelay: 2000,
        priority: 'critical',
        conflictResolution: 'auto'
      },
      {
        stateType: SystemStateType.RISK_ASSESSMENT_STATE,
        mode: SyncMode.SCHEDULED,
        interval: 30000,
        enabled: true,
        targetSystems: ['trading', 'execution'],
        retryAttempts: 3,
        retryDelay: 5000,
        priority: 'high',
        conflictResolution: 'manual'
      }
    ];

    for (const config of defaultConfigs) {
      this.addSyncConfig(config);
    }

    this.logger.info('✅ 默认同步配置初始化完成');
  }

  /**
   * 生成状态键
   */
  private generateStateKey(stateType: SystemStateType, systemType: SystemType): string {
    return `${stateType}:${systemType}`;
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(syncResults: SyncResult[]): void {
    this.statistics.totalSyncs += syncResults.length;
    this.statistics.successfulSyncs += syncResults.filter(r => r.success).length;
    this.statistics.failedSyncs += syncResults.filter(r => !r.success).length;
    this.statistics.lastSyncTime = new Date();

    // 更新系统统计
    for (const result of syncResults) {
      this.updateSystemStats(result.systemType, 'received');
    }
  }

  /**
   * 更新系统统计
   */
  private updateSystemStats(systemType: SystemType, action: 'sent' | 'received'): void {
    if (!this.statistics.systemStats.has(systemType)) {
      this.statistics.systemStats.set(systemType, { sent: 0, received: 0 });
    }
    this.statistics.systemStats.get(systemType)![action]++;
  }
}

/**
 * 同步结果接口
 */
export interface SyncResult {
  success: boolean;
  stateType: SystemStateType;
  systemType: SystemType;
  error?: string;
  duration: number;
}
