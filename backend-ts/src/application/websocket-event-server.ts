/**
 * WebSocket事件服务器
 * 为数据变更事件广播提供WebSocket服务器支持
 */

import WebSocket from 'ws';
import { Server } from 'http';
import { getLogger, ILogger } from '../config/logging';
import { v4 as uuidv4 } from 'uuid';
import { DataChangeEventBroadcaster, DataChangeEventType, SubscriptionFilter } from './data-change-event-broadcaster';
import { SystemType } from './unified-data-sync-coordinator';

/**
 * 客户端连接信息
 */
interface ClientConnection {
  id: string;
  websocket: WebSocket;
  systemType?: SystemType;
  subscriptions: SubscriptionFilter[];
  connectedAt: Date;
  lastActivity: Date;
  messagesSent: number;
  messagesReceived: number;
}

/**
 * WebSocket消息类型
 */
enum MessageType {
  SUBSCRIBE = 'subscribe',
  UNSUBSCRIBE = 'unsubscribe',
  PING = 'ping',
  PONG = 'pong',
  ERROR = 'error',
  SUCCESS = 'success'
}

/**
 * WebSocket消息格式
 */
interface WebSocketMessage {
  type: MessageType;
  data?: any;
  timestamp?: string;
  id?: string;
}

/**
 * WebSocket事件服务器
 */
export class WebSocketEventServer {
  private readonly logger: ILogger;
  private readonly eventBroadcaster: DataChangeEventBroadcaster;
  private wss: WebSocket.Server | null = null;
  private readonly clients: Map<string, ClientConnection> = new Map();
  
  // 配置
  private readonly config = {
    port: 3001,
    maxConnections: 1000,
    pingInterval: 30000,
    connectionTimeout: 60000,
    maxMessageSize: 1024 * 1024, // 1MB
    enableCompression: true
  };

  // 统计信息
  private readonly statistics = {
    totalConnections: 0,
    activeConnections: 0,
    totalMessages: 0,
    totalErrors: 0,
    startTime: new Date()
  };

  constructor(eventBroadcaster: DataChangeEventBroadcaster, server?: Server) {
    this.eventBroadcaster = eventBroadcaster;
    
    this.logger = getLogger();

    // 如果提供了HTTP服务器，使用它；否则创建独立的WebSocket服务器
    if (server) {
      this.wss = new WebSocket.Server({ 
        server,
        perMessageDeflate: this.config.enableCompression
      });
    } else {
      this.wss = new WebSocket.Server({ 
        port: this.config.port,
        perMessageDeflate: this.config.enableCompression
      });
    }

    this.setupWebSocketServer();
    this.logger.info('WebSocket事件服务器初始化完成', {
      port: this.config.port,
      maxConnections: this.config.maxConnections
    });
  }

  /**
   * 设置WebSocket服务器
   */
  private setupWebSocketServer(): void {
    if (!this.wss) return;

    this.wss.on('connection', (ws: WebSocket, request) => {
      this.handleNewConnection(ws, request);
    });

    this.wss.on('error', (error) => {
      this.logger.error('WebSocket服务器错误', { error: error.message });
      this.statistics.totalErrors++;
    });

    this.logger.info('WebSocket服务器事件处理器设置完成');
  }

  /**
   * 处理新连接
   */
  private handleNewConnection(ws: WebSocket, request: any): void {
    // 检查连接数限制
    if (this.clients.size >= this.config.maxConnections) {
      ws.close(1013, 'Server overloaded');
      this.logger.warn('连接数已达上限，拒绝新连接');
      return;
    }

    const clientId = this.generateClientId();
    const client: ClientConnection = {
      id: clientId,
      websocket: ws,
      subscriptions: [],
      connectedAt: new Date(),
      lastActivity: new Date(),
      messagesSent: 0,
      messagesReceived: 0
    };

    // 从请求头中提取系统类型
    const systemTypeHeader = request.headers['x-system-type'];
    if (systemTypeHeader && Object.values(SystemType).includes(systemTypeHeader)) {
      client.systemType = systemTypeHeader as SystemType;
    }

    this.clients.set(clientId, client);
    this.statistics.totalConnections++;
    this.updateActiveConnectionCount();

    // 设置WebSocket事件处理器
    ws.on('message', (data) => {
      this.handleMessage(clientId, data);
    });

    ws.on('close', (code, reason) => {
      this.handleDisconnection(clientId, code, reason);
    });

    ws.on('error', (error) => {
      this.logger.error('客户端WebSocket错误', {
        clientId,
        error: error.message
      });
      this.handleDisconnection(clientId, 1011, Buffer.from('WebSocket error'));
    });

    ws.on('pong', () => {
      client.lastActivity = new Date();
    });

    // 发送欢迎消息
    this.sendMessage(clientId, {
      type: MessageType.SUCCESS,
      data: {
        message: 'Connected to data change event server',
        clientId,
        serverTime: new Date().toISOString()
      }
    });

    this.logger.info('新客户端连接', {
      clientId,
      systemType: client.systemType,
      totalConnections: this.clients.size
    });
  }

  /**
   * 处理消息
   */
  private handleMessage(clientId: string, data: WebSocket.Data): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      const message: WebSocketMessage = JSON.parse(data.toString());
      client.messagesReceived++;
      client.lastActivity = new Date();
      this.statistics.totalMessages++;

      this.logger.debug('收到客户端消息', {
        clientId,
        messageType: message.type
      });

      switch (message.type) {
        case MessageType.SUBSCRIBE:
          this.handleSubscribe(clientId, message.data);
          break;

        case MessageType.UNSUBSCRIBE:
          this.handleUnsubscribe(clientId, message.data);
          break;

        case MessageType.PING:
          this.sendMessage(clientId, { type: MessageType.PONG });
          break;

        default:
          this.sendError(clientId, `Unknown message type: ${message.type}`);
      }
    } catch (error) {
      this.logger.error('处理消息失败', {
        clientId,
        error: error instanceof Error ? error.message : String(error)
      });
      this.sendError(clientId, 'Invalid message format');
      this.statistics.totalErrors++;
    }
  }

  /**
   * 处理订阅
   */
  private handleSubscribe(clientId: string, subscriptionData: any): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      const filter: SubscriptionFilter = {
        eventTypes: subscriptionData.eventTypes,
        symbols: subscriptionData.symbols,
        timeframes: subscriptionData.timeframes,
        sources: subscriptionData.sources,
        priority: subscriptionData.priority,
        tags: subscriptionData.tags
      };

      client.subscriptions.push(filter);

      // 注册到事件广播器
      this.eventBroadcaster.subscribe(
        client.websocket,
        `WebSocket-${clientId}`,
        filter
      );

      this.sendMessage(clientId, {
        type: MessageType.SUCCESS,
        data: {
          message: 'Subscription added successfully',
          filter
        }
      });

      this.logger.info('客户端添加订阅', {
        clientId,
        filter
      });
    } catch (error) {
      this.sendError(clientId, `Subscription failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理取消订阅
   */
  private handleUnsubscribe(clientId: string, unsubscribeData: any): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      // 🔥 实现逻辑
      client.subscriptions = [];

      // 从事件广播器中移除
      this.eventBroadcaster.unsubscribe(clientId);

      this.sendMessage(clientId, {
        type: MessageType.SUCCESS,
        data: {
          message: 'Unsubscribed successfully'
        }
      });

      this.logger.info('客户端取消订阅', { clientId });
    } catch (error) {
      this.sendError(clientId, `Unsubscribe failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 处理断开连接
   */
  private handleDisconnection(clientId: string, code: number, reason: Buffer): void {
    const client = this.clients.get(clientId);
    if (client) {
      // 从事件广播器中移除
      this.eventBroadcaster.unsubscribe(clientId);
      
      // 移除客户端
      this.clients.delete(clientId);
      this.updateActiveConnectionCount();

      this.logger.info('客户端断开连接', {
        clientId,
        code,
        reason: reason.toString(),
        messagesSent: client.messagesSent,
        messagesReceived: client.messagesReceived,
        connectionDuration: Date.now() - client.connectedAt.getTime()
      });
    }
  }

  /**
   * 发送消息
   */
  private sendMessage(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.websocket.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      const messageWithTimestamp = {
        ...message,
        timestamp: new Date().toISOString(),
        id: this.generateMessageId()
      };

      client.websocket.send(JSON.stringify(messageWithTimestamp));
      client.messagesSent++;
    } catch (error) {
      this.logger.error('发送消息失败', {
        clientId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送错误消息
   */
  private sendError(clientId: string, errorMessage: string): void {
    this.sendMessage(clientId, {
      type: MessageType.ERROR,
      data: { error: errorMessage }
    });
  }

  /**
   * 🔥 生成客户端ID - 使用UUID替代虚假实现
   */
  private generateClientId(): string {
    return `client-${Date.now()}-${uuidv4().replace(/-/g, '').substring(0, 9)}`;
  }

  /**
   * 🔥 生成消息ID - 使用UUID替代虚假实现
   */
  private generateMessageId(): string {
    return `msg-${Date.now()}-${uuidv4().replace(/-/g, '').substring(0, 9)}`;
  }

  /**
   * 更新活跃连接数
   */
  private updateActiveConnectionCount(): void {
    this.statistics.activeConnections = this.clients.size;
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    return {
      ...this.statistics,
      uptime: Date.now() - this.statistics.startTime.getTime(),
      clients: Array.from(this.clients.values()).map(client => ({
        id: client.id,
        systemType: client.systemType,
        connectedAt: client.connectedAt,
        messagesSent: client.messagesSent,
        messagesReceived: client.messagesReceived,
        subscriptionsCount: client.subscriptions.length
      }))
    };
  }

  /**
   * 停止服务器
   */
  async stop(): Promise<void> {
    if (this.wss) {
      // 关闭所有客户端连接
      for (const client of this.clients.values()) {
        client.websocket.close(1000, 'Server shutdown');
      }

      // 关闭服务器
      this.wss.close();
      this.wss = null;
    }

    this.clients.clear();
    this.logger.info('WebSocket事件服务器已停止');
  }
}
