import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import * as cron from 'node-cron';
import { getLogger } from '../config/logging';
import { AdaptiveLearningEngine } from './adaptive-learning-engine';

/**
 * 短期学习服务
 * 15分钟预测，30分钟后验证
 *
 * @deprecated 请使用 UnifiedLearningServiceManager 替代
 * @see ../shared/infrastructure/learning/unified-learning-service-manager.ts
 */
export class ShortTermLearningService extends EventEmitter {
  private readonly prisma: PrismaClient;
  private readonly logger: any;
  private readonly learningEngine: AdaptiveLearningEngine;
  private predictionTask: any | null = null;
  private verificationTask: any | null = null;
  private isRunning = false;

  // 学习配置
  private readonly config = {
    predictionInterval: '*/15 * * * *', // 每15分钟
    verificationInterval: '*/5 * * * *', // 每5分钟检查验证
    predictionHorizon: '15m',
    verificationDelay: '30m',
    verificationDelayMs: 30 * 60 * 1000, // 30分钟
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.logger = getLogger('ShortTermLearning');
    this.learningEngine = new AdaptiveLearningEngine(prisma);
  }

  /**
   * 启动短期学习服务
   */
  async start(): Promise<void> {
    if (this.predictionTask || this.verificationTask) {
      this.logger.warn('短期学习服务已在运行');
      return;
    }

    this.logger.info('🚀 启动短期学习服务...');

    // 立即执行一次预测
    await this.generatePrediction();

    // 设置预测定时任务 - 每15分钟
    this.predictionTask = cron.schedule(this.config.predictionInterval, async () => {
      if (!this.isRunning) {
        await this.generatePrediction();
      }
    });

    // 设置验证定时任务 - 每5分钟检查
    this.verificationTask = cron.schedule(this.config.verificationInterval, async () => {
      await this.verifyPredictions();
    });

    // 启动定时任务
    this.predictionTask.start();
    this.verificationTask.start();

    this.logger.info('✅ 短期学习服务已启动');
    this.logger.info(`📅 预测频率: 每15分钟`);
    this.logger.info(`🔍 验证频率: 每5分钟检查`);
    this.logger.info(`⏱️ 验证延迟: 30分钟`);

    this.emit('started');
  }

  /**
   * 停止短期学习服务
   */
  async stop(): Promise<void> {
    if (this.predictionTask) {
      this.predictionTask.stop();
      this.predictionTask = null;
    }

    if (this.verificationTask) {
      this.verificationTask.stop();
      this.verificationTask = null;
    }

    this.logger.info('📴 短期学习服务已停止');
    this.emit('stopped');
  }

  /**
   * 生成15分钟预测
   */
  private async generatePrediction(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('⚠️ 预测任务正在运行中，跳过本次预测');
      return;
    }

    this.isRunning = true;
    this.logger.info('🔮 开始生成15分钟预测...');

    try {
      // 获取BTC符号
      const symbolRecord = await this.prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) {
        throw new Error('BTC/USDT符号记录不存在');
      }

      // 获取最新价格数据
      const latestPrice = await this.prisma.priceData.findFirst({
        where: { symbolId: symbolRecord.id },
        orderBy: { timestamp: 'desc' }
      });

      if (!latestPrice) {
        throw new Error('没有找到最新价格数据');
      }

      // 获取历史数据用于预测
      const historicalData = await this.prisma.historicalData.findMany({
        where: {
          symbolId: symbolRecord.id,
          timeframe: '15m'
        },
        orderBy: { timestamp: 'desc' },
        take: 100 // 获取最近100条15分钟数据
      });

      if (historicalData.length < 50) {
        throw new Error('历史数据不足，无法生成预测');
      }

      // 生成预测
      const prediction = await this.createPrediction(symbolRecord.id, latestPrice, historicalData);
      
      // 保存预测
      await this.savePrediction(prediction);

      this.logger.info(`✅ 预测生成完成: ${prediction.predictionType} = ${prediction.predictedValue}`);
      this.emit('predictionGenerated', prediction);

    } catch (error) {
      this.logger.error('❌ 预测生成失败:', error instanceof Error ? error.message : String(error));
      this.logger.error('❌ 详细错误:', error);
      this.logger.error('❌ 预测生成详细错误:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 创建预测数据（使用自适应学习的参数）
   */
  private async createPrediction(symbolId: string, latestPrice: any, historicalData: any[]): Promise<any> {
    const now = new Date();
    const targetVerificationTime = new Date(now.getTime() + this.config.verificationDelayMs);

    // 获取市场数据
    const recentPrices = historicalData.slice(0, 20).map(d => parseFloat(d.closePrice.toString()));
    const avgPrice = recentPrices.reduce((sum, price) => sum + price, 0) / recentPrices.length;
    const currentPrice = parseFloat(latestPrice.price.toString());
    const volatility = this.calculateVolatility(recentPrices);

    // 分类市场条件
    const marketCondition = this.classifyMarketCondition(currentPrice, avgPrice, volatility);

    // 获取学习到的参数
    const learnedParams = await this.learningEngine.getOptimizedParameters('price_15m', marketCondition);

    // 使用学习到的参数进行预测
    const priceChange = currentPrice - avgPrice;
    const trendFactor = learnedParams?.trendContinuationFactor || 0.1;
    const volatilityAdjustment = learnedParams?.volatilityAdjustment || 1.0;

    // 应用学习到的趋势延续因子和波动率调整
    const adjustedTrendFactor = trendFactor * volatilityAdjustment * (1 - volatility);
    const predictedValue = currentPrice + (priceChange * adjustedTrendFactor);
    const predictedDirection = predictedValue > currentPrice ? 'UP' : 'DOWN';

    // 使用学习到的置信度计算方法
    const baseConfidence = Math.max(0.3, Math.min(0.9, 1 - volatility));
    const confidenceThreshold = learnedParams?.confidenceThreshold || 0.5;
    const confidence = Math.max(confidenceThreshold, baseConfidence);

    return {
      symbolId,
      predictionType: 'price_15m',
      predictedValue: predictedValue.toFixed(8),
      predictedDirection,
      confidence: confidence.toFixed(2),
      marketContext: {
        currentPrice,
        avgPrice,
        volatility,
        marketCondition,
        trendFactor: adjustedTrendFactor,
        learnedParams,
        dataPoints: recentPrices.length,
        timestamp: now.toISOString()
      },
      modelVersion: 'adaptive_learn_v2.0',
      predictionTimestamp: now,
      targetVerificationTime,
      predictionHorizon: this.config.predictionHorizon,
      verificationDelay: this.config.verificationDelay
    };
  }

  /**
   * 分类市场条件
   */
  private classifyMarketCondition(currentPrice: number, avgPrice: number, volatility: number): string {
    const priceChange = Math.abs(currentPrice - avgPrice) / avgPrice;

    if (volatility > 0.02) return 'HIGH_VOLATILITY';
    if (priceChange > 0.01) return 'TRENDING';
    if (volatility < 0.005) return 'LOW_VOLATILITY';
    return 'NORMAL';
  }

  /**
   * 计算价格波动率
   */
  private calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;

    const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev / mean; // 相对波动率
  }

  /**
   * 保存预测到数据库
   */
  private async savePrediction(prediction: any): Promise<void> {
    await this.prisma.shortCyclePredictions.create({
      data: {
        symbolId: prediction.symbolId,
        predictionType: prediction.predictionType,
        predictedValue: parseFloat(prediction.predictedValue),
        predictedDirection: prediction.predictedDirection,
        confidence: parseFloat(prediction.confidence),
        marketContext: prediction.marketContext,
        modelVersion: prediction.modelVersion,
        predictionTimestamp: prediction.predictionTimestamp,
        targetVerificationTime: prediction.targetVerificationTime,
        predictionHorizon: prediction.predictionHorizon,
        verificationDelay: prediction.verificationDelay,
        isVerified: false
      }
    });
  }

  /**
   * 验证预测结果
   */
  private async verifyPredictions(): Promise<void> {
    try {
      const now = new Date();
      
      // 查找需要验证的预测（目标验证时间已过且未验证）
      const pendingPredictions = await this.prisma.shortCyclePredictions.findMany({
        where: {
          isVerified: false,
          targetVerificationTime: {
            lte: now
          }
        },
        include: {
          Symbols: true
        }
      });

      if (pendingPredictions.length === 0) {
        return;
      }

      this.logger.info(`🔍 开始验证 ${pendingPredictions.length} 个预测...`);

      for (const prediction of pendingPredictions) {
        await this.verifyPrediction(prediction);
      }

      // 更新学习指标
      await this.updateLearningMetrics();

    } catch (error) {
      this.logger.error('❌ 预测验证失败:', (error as Error).message);
    }
  }

  /**
   * 验证单个预测
   */
  private async verifyPrediction(prediction: any): Promise<void> {
    try {
      // 获取验证时间点的实际价格
      const actualPrice = await this.getActualPrice(prediction.symbolId, prediction.targetVerificationTime);
      
      if (!actualPrice) {
        this.logger.warn(`⚠️ 无法获取验证时间的实际价格: ${prediction.id}`);
        return;
      }

      // 计算准确性
      const predictedValue = parseFloat(prediction.predictedValue.toString());
      const actualValue = parseFloat(actualPrice.toString());
      const accuracyScore = this.calculateAccuracy(predictedValue, actualValue, prediction.predictedDirection);

      // 更新预测记录
      await this.prisma.shortCyclePredictions.update({
        where: { id: prediction.id },
        data: {
          isVerified: true,
          verificationTimestamp: new Date(),
          actualValue,
          actualDirection: actualValue > parseFloat(prediction.marketContext.currentPrice) ? 'UP' : 'DOWN',
          accuracyScore
        }
      });

      // 🧠 关键：从验证结果中学习
      await this.learningEngine.learnFromVerification(prediction, actualValue, accuracyScore);

      this.logger.info(`✅ 预测验证完成: ${prediction.id}, 准确率: ${(accuracyScore * 100).toFixed(1)}%`);
      this.emit('predictionVerified', { prediction, accuracyScore });

    } catch (error) {
      this.logger.error(`❌ 预测验证失败 ${prediction.id}:`, (error as Error).message);
    }
  }

  /**
   * 获取指定时间的实际价格
   */
  private async getActualPrice(symbolId: string, targetTime: Date): Promise<number | null> {
    // 查找最接近目标时间的历史数据
    const historicalData = await this.prisma.historicalData.findFirst({
      where: {
        symbolId,
        timeframe: '15m',
        timestamp: {
          gte: new Date(targetTime.getTime() - 15 * 60 * 1000), // 15分钟前
          lte: new Date(targetTime.getTime() + 15 * 60 * 1000)  // 15分钟后
        }
      },
      orderBy: {
        timestamp: 'desc'
      }
    });

    return historicalData ? parseFloat(historicalData.closePrice.toString()) : null;
  }

  /**
   * 计算预测准确性
   */
  private calculateAccuracy(predicted: number, actual: number, predictedDirection: string): number {
    // 方向准确性
    const actualDirection = actual > predicted ? 'UP' : 'DOWN';
    const directionCorrect = predictedDirection === actualDirection ? 1 : 0;

    // 价格准确性（相对误差）
    const relativeError = Math.abs(predicted - actual) / actual;
    const priceAccuracy = Math.max(0, 1 - relativeError);

    // 综合准确性（方向权重60%，价格权重40%）
    return directionCorrect * 0.6 + priceAccuracy * 0.4;
  }

  /**
   * 更新学习指标
   */
  private async updateLearningMetrics(): Promise<void> {
    try {
      const symbolRecord = await this.prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) return;

      // 计算最近24小时的指标
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const recentPredictions = await this.prisma.shortCyclePredictions.findMany({
        where: {
          symbolId: symbolRecord.id,
          isVerified: true,
          verificationTimestamp: {
            gte: oneDayAgo
          }
        }
      });

      if (recentPredictions.length === 0) return;

      const totalPredictions = recentPredictions.length;
      const correctPredictions = recentPredictions.filter((p: any) =>
        parseFloat(p.accuracyScore?.toString() || '0') > 0.5
      ).length;

      const accuracyRate = correctPredictions / totalPredictions;
      const averageConfidence = recentPredictions.reduce((sum: number, p: any) =>
        sum + parseFloat(p.confidence.toString()), 0
      ) / totalPredictions;

      // 保存指标
      await this.prisma.shortCycleMetrics.create({
        data: {
          symbolId: symbolRecord.id,
          timeWindow: '24h',
          predictionType: 'price_15m',
          accuracyRate,
          predictionCount: totalPredictions,
          correctPredictions,
          averageConfidence,
          calculatedAt: new Date()
        }
      });

      this.logger.info(`📊 学习指标更新: 准确率 ${(accuracyRate * 100).toFixed(1)}%, 预测数 ${totalPredictions}`);

    } catch (error) {
      this.logger.error('❌ 学习指标更新失败:', (error as Error).message);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): any {
    return {
      isRunning: this.predictionTask !== null && this.verificationTask !== null,
      config: this.config,
      lastUpdate: new Date().toISOString()
    };
  }
}
