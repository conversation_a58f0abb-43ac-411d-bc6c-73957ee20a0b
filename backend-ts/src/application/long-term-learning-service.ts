import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import * as cron from 'node-cron';
import { getLogger } from '../config/logging';
import { AdaptiveLearningEngine } from './adaptive-learning-engine';

/**
 * 长期学习服务
 * 8小时预测，24小时后验证
 *
 * @deprecated 请使用 UnifiedLearningServiceManager 替代
 * @see ../shared/infrastructure/learning/unified-learning-service-manager.ts
 */
export class LongTermLearningService extends EventEmitter {
  private readonly prisma: PrismaClient;
  private readonly logger: any;
  private readonly learningEngine: AdaptiveLearningEngine;
  private predictionTask: any | null = null;
  private verificationTask: any | null = null;
  private isRunning = false;

  // 学习配置
  private readonly config = {
    predictionInterval: '0 */8 * * *', // 每8小时
    verificationInterval: '0 */4 * * *', // 每4小时检查验证
    predictionHorizon: '8h',
    verificationDelay: '24h',
    verificationDelayMs: 24 * 60 * 60 * 1000, // 24小时
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.logger = getLogger('LongTermLearning');
    this.learningEngine = new AdaptiveLearningEngine(prisma);
  }

  /**
   * 启动长期学习服务
   */
  async start(): Promise<void> {
    if (this.predictionTask || this.verificationTask) {
      this.logger.warn('长期学习服务已在运行');
      return;
    }

    this.logger.info('🚀 启动长期学习服务...');

    // 立即执行一次预测
    await this.generatePrediction();

    // 设置预测定时任务 - 每8小时
    this.predictionTask = cron.schedule(this.config.predictionInterval, async () => {
      if (!this.isRunning) {
        await this.generatePrediction();
      }
    });

    // 设置验证定时任务 - 每4小时检查
    this.verificationTask = cron.schedule(this.config.verificationInterval, async () => {
      await this.verifyPredictions();
    });

    // 启动定时任务
    this.predictionTask.start();
    this.verificationTask.start();

    this.logger.info('✅ 长期学习服务已启动');
    this.logger.info(`📅 预测频率: 每8小时`);
    this.logger.info(`🔍 验证频率: 每4小时检查`);
    this.logger.info(`⏱️ 验证延迟: 24小时`);

    this.emit('started');
  }

  /**
   * 停止长期学习服务
   */
  async stop(): Promise<void> {
    if (this.predictionTask) {
      this.predictionTask.stop();
      this.predictionTask = null;
    }

    if (this.verificationTask) {
      this.verificationTask.stop();
      this.verificationTask = null;
    }

    this.logger.info('📴 长期学习服务已停止');
    this.emit('stopped');
  }

  /**
   * 生成8小时预测
   */
  private async generatePrediction(): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('⚠️ 预测任务正在运行中，跳过本次预测');
      return;
    }

    this.isRunning = true;
    this.logger.info('🔮 开始生成8小时预测...');

    try {
      // 获取BTC符号
      const symbolRecord = await this.prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) {
        throw new Error('BTC/USDT符号记录不存在');
      }

      // 获取最新价格数据
      const latestPrice = await this.prisma.priceData.findFirst({
        where: { symbolId: symbolRecord.id },
        orderBy: { timestamp: 'desc' }
      });

      if (!latestPrice) {
        throw new Error('没有找到最新价格数据');
      }

      // 获取历史数据用于预测（使用8小时数据）
      const historicalData = await this.prisma.historicalData.findMany({
        where: {
          symbolId: symbolRecord.id,
          timeframe: '8h'
        },
        orderBy: { timestamp: 'desc' },
        take: 50 // 获取最近50条8小时数据
      });

      if (historicalData.length < 20) {
        // 如果8小时数据不足，使用4小时数据
        const fallbackData = await this.prisma.historicalData.findMany({
          where: {
            symbolId: symbolRecord.id,
            timeframe: '4h'
          },
          orderBy: { timestamp: 'desc' },
          take: 100
        });

        if (fallbackData.length < 40) {
          throw new Error('历史数据不足，无法生成长期预测');
        }

        // 生成预测
        const prediction = await this.createPrediction(symbolRecord.id, latestPrice, fallbackData, '4h');
        await this.savePrediction(prediction);
      } else {
        // 生成预测
        const prediction = await this.createPrediction(symbolRecord.id, latestPrice, historicalData, '8h');
        await this.savePrediction(prediction);
      }

      this.logger.info(`✅ 长期预测生成完成`);
      this.emit('predictionGenerated');

    } catch (error) {
      this.logger.error('❌ 长期预测生成失败:', error instanceof Error ? error.message : String(error));
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 创建长期预测数据
   */
  private async createPrediction(symbolId: string, latestPrice: any, historicalData: any[], timeframe: string): Promise<any> {
    const now = new Date();
    const targetVerificationTime = new Date(now.getTime() + this.config.verificationDelayMs);

    // 长期趋势分析
    const recentPrices = historicalData.slice(0, 30).map(d => parseFloat(d.closePrice.toString()));
    const currentPrice = parseFloat(latestPrice.price.toString());
    
    // 计算移动平均线
    const shortMA = this.calculateMA(recentPrices.slice(0, 10));
    const longMA = this.calculateMA(recentPrices.slice(0, 20));
    
    // 计算趋势强度
    const trendStrength = this.calculateTrendStrength(recentPrices);
    
    // 预测8小时后的价格（基于趋势分析）
    const trendDirection = shortMA > longMA ? 1 : -1;
    const priceChangePercent = trendStrength * trendDirection * 0.02; // 最大2%变化
    const predictedValue = currentPrice * (1 + priceChangePercent);
    const predictedDirection = predictedValue > currentPrice ? 'UP' : 'DOWN';

    // 计算置信度（基于趋势一致性）
    const trendConsistency = this.calculateTrendConsistency(recentPrices);
    const confidence = Math.max(0.4, Math.min(0.85, trendConsistency));

    return {
      symbolId,
      predictionType: 'trend_8h',
      predictedValue: predictedValue.toFixed(8),
      predictedDirection,
      confidence: confidence.toFixed(2),
      marketContext: {
        currentPrice,
        shortMA,
        longMA,
        trendStrength,
        trendConsistency,
        timeframe,
        dataPoints: recentPrices.length,
        timestamp: now.toISOString()
      },
      modelVersion: 'trend_analysis_v1.0',
      predictionTimestamp: now,
      targetVerificationTime,
      predictionHorizon: this.config.predictionHorizon,
      verificationDelay: this.config.verificationDelay
    };
  }

  /**
   * 计算移动平均线
   */
  private calculateMA(prices: number[]): number {
    return prices.reduce((sum, price) => sum + price, 0) / prices.length;
  }

  /**
   * 计算趋势强度
   */
  private calculateTrendStrength(prices: number[]): number {
    if (prices.length < 3) return 0;

    let upCount = 0;
    let downCount = 0;

    for (let i = 1; i < prices.length; i++) {
      if (prices[i] > prices[i - 1]) {
        upCount++;
      } else if (prices[i] < prices[i - 1]) {
        downCount++;
      }
    }

    const total = upCount + downCount;
    if (total === 0) return 0;

    return Math.abs(upCount - downCount) / total;
  }

  /**
   * 计算趋势一致性
   */
  private calculateTrendConsistency(prices: number[]): number {
    if (prices.length < 5) return 0.5;

    const segments = 5;
    const segmentSize = Math.floor(prices.length / segments);
    const segmentTrends: number[] = [];

    for (let i = 0; i < segments; i++) {
      const start = i * segmentSize;
      const end = Math.min(start + segmentSize, prices.length);
      const segmentPrices = prices.slice(start, end);
      
      if (segmentPrices.length >= 2) {
        const trend = segmentPrices[0] > segmentPrices[segmentPrices.length - 1] ? -1 : 1;
        segmentTrends.push(trend);
      }
    }

    if (segmentTrends.length === 0) return 0.5;

    const positiveCount = segmentTrends.filter(t => t > 0).length;
    const negativeCount = segmentTrends.filter(t => t < 0).length;
    
    return Math.max(positiveCount, negativeCount) / segmentTrends.length;
  }

  /**
   * 保存长期预测到数据库
   */
  private async savePrediction(prediction: any): Promise<void> {
    await this.prisma.longCyclePredictions.create({
      data: {
        symbolId: prediction.symbolId,
        predictionType: prediction.predictionType,
        predictedValue: parseFloat(prediction.predictedValue),
        predictedDirection: prediction.predictedDirection,
        confidence: parseFloat(prediction.confidence),
        marketContext: prediction.marketContext,
        modelVersion: prediction.modelVersion,
        predictionTimestamp: prediction.predictionTimestamp,
        targetVerificationTime: prediction.targetVerificationTime,
        predictionHorizon: prediction.predictionHorizon,
        verificationDelay: prediction.verificationDelay,
        isVerified: false
      }
    });
  }

  /**
   * 验证长期预测结果
   */
  private async verifyPredictions(): Promise<void> {
    try {
      const now = new Date();
      
      // 查找需要验证的预测
      const pendingPredictions = await this.prisma.longCyclePredictions.findMany({
        where: {
          isVerified: false,
          targetVerificationTime: {
            lte: now
          }
        },
        include: {
          Symbols: true
        }
      });

      if (pendingPredictions.length === 0) {
        return;
      }

      this.logger.info(`🔍 开始验证 ${pendingPredictions.length} 个长期预测...`);

      for (const prediction of pendingPredictions) {
        await this.verifyPrediction(prediction);
      }

      // 更新学习指标
      await this.updateLearningMetrics();

    } catch (error) {
      this.logger.error('❌ 长期预测验证失败:', error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 验证单个长期预测
   */
  private async verifyPrediction(prediction: any): Promise<void> {
    try {
      // 获取验证时间点的实际价格
      const actualPrice = await this.getActualPrice(prediction.symbolId, prediction.targetVerificationTime);
      
      if (!actualPrice) {
        this.logger.warn(`⚠️ 无法获取验证时间的实际价格: ${prediction.id}`);
        return;
      }

      // 计算准确性
      const predictedValue = parseFloat(prediction.predictedValue.toString());
      const actualValue = parseFloat(actualPrice.toString());
      const accuracyScore = this.calculateAccuracy(predictedValue, actualValue, prediction.predictedDirection);

      // 更新预测记录
      await this.prisma.longCyclePredictions.update({
        where: { id: prediction.id },
        data: {
          isVerified: true,
          verificationTimestamp: new Date(),
          actualValue,
          actualDirection: actualValue > parseFloat(prediction.marketContext.currentPrice) ? 'UP' : 'DOWN',
          accuracyScore
        }
      });

      // 🧠 关键：从验证结果中学习
      await this.learningEngine.learnFromVerification(prediction, actualValue, accuracyScore);

      this.logger.info(`✅ 长期预测验证完成: ${prediction.id}, 准确率: ${(accuracyScore * 100).toFixed(1)}%`);
      this.emit('predictionVerified', { prediction, accuracyScore });

    } catch (error) {
      this.logger.error(`❌ 长期预测验证失败 ${prediction.id}:`, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 获取指定时间的实际价格
   */
  private async getActualPrice(symbolId: string, targetTime: Date): Promise<number | null> {
    // 查找最接近目标时间的历史数据
    const historicalData = await this.prisma.historicalData.findFirst({
      where: {
        symbolId,
        timeframe: '8h',
        timestamp: {
          gte: new Date(targetTime.getTime() - 4 * 60 * 60 * 1000), // 4小时前
          lte: new Date(targetTime.getTime() + 4 * 60 * 60 * 1000)  // 4小时后
        }
      },
      orderBy: {
        timestamp: 'desc'
      }
    });

    return historicalData ? parseFloat(historicalData.closePrice.toString()) : null;
  }

  /**
   * 计算预测准确性
   */
  private calculateAccuracy(predicted: number, actual: number, predictedDirection: string): number {
    // 方向准确性
    const actualDirection = actual > predicted ? 'UP' : 'DOWN';
    const directionCorrect = predictedDirection === actualDirection ? 1 : 0;

    // 价格准确性（相对误差）
    const relativeError = Math.abs(predicted - actual) / actual;
    const priceAccuracy = Math.max(0, 1 - relativeError * 2); // 长期预测容忍度更高

    // 综合准确性（方向权重70%，价格权重30%）
    return directionCorrect * 0.7 + priceAccuracy * 0.3;
  }

  /**
   * 更新长期学习指标
   */
  private async updateLearningMetrics(): Promise<void> {
    try {
      const symbolRecord = await this.prisma.symbols.findUnique({
        where: { symbol: 'BTC/USDT' }
      });

      if (!symbolRecord) return;

      // 计算最近7天的指标
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      const recentPredictions = await this.prisma.longCyclePredictions.findMany({
        where: {
          symbolId: symbolRecord.id,
          isVerified: true,
          verificationTimestamp: {
            gte: sevenDaysAgo
          }
        }
      });

      if (recentPredictions.length === 0) return;

      const totalPredictions = recentPredictions.length;
      const correctPredictions = recentPredictions.filter((p: any) =>
        parseFloat(p.accuracyScore?.toString() || '0') > 0.6
      ).length;

      const accuracyRate = correctPredictions / totalPredictions;
      const averageConfidence = recentPredictions.reduce((sum: number, p: any) =>
        sum + parseFloat(p.confidence.toString()), 0
      ) / totalPredictions;

      // 保存指标
      await this.prisma.longCycleMetrics.create({
        data: {
          symbolId: symbolRecord.id,
          timeWindow: '7d',
          predictionType: 'trend_8h',
          accuracyRate,
          predictionCount: totalPredictions,
          correctPredictions,
          averageConfidence,
          calculatedAt: new Date()
        }
      });

      this.logger.info(`📊 长期学习指标更新: 准确率 ${(accuracyRate * 100).toFixed(1)}%, 预测数 ${totalPredictions}`);

    } catch (error) {
      this.logger.error('❌ 长期学习指标更新失败:', (error as Error).message);
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): any {
    return {
      isRunning: this.predictionTask !== null && this.verificationTask !== null,
      config: this.config,
      lastUpdate: new Date().toISOString()
    };
  }
}
