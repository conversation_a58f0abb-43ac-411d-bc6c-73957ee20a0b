# ===========================================
# 加密货币监控系统 - 环境配置模板
# ===========================================

# 应用基础配置
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# 数据库配置
DATABASE_URL=postgresql://ai3_user:ai3_password@localhost:5432/ai3_crypto_db
SHADOW_DATABASE_URL=postgresql://ai3_user:ai3_password@localhost:5432/ai3_crypto_db_shadow

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AI模型配置
DEFAULT_LLM_MODEL=gemini-2.0-flash
REDIS_DB=0

# AI模型API密钥 (请替换为您的真实API密钥)
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com

# 交易所API配置
BINANCE_API_URL=https://fapi.binance.com
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
COINBASE_API_URL=https://api.exchange.coinbase.com

# 数据源API配置
TOKEN_METRICS_API_KEY=your_token_metrics_api_key_here

# 测试环境配置
TEST_DATABASE_URL=postgresql://ai3_user:your_password@localhost:5432/ai3_crypto_db_test
TEST_REDIS_URL=redis://localhost:6379/15

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
CREDENTIAL_ENCRYPTION_KEY=your-credential-encryption-key-here

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=9090

# WebSocket配置
WS_CORS_ORIGINS=http://localhost:3001,http://localhost:3000

# 任务队列配置
QUEUE_CONCURRENCY=5
QUEUE_MAX_ATTEMPTS=3

# 缓存配置
CACHE_TTL=300

# 限流配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000
